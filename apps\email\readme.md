## APP 说明

> 通过 smtp 发送邮件

## 动作列表

### 邮件发送

**参数：**

|  参数   | 类型  |  必填   |  备注  |
|  ----  | ----  |  ----  |  ----  |
| **host**  | text | `是` | SMTP 服务器 |
| **port**  | number | `是` | 端口 |
| **user**  | text | `是` | 登录用户|
| **passwd**  | text | `是` | 登录密码，目前很多提供商是填写授权码|
| **encrypt**  | select | `是` | 加密协议|
| **sender**  | text | `是` | 发送人，一般和 {{user}} 一致 |
| **to**  | text | `是` | 接收人，多个英文逗号|
| **title**  | text | `是` | 邮件标题|
| **type**  | text | `是` | 邮件类型，纯文本 或 HTML|
| **text**  | text | `是` | 邮件内容|

**返回值：**

```
发送成功
```