#!/usr/bin/env python
# encoding:utf-8
# cython: language_level=3
from loguru import logger
import requests
import cv2
import numpy as np
import pytesseract
from PIL import Image
import os
import json
import base64
from cryptography.hazmat.primitives import serialization
from cryptography.hazmat.primitives.asymmetric import padding
from cryptography.hazmat.backends import default_backend
import io
import time
import asyncio
import re

class H3CBlacklistManager:
    def __init__(self, host="**************", port="80"):
        """初始化H3C黑名单管理器"""
        self.base_url = f"http://{host}:{port}"
        self.session = requests.Session()
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/121.0.6167.85 Safari/537.36'
        }
        logger.info("[H3C] 初始化管理器: {url}", url=self.base_url)
        pytesseract.pytesseract.tesseract_cmd = r'/usr/bin/tesseract'
        # RSA公钥
        self.pubkey = """-----BEGIN PUBLIC KEY-----
MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQC3//sR2tXw0wrC2DySx8vNGlqt
3Y7ldU9+LBLI6e1KS5lfc5jlTGF7KBTSkCHBM3ouEHWqp1ZJ85iJe59aF5gIB2kl
Bd6h4wrbbHA2XE1sq21ykja/Gqx7/IRia3zQfxGv/qEkyGOx+XALVoOlZqDwh76o
2n1vP1D+tD3amHsK7QIDAQAB
-----END PUBLIC KEY-----"""

    def _get_captcha(self):
        """获取验证码图片并识别"""
        try:
            captcha_url = f"{self.base_url}/php/common/checknum_creat.php?module=config_authnum"
            response = self.session.get(captcha_url)
            if response.status_code != 200:
                return None

            # 将图片数据转换为OpenCV格式
            image_array = np.frombuffer(response.content, np.uint8)
            img = cv2.imdecode(image_array, cv2.IMREAD_COLOR)

            # 图像预处理
            gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
            # 固定阈值二值化
            ret, binary = cv2.threshold(gray, 127, 255, cv2.THRESH_BINARY)

            # 使用PIL进行OCR识别
            pil_img = Image.fromarray(binary)
            captcha_text = pytesseract.image_to_string(pil_img).strip()

            # 验证码应该是4个字符
            if len(captcha_text) != 4:
                fail_path = f"/tmp/h3c_captcha_fail_{int(time.time())}.png"
                pil_img.save(fail_path)
                logger.warning("[H3C] 验证码识别失败，已保存图片: {path},识别结果为：{text}", path=fail_path, text=captcha_text)
                return None

            logger.info("[H3C] 验证码识别结果: {text}", text=captcha_text)
            return captcha_text

        except Exception as e:
            logger.error("[H3C] 获取验证码失败: {e}", e=e)
            return None

    def _encrypt_password(self, password):
        """使用RSA加密密码"""
        try:
            # 加载RSA公钥
            public_key = serialization.load_pem_public_key(
                self.pubkey.encode('utf-8'),
                backend=default_backend()
            )
            
            # 使用RSA加密
            encrypted = public_key.encrypt(
                password.encode('utf-8'),
                padding.PKCS1v15()
            )
            
            # 转换为base64编码
            encrypted_b64 = base64.b64encode(encrypted).decode('utf-8')
            return encrypted_b64
            
        except Exception as e:
            logger.error("[H3C] 密码加密失败: {e}", e=e)
            return None

    def _login(self, username, password):
        """登录H3C系统"""
        try:
            # 首先访问登录页面
            login_page_url = f"{self.base_url}/login.html"
            logger.info("[H3C] 访问登录页面: {url}", url=login_page_url)
            response = self.session.get(login_page_url)
            logger.debug("[H3C] 登录页面响应状态码: {status}", status=response.status_code)
            
            # 加密密码
            encrypted_pwd = self._encrypt_password(password)
            if not encrypted_pwd:
                return False, "密码加密失败"
            
            # 获取验证码
            captcha = self._get_captcha()
            if not captcha:
                return False, "验证码获取失败"

            # 构建登录数据
            login_data = {
                'user': username,
                'pwd': encrypted_pwd,
                'vldcode': captcha,
                'lang': 'cn',
                'encrypt': '1'
            }
            logger.debug("[H3C] 登录请求数据: {data}", data=login_data)

            # 设置请求头
            headers = {
                'Referer': f'{self.base_url}/login.html',
                'Content-Type': 'application/x-www-form-urlencoded',
                'Accept': 'application/json, text/javascript, */*; q=0.01',
                'X-Requested-With': 'XMLHttpRequest'
            }

            # 发送登录请求
            login_url = f"{self.base_url}/login_auth.php"
            logger.info("[H3C] 发送登录请求: {url}", url=login_url)
            response = self.session.post(login_url, headers=headers, data=login_data)
            logger.debug("[H3C] 登录响应状态码: {status}", status=response.status_code)
            logger.debug("[H3C] 登录原始响应内容: {content}", content=response.text)
            
            if response.status_code == 200:
                # 去除响应中的while(1);前缀并解析JSON
                json_text = response.text.replace('while(1);', '')
                try:
                    response_json = json.loads(json_text)
                    
                    # 检查是否登录成功（返回url和code=1）
                    if response_json.get('url') and response_json.get('code') == "1":
                        logger.info("[H3C] 登录成功")
                        return True, "登录成功"
                    
                    # 检查错误消息
                    msg = response_json.get('msg', '')
                    logger.debug("[H3C] 登录响应消息(解码后): {msg}", msg=msg)
                    
                    if "验证码错误" in msg or "超时" in msg:
                        logger.warning("[H3C] 验证码错误或超时")
                        return False, msg
                    elif msg:
                        logger.error("[H3C] 登录失败: {msg}", msg=msg)
                        return False, msg
                    else:
                        logger.error("[H3C] 登录失败: 未知错误")
                        return False, "登录失败：未知错误"
                        
                except json.JSONDecodeError as e:
                    logger.error("[H3C] 解析登录响应JSON失败: {e}", e=e)
                    return False, "登录响应格式错误"
            else:
                return False, "登录请求失败"

        except Exception as e:
            logger.error("[H3C] 登录失败: {e}", e=e)
            return False, str(e)

    def _get_csrf_token(self):
        """获取添加黑名单页面中的CSRF token"""
        try:
            add_page_url = f"{self.base_url}/webui/?g=sec_ad_blacklist_add"
            response = self.session.get(add_page_url)
            if response.status_code != 200:
                logger.error("[H3C] 获取添加黑名单页面失败，状态码: {status}", status=response.status_code)
                return None
            # 用正则提取token
            match = re.search(r'<input[^>]+name="token"[^>]+value="([a-fA-F0-9]+)"', response.text)
            if match:
                token = match.group(1)
                logger.info("[H3C] 提取到CSRF token: {token}", token=token)
                return token
            else:
                logger.error("[H3C] 未能在页面中找到token字段")
                return None
        except Exception as e:
            logger.error("[H3C] 获取CSRF token失败: {e}", e=e)
            return None

    def _add_blacklist(self, ip, age=300):
        """添加IP到黑名单"""
        max_retries = 3  # 最大重试次数
        retry_delay = 3  # 重试间隔（秒）
        
        for attempt in range(max_retries):
            try:
                # 先获取CSRF token
                token = self._get_csrf_token()
                if not token:
                    return False, "获取CSRF token失败"
                logger.debug("[H3C] 当前CSRF Token: {token}", token=token)

                # 构建添加黑名单的数据
                blacklist_data = {
                    'enable': '1',
                    'src_ip': ip,
                    'age': str(age),
                    'custom': '0',
                    'submit_post': 'sec_ad_blacklist_addsave',
                    'token': token
                }
                logger.debug("[H3C] 黑名单添加请求数据: {data}", data=blacklist_data)

                # 设置请求头
                headers = {
                    'Referer': f'{self.base_url}/webui/?g=sec_ad_blacklist_add',
                    'Content-Type': 'application/x-www-form-urlencoded',
                    'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,*/*;q=0.8'
                }

                # 发送添加黑名单请求
                add_url = f"{self.base_url}/webui/?g=sec_ad_blacklist_add"
                logger.info("[H3C] 发送添加黑名单请求: {url}", url=add_url)
                response = self.session.post(add_url, headers=headers, data=blacklist_data)
                logger.debug("[H3C] 添加黑名单响应状态码: {status}", status=response.status_code)
                logger.debug("[H3C] 添加黑名单响应内容: {content}", content=response.text)
                
                if response.status_code == 200:
                    # 检查是否是频繁操作的提示
                    if "当前请求任务执行中，请勿频繁操作" in response.text:
                        if attempt < max_retries - 1:  # 如果不是最后一次尝试
                            logger.warning("[H3C] 检测到频繁操作，等待{delay}秒后重试", delay=retry_delay)
                            time.sleep(retry_delay)
                            continue
                        else:
                            return False, "系统繁忙，请稍后重试"
                    
                    # 检查是否操作成功
                    if "操作成功" in response.text:
                        logger.info("[H3C] IP {ip} 添加成功", ip=ip)
                        return True, "IP添加成功"
                    else:
                        logger.warning("[H3C] IP {ip} 添加失败，响应内容中未包含'操作成功'", ip=ip)
                        return False, "IP添加失败，未收到成功确认"
                else:
                    logger.error("[H3C] 添加黑名单请求失败，状态码: {status}", status=response.status_code)
                    return False, "添加黑名单请求失败"

            except Exception as e:
                logger.error("[H3C] 添加黑名单失败: {e}", e=e)
                if attempt < max_retries - 1:  # 如果不是最后一次尝试
                    logger.warning("[H3C] 将在{delay}秒后重试", delay=retry_delay)
                    time.sleep(retry_delay)
                    continue
                return False, str(e)
        
        return False, "添加黑名单失败，已达到最大重试次数"

async def add_blacklist(ip, age=300):
    """
    添加IP到H3C黑名单的异步函数

    Args:
        ip (str): 要添加到黑名单的IP地址
        age (int, optional): IP在黑名单中的存活时间(秒)。默认为300秒。

    Returns:
        dict: 包含操作状态和结果的字典
    """
    logger.info("[H3C] APP执行参数为: {ip} {age}", ip=ip, age=age)
    
    try:
        # 从环境变量获取认证信息
        username = os.getenv('H3C_USERNAME', 'admin')
        password = os.getenv('H3C_PASSWORD', 'admin1!@#EmR')

        # 创建H3C黑名单管理器实例
        manager = H3CBlacklistManager()

        # 尝试登录，最多重试5次
        max_attempts = 5
        for attempt in range(max_attempts):
            login_success, login_msg = manager._login(username, password)
            if login_success:
                break
            if attempt < max_attempts - 1:
                logger.warning(f"[H3C] 登录尝试 {attempt + 1} 失败，2秒后重试")
                time.sleep(2)
            else:
                return {"status": 2, "result": f"登录失败（已重试{max_attempts}次）: {login_msg}"}

        # 添加黑名单
        add_success, add_msg = manager._add_blacklist(ip, age)
        if not add_success:
            return {"status": 2, "result": f"添加黑名单失败: {add_msg}"}

        return {"status": 0, "result": f"IP {ip} 已成功添加到黑名单"}

    except Exception as e:
        logger.error("[H3C] 操作失败: {e}", e=e)
        return {"status": 2, "result": f"操作失败: {str(e)}"} 
