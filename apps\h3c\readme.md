## APP 说明

> 自动登录H3C系统并添加IP到黑名单，支持验证码自动识别

## 动作列表

### 添加黑名单

**参数：**

|  参数   | 类型  |  必填   |  备注  |
|  ----  | ----  |  ----  |  ----  |
| **ip**  | text | `是` | 要添加到黑名单的IP地址 |
| **age**  | number | `否` | IP在黑名单中的存活时间(秒)，默认300秒 |

**返回值：**

```
IP添加到黑名单的结果信息
```

**注意事项：**

1. 使用前需要设置以下环境变量：
   - H3C_USERNAME：登录用户名，默认为admin
   - H3C_PASSWORD：登录密码，默认为admin1!@#EmR
2. 需要安装以下依赖：
   - opencv-python
   - pytesseract
   - cryptography
   - requests
3. 需要在系统中安装Tesseract-OCR：
   ```bash
   apt-get update
   apt-get install tesseract-ocr
   ```

4. 登录失败时会自动重试5次，每次间隔2秒 