#!/usr/bin/env python
# encoding:utf-8
# cython: language_level=3
from loguru import logger
import json
import requests
import os
import sys

# 添加项目根目录到Python路径，以便导入core模块
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.join(current_dir, '..', '..', '..')
sys.path.insert(0, project_root)

try:
    # 导入核心模块
    from core.model import Users
    from core import redis
    from core.utils.randoms import Random
    from core.utils.times import Time
except ImportError as e:
    logger.error("[消息通知] 导入核心模块失败: {}", str(e))
    # 如果导入失败，使用备用方案
    Users = None
    redis = None
    Random = None
    Time = None


def get_system_token():
    """
    获取系统TOKEN
    1. 从数据库中获取ID为1的用户的token
    2. 验证token在Redis中是否有效
    3. 如果有效则使用，如果失效则重新生成token
    """
    try:
        # 检查模块是否导入成功
        if Users is None or redis is None:
            logger.warning("[消息通知] 核心模块未导入，使用备用TOKEN")
            return os.getenv('FALLBACK_SYSTEM_TOKEN', 'W5_TOKEN_FALLBACK')

        # 1. 从数据库获取ID为1的用户的token
        user = Users.where('id', 1).first()
        if not user:
            logger.error("[消息通知] 未找到ID为1的用户")
            return os.getenv('FALLBACK_SYSTEM_TOKEN', 'W5_TOKEN_FALLBACK')

        token = user.token
        if not token:
            logger.info("[消息通知] 用户token为空，生成新token")
            return generate_new_token(user)

        logger.info("[消息通知] 从数据库获取到TOKEN: {}...", token[:20] if len(token) > 20 else token)

        # 2. 验证token在Redis中是否有效
        if verify_token_in_redis(token):
            logger.info("[消息通知] TOKEN验证有效，直接使用")
            return token
        else:
            logger.info("[消息通知] TOKEN已失效，重新生成")
            return generate_new_token(user)

    except Exception as e:
        logger.error("[消息通知] 获取系统TOKEN失败: {}", str(e))
        return os.getenv('FALLBACK_SYSTEM_TOKEN', 'W5_TOKEN_FALLBACK')


def verify_token_in_redis(token):
    """验证TOKEN在Redis中是否有效"""
    try:
        if redis is None:
            logger.error("[消息通知] Redis模块未导入")
            return False

        # 检查token是否存在
        user_id = redis.get(token)
        if user_id:
            logger.info("[消息通知] TOKEN在Redis中有效，用户ID: {}", user_id)
            return True
        else:
            logger.info("[消息通知] TOKEN在Redis中不存在或已过期")
            return False
    except Exception as e:
        logger.error("[消息通知] Redis TOKEN验证失败: {}", str(e))
        return False


def generate_new_token(user):
    """生成新的TOKEN并更新到数据库和Redis"""
    try:
        if Random is None:
            logger.error("[消息通知] Random模块未导入，无法生成TOKEN")
            return os.getenv('FALLBACK_SYSTEM_TOKEN', 'W5_TOKEN_FALLBACK')

        # 生成新TOKEN
        token = "W5_TOKEN_" + Random.make_token(string=user.account)

        # 存储到Redis（7天过期）
        if redis:
            redis.set(token, str(user.id), ex=60 * 60 * 24 * 7)
            logger.info("[消息通知] 新TOKEN已存储到Redis")

        # 更新数据库中的TOKEN
        if Time is not None:
            update_time = Time.get_date_time()
        else:
            from datetime import datetime
            update_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')

        Users.where('id', user.id).update({
            "token": token,
            "update_time": update_time
        })
        logger.info("[消息通知] 数据库中的TOKEN已更新")

        return token

    except Exception as e:
        logger.error("[消息通知] 生成新TOKEN失败: {}", str(e))
        return os.getenv('FALLBACK_SYSTEM_TOKEN', 'W5_TOKEN_FALLBACK')


async def process_message(script_type, result_data, execution_id=""):
    """
    处理消息通知
    
    Args:
        script_type: 剧本类型
        result_data: 业务APP的JSON返回结果（字符串格式）
        execution_id: 剧本执行ID（可选）
    
    Returns:
        dict: 处理结果
    """
    logger.info("[消息通知] APP执行参数为: script_type={}, result_data={}, execution_id={}", 
                script_type=script_type, result_data=result_data, execution_id=execution_id)
    
    try:
        # 解析JSON数据
        try:
            result_json = json.loads(result_data) if isinstance(result_data, str) else result_data
        except json.JSONDecodeError as e:
            logger.error("[消息通知] JSON解析失败: {}", str(e))
            return {"status": 1, "result": f"JSON解析失败: {str(e)}"}
        
        # 构造请求数据
        request_data = {
            "script_type": script_type,
            "result": result_json
        }
        
        # 如果有执行ID，添加到请求中
        if execution_id:
            request_data["execution_id"] = execution_id
        
        # 获取系统TOKEN
        token = get_system_token()
        
        # 调用消息处理接口
        try:
            response = requests.post(
                "http://localhost:8888/api/v1/soar/notification/process",
                json=request_data,
                headers={
                    "Content-Type": "application/json",
                    "Authorization": f"Bearer {token}"
                },
                timeout=30
            )
            
            logger.info("[消息通知] 接口调用响应状态码: {}", response.status_code)
            logger.info("[消息通知] 接口调用响应内容: {}", response.text)
            
            if response.status_code == 200:
                response_data = response.json()
                
                # 检查业务逻辑是否成功
                if response_data.get("code") == 0:
                    data = response_data.get("data", {})
                    if data.get("need_notification", False):
                        logger.info("[消息通知] 通知处理成功，生成通知: {}", data.get("title", ""))
                        return {
                            "status": 0, 
                            "result": {
                                "message": "通知处理成功",
                                "notification_created": True,
                                "notification_info": data
                            }
                        }
                    else:
                        logger.info("[消息通知] 无需生成通知")
                        return {
                            "status": 0,
                            "result": {
                                "message": "无需生成通知",
                                "notification_created": False
                            }
                        }
                else:
                    error_msg = response_data.get("msg", "未知错误")
                    logger.error("[消息通知] 业务逻辑处理失败: {}", error_msg)
                    return {"status": 1, "result": f"业务逻辑处理失败: {error_msg}"}
            else:
                logger.error("[消息通知] HTTP请求失败，状态码: {}", response.status_code)
                return {"status": 1, "result": f"HTTP请求失败，状态码: {response.status_code}"}
                
        except requests.exceptions.Timeout:
            logger.error("[消息通知] 请求超时")
            return {"status": 1, "result": "请求超时"}
        except requests.exceptions.ConnectionError:
            logger.error("[消息通知] 连接失败")
            return {"status": 1, "result": "连接失败"}
        except requests.exceptions.RequestException as e:
            logger.error("[消息通知] 请求异常: {}", str(e))
            return {"status": 1, "result": f"请求异常: {str(e)}"}
            
    except Exception as e:
        logger.error("[消息通知] 处理异常: {}", str(e))
        return {"status": 1, "result": f"处理异常: {str(e)}"}
