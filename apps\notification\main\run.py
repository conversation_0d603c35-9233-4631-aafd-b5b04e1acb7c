#!/usr/bin/env python
# encoding:utf-8
# cython: language_level=3
from loguru import logger
import json
import requests
import os


def get_system_token():
    """获取系统TOKEN，这里需要根据实际情况实现"""
    # 可以从环境变量或配置文件中获取系统TOKEN
    # 或者使用系统账号登录获取TOKEN
    return os.getenv('SYSTEM_TOKEN', 'default_system_token')


async def process_message(script_type, result_data, execution_id=""):
    """
    处理消息通知
    
    Args:
        script_type: 剧本类型
        result_data: 业务APP的JSON返回结果（字符串格式）
        execution_id: 剧本执行ID（可选）
    
    Returns:
        dict: 处理结果
    """
    logger.info("[消息通知] APP执行参数为: script_type={}, result_data={}, execution_id={}", 
                script_type=script_type, result_data=result_data, execution_id=execution_id)
    
    try:
        # 解析JSON数据
        try:
            result_json = json.loads(result_data) if isinstance(result_data, str) else result_data
        except json.JSONDecodeError as e:
            logger.error("[消息通知] JSON解析失败: {}", str(e))
            return {"status": 1, "result": f"JSON解析失败: {str(e)}"}
        
        # 构造请求数据
        request_data = {
            "script_type": script_type,
            "result": result_json
        }
        
        # 如果有执行ID，添加到请求中
        if execution_id:
            request_data["execution_id"] = execution_id
        
        # 获取系统TOKEN
        token = get_system_token()
        
        # 调用消息处理接口
        try:
            response = requests.post(
                "http://localhost:8888/api/v1/soar/notification/process",
                json=request_data,
                headers={
                    "Content-Type": "application/json",
                    "Authorization": f"Bearer {token}"
                },
                timeout=30
            )
            
            logger.info("[消息通知] 接口调用响应状态码: {}", response.status_code)
            logger.info("[消息通知] 接口调用响应内容: {}", response.text)
            
            if response.status_code == 200:
                response_data = response.json()
                
                # 检查业务逻辑是否成功
                if response_data.get("code") == 0:
                    data = response_data.get("data", {})
                    if data.get("need_notification", False):
                        logger.info("[消息通知] 通知处理成功，生成通知: {}", data.get("title", ""))
                        return {
                            "status": 0, 
                            "result": {
                                "message": "通知处理成功",
                                "notification_created": True,
                                "notification_info": data
                            }
                        }
                    else:
                        logger.info("[消息通知] 无需生成通知")
                        return {
                            "status": 0,
                            "result": {
                                "message": "无需生成通知",
                                "notification_created": False
                            }
                        }
                else:
                    error_msg = response_data.get("msg", "未知错误")
                    logger.error("[消息通知] 业务逻辑处理失败: {}", error_msg)
                    return {"status": 1, "result": f"业务逻辑处理失败: {error_msg}"}
            else:
                logger.error("[消息通知] HTTP请求失败，状态码: {}", response.status_code)
                return {"status": 1, "result": f"HTTP请求失败，状态码: {response.status_code}"}
                
        except requests.exceptions.Timeout:
            logger.error("[消息通知] 请求超时")
            return {"status": 1, "result": "请求超时"}
        except requests.exceptions.ConnectionError:
            logger.error("[消息通知] 连接失败")
            return {"status": 1, "result": "连接失败"}
        except requests.exceptions.RequestException as e:
            logger.error("[消息通知] 请求异常: {}", str(e))
            return {"status": 1, "result": f"请求异常: {str(e)}"}
            
    except Exception as e:
        logger.error("[消息通知] 处理异常: {}", str(e))
        return {"status": 1, "result": f"处理异常: {str(e)}"}
