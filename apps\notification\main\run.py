#!/usr/bin/env python
# encoding:utf-8
# cython: language_level=3
from loguru import logger
import json
import requests
import os
import sys
import configparser

# 添加项目根目录到Python路径，以便导入core模块
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.join(current_dir, '..', '..', '..')
sys.path.insert(0, project_root)

try:
    # 导入数据库相关模块
    from core.utils.randoms import Random
    from core.utils.times import Time
    import redis
    import pymysql
except ImportError as e:
    logger.error("[消息通知] 导入模块失败: {}", str(e))
    # 如果导入失败，设置为None，后续会有错误处理
    Random = None
    Time = None
    redis = None
    pymysql = None


def get_db_config():
    """获取数据库配置"""
    try:
        config_path = os.path.join(project_root, 'config.ini')
        cf = configparser.RawConfigParser()
        cf.read(config_path)

        # 优先使用环境变量
        if os.getenv('MYSQL_HOST'):
            return {
                'host': os.getenv('MYSQL_HOST'),
                'port': int(os.getenv('MYSQL_PORT')),
                'database': os.getenv('MYSQL_DATABASE'),
                'user': os.getenv('MYSQL_USER'),
                'password': os.getenv('MYSQL_PASSWORD')
            }
        else:
            return {
                'host': cf.get("mysql", "host"),
                'port': int(cf.get("mysql", "port")),
                'database': cf.get("mysql", "database"),
                'user': cf.get("mysql", "user"),
                'password': cf.get("mysql", "password")
            }
    except Exception as e:
        logger.error("[消息通知] 获取数据库配置失败: {}", str(e))
        # 返回默认配置
        return {
            'host': 'localhost',
            'port': 3306,
            'database': 'w5_db',
            'user': 'root',
            'password': 'root'
        }


def get_redis_config():
    """获取Redis配置"""
    try:
        config_path = os.path.join(project_root, 'config.ini')
        cf = configparser.RawConfigParser()
        cf.read(config_path)

        # 优先使用环境变量
        if os.getenv('REDIS_HOST'):
            return {
                'host': os.getenv('REDIS_HOST'),
                'port': int(os.getenv('REDIS_PORT')),
                'database': int(os.getenv('REDIS_DATABASE')),
                'password': os.getenv('REDIS_PASSWORD')
            }
        else:
            return {
                'host': cf.get("redis", "host"),
                'port': int(cf.get("redis", "port")),
                'database': int(cf.get("redis", "database")),
                'password': cf.get("redis", "password")
            }
    except Exception as e:
        logger.error("[消息通知] 获取Redis配置失败: {}", str(e))
        # 返回默认配置
        return {
            'host': 'localhost',
            'port': 6379,
            'database': 0,
            'password': ''
        }


def get_redis_connection():
    """获取Redis连接"""
    try:
        config = get_redis_config()
        if str(config['password']).strip() == "":
            r = redis.Redis(
                host=config['host'],
                port=config['port'],
                db=config['database'],
                decode_responses=True
            )
        else:
            r = redis.Redis(
                host=config['host'],
                port=config['port'],
                db=config['database'],
                password=config['password'],
                decode_responses=True
            )
        return r
    except Exception as e:
        logger.error("[消息通知] Redis连接失败: {}", str(e))
        return None


def get_db_connection():
    """获取数据库连接"""
    try:
        if pymysql is None:
            logger.error("[消息通知] pymysql模块未安装")
            return None

        config = get_db_config()
        return pymysql.connect(
            host=config['host'],
            port=config['port'],
            user=config['user'],
            password=config['password'],
            database=config['database'],
            charset='utf8mb4'
        )
    except Exception as e:
        logger.error("[消息通知] 数据库连接失败: {}", str(e))
        return None


def verify_token_in_redis(token):
    """验证TOKEN在Redis中是否有效"""
    try:
        if redis is None:
            logger.error("[消息通知] Redis模块未导入")
            return False

        r = get_redis_connection()
        if r is None:
            return False

        # 检查token是否存在
        user_id = r.get(token)
        if user_id:
            logger.info("[消息通知] TOKEN在Redis中有效，用户ID: {}", user_id)
            return True
        else:
            logger.info("[消息通知] TOKEN在Redis中不存在或已过期")
            return False
    except Exception as e:
        logger.error("[消息通知] Redis TOKEN验证失败: {}", str(e))
        return False


def login_and_get_token():
    """通过登录获取新的TOKEN"""
    try:
        # 从数据库获取ID为1的用户信息
        conn = get_db_connection()
        if conn is None:
            return None

        cursor = conn.cursor()
        cursor.execute("SELECT account, passwd FROM w5_users WHERE id = 1")
        user_data = cursor.fetchone()
        cursor.close()
        conn.close()

        if not user_data:
            logger.error("[消息通知] 未找到ID为1的用户")
            return None

        account, _ = user_data  # 忽略stored_password，因为我们不需要它
        logger.info("[消息通知] 找到系统用户: {}", account)

        # 这里需要原始密码来登录，但数据库中存储的是MD5加密后的密码
        # 由于无法从MD5反推原始密码，我们需要使用其他方式
        # 方案1：直接生成新的TOKEN并存储到Redis和数据库
        # 方案2：使用预设的系统账号密码

        # 使用方案1：直接生成TOKEN
        if Random is None:
            logger.error("[消息通知] Random模块未导入，无法生成TOKEN")
            return None

        token = "W5_TOKEN_" + Random.make_token(string=account)

        # 将TOKEN存储到Redis（7天过期）
        r = get_redis_connection()
        if r:
            r.set(token, "1", ex=60 * 60 * 24 * 7)
            logger.info("[消息通知] 新TOKEN已存储到Redis")

        # 更新数据库中的TOKEN
        conn = get_db_connection()
        if conn:
            cursor = conn.cursor()
            if Time is not None:
                update_time = Time.get_date_time()
            else:
                from datetime import datetime
                update_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')

            cursor.execute(
                "UPDATE w5_users SET token = %s, update_time = %s WHERE id = 1",
                (token, update_time)
            )
            conn.commit()
            cursor.close()
            conn.close()
            logger.info("[消息通知] 数据库中的TOKEN已更新")

        return token

    except Exception as e:
        logger.error("[消息通知] 登录获取TOKEN失败: {}", str(e))
        return None


def get_system_token():
    """
    获取系统TOKEN
    1. 从数据库中获取ID为1的用户的token
    2. 验证token在Redis中是否有效
    3. 如果有效则使用，如果失效则重新生成token
    """
    try:
        # 检查必要的模块是否导入成功
        if pymysql is None:
            logger.error("[消息通知] pymysql模块未导入，无法获取TOKEN")
            return get_fallback_token()

        # 1. 从数据库获取ID为1的用户的token
        conn = get_db_connection()
        if conn is None:
            logger.error("[消息通知] 无法连接数据库")
            return get_fallback_token()

        cursor = conn.cursor()
        cursor.execute("SELECT token FROM w5_users WHERE id = 1")
        result = cursor.fetchone()
        cursor.close()
        conn.close()

        if not result or not result[0]:
            logger.info("[消息通知] 数据库中未找到有效TOKEN，尝试生成新TOKEN")
            return login_and_get_token()

        token = result[0]
        logger.info("[消息通知] 从数据库获取到TOKEN: {}...", token[:20] if len(token) > 20 else token)

        # 2. 验证token在Redis中是否有效
        if verify_token_in_redis(token):
            logger.info("[消息通知] TOKEN验证有效，直接使用")
            return token
        else:
            logger.info("[消息通知] TOKEN已失效，重新生成")
            return login_and_get_token()

    except Exception as e:
        logger.error("[消息通知] 获取系统TOKEN失败: {}", str(e))
        return get_fallback_token()


def get_fallback_token():
    """备用TOKEN获取方案"""
    logger.warning("[消息通知] 使用备用TOKEN方案")
    # 这里可以返回一个预设的TOKEN或者从环境变量获取
    fallback_token = os.getenv('FALLBACK_SYSTEM_TOKEN', 'W5_TOKEN_FALLBACK')
    logger.info("[消息通知] 使用备用TOKEN: {}...", fallback_token[:20] if len(fallback_token) > 20 else fallback_token)
    return fallback_token


async def process_message(script_type, result_data, execution_id=""):
    """
    处理消息通知
    
    Args:
        script_type: 剧本类型
        result_data: 业务APP的JSON返回结果（字符串格式）
        execution_id: 剧本执行ID（可选）
    
    Returns:
        dict: 处理结果
    """
    logger.info("[消息通知] APP执行参数为: script_type={}, result_data={}, execution_id={}", 
                script_type=script_type, result_data=result_data, execution_id=execution_id)
    
    try:
        # 解析JSON数据
        try:
            result_json = json.loads(result_data) if isinstance(result_data, str) else result_data
        except json.JSONDecodeError as e:
            logger.error("[消息通知] JSON解析失败: {}", str(e))
            return {"status": 1, "result": f"JSON解析失败: {str(e)}"}
        
        # 构造请求数据
        request_data = {
            "script_type": script_type,
            "result": result_json
        }
        
        # 如果有执行ID，添加到请求中
        if execution_id:
            request_data["execution_id"] = execution_id
        
        # 获取系统TOKEN
        token = get_system_token()
        
        # 调用消息处理接口
        try:
            response = requests.post(
                "http://localhost:8888/api/v1/soar/notification/process",
                json=request_data,
                headers={
                    "Content-Type": "application/json",
                    "Authorization": f"Bearer {token}"
                },
                timeout=30
            )
            
            logger.info("[消息通知] 接口调用响应状态码: {}", response.status_code)
            logger.info("[消息通知] 接口调用响应内容: {}", response.text)
            
            if response.status_code == 200:
                response_data = response.json()
                
                # 检查业务逻辑是否成功
                if response_data.get("code") == 0:
                    data = response_data.get("data", {})
                    if data.get("need_notification", False):
                        logger.info("[消息通知] 通知处理成功，生成通知: {}", data.get("title", ""))
                        return {
                            "status": 0, 
                            "result": {
                                "message": "通知处理成功",
                                "notification_created": True,
                                "notification_info": data
                            }
                        }
                    else:
                        logger.info("[消息通知] 无需生成通知")
                        return {
                            "status": 0,
                            "result": {
                                "message": "无需生成通知",
                                "notification_created": False
                            }
                        }
                else:
                    error_msg = response_data.get("msg", "未知错误")
                    logger.error("[消息通知] 业务逻辑处理失败: {}", error_msg)
                    return {"status": 1, "result": f"业务逻辑处理失败: {error_msg}"}
            else:
                logger.error("[消息通知] HTTP请求失败，状态码: {}", response.status_code)
                return {"status": 1, "result": f"HTTP请求失败，状态码: {response.status_code}"}
                
        except requests.exceptions.Timeout:
            logger.error("[消息通知] 请求超时")
            return {"status": 1, "result": "请求超时"}
        except requests.exceptions.ConnectionError:
            logger.error("[消息通知] 连接失败")
            return {"status": 1, "result": "连接失败"}
        except requests.exceptions.RequestException as e:
            logger.error("[消息通知] 请求异常: {}", str(e))
            return {"status": 1, "result": f"请求异常: {str(e)}"}
            
    except Exception as e:
        logger.error("[消息通知] 处理异常: {}", str(e))
        return {"status": 1, "result": f"处理异常: {str(e)}"}
