# 消息通知处理APP

## 功能描述
处理剧本执行结果并发送通知的APP，用于在业务剧本执行完成后自动判断是否需要生成通知。

## 使用方法

### 输入参数
- `script_type`: 剧本类型，支持以下值：
  - `s60000_pending`: S60000待处理剧本
  - `email`: 邮件剧本
  - `qianxin_ids`: 奇安信天眼和IDS剧本
  - `nsfocus_scan`: 绿盟漏扫剧本
  - `s60000_feedback`: S60000反馈截止剧本

- `result_data`: 业务APP的JSON返回结果（字符串格式）

- `execution_id`: 剧本执行ID（可选）

### 输出结果
```json
{
  "status": 0,  // 0表示成功，1表示失败
  "result": {
    "message": "通知处理成功",
    "notification_created": true,
    "notification_info": {
      "notification_id": "uuid",
      "title": "通知标题",
      "source": "来源系统",
      "count": 10
    }
  }
}
```

## 判断逻辑

### S60000待处理剧本 (s60000_pending)
- 判断字段: `processed_count`
- 条件: `processed_count != 0`
- 通知类型: "待处理数"

### 邮件剧本 (email)
- 判断字段: `mails_count`
- 条件: `mails_count != 0`
- 通知类型: "未读邮件"

### 奇安信天眼和IDS剧本 (qianxin_ids)
- 判断字段: `total`
- 条件: `total != 0`
- 通知类型: "危急告警"

### 绿盟漏扫剧本 (nsfocus_scan)
- 判断字段: `total_tasks` 和 `total_vulns`
- 条件: `total_tasks != 0` 或 `total_vulns != 0`
- 通知类型: "扫描任务" 或 "安全漏洞"

### S60000反馈截止剧本 (s60000_feedback)
- 判断字段: `ddl_entry_count`
- 条件: `ddl_entry_count != 0`
- 通知类型: "反馈截止"

## 配置说明

### 环境变量
- `SYSTEM_TOKEN`: 系统TOKEN，用于调用通知处理接口

## 注意事项
1. 确保通知处理接口服务正常运行
2. 确保网络连接正常
3. 输入的JSON数据格式必须正确
4. 系统TOKEN必须有效
