## APP 说明

> Redis 操作客户端

### 通用参数

|  参数   | 类型  |  必填   |  备注  |
|  ----  | ----  |  ----  |  ----  |
| **host**  | text | `是` | Redis 主机 |
| **port**  | number | `是` | 端口 |
| **db**  | number | `是` | Redis DB |
| **password**  | text | `否` | Redis 密码 |


## 动作列表

### GET

**参数：**

|  参数   | 类型  |  必填   |  备注  |
|  ----  | ----  |  ----  |  ----  |
| **key**  | text | `是` | 获取某个KEY的数据 |

**返回值：**

```
有数据直接返回数据，没有返回 None
```

### SET

**参数：**

|  参数   | 类型  |  必填   |  备注  |
|  ----  | ----  |  ----  |  ----  |
| **key**  | text | `是` | 写入某个 KEY |
| **value**  | text | `是` | 写入这个 KEY 的内容 |

**返回值：**

```
成功返回 True，失败 False
```

### DEL

**参数：**

|  参数   | 类型  |  必填   |  备注  |
|  ----  | ----  |  ----  |  ----  |
| **key**  | text | `是` | 删除这个 KEY |

**返回值：**

```
有数据返回条数，没有返回 0
```

### 清空数据-单个DB

> **危险动作，请谨慎操作**

**返回值：**

```
成功返回 True，失败 False
```

### 清空数据-全部DB

> **危险动作，请谨慎操作**

**返回值：**

```
成功返回 True，失败 False
```

