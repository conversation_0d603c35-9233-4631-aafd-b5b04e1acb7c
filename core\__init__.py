# !/usr/bin/env python
# encoding:utf-8
import os
import sys
import signal
import configparser
from flask_cors import CORS
from flask_orator import Orator
from flask_redis import FlaskRedis
from flask_sockets import Sockets
from core.view_soar import DecoratorSoar
from core.view_alert import DecoratorAlert
from core.view_decision import DecoratorDecision
from flask import (Flask, send_from_directory)
from flasgger import Swagger
import requests
from flask import request, Response
version = "0.6.3"

db = Orator()
redis = FlaskRedis()
sockets = Sockets()

lose_time = None
max_instances = None
w5_apps_path = None


def init_proxy_route(app):
    # 代理所有 /bigdata 开头的请求到 Java 后端（假设运行在 8080 端口）
    @app.route('/api/v1/bigdata/<path:subpath>', methods=['GET', 'POST', 'PUT', 'DELETE'])
    def proxy_to_java(subpath):
        # 构建目标 URL
        java_url = f'http://localhost:8080/{subpath}'
        
        # 转发请求头和参数
        headers = {key: value for (key, value) in request.headers if key != 'Host'}
        resp = requests.request(
            method=request.method,
            url=java_url,
            headers=headers,
            data=request.get_data(),
            cookies=request.cookies,
            allow_redirects=False
        )
        
        # 返回 Java 响应
        excluded_headers = ['content-encoding', 'content-length', 'transfer-encoding', 'connection']
        headers = [(name, value) for (name, value) in resp.raw.headers.items() 
                   if name.lower() not in excluded_headers]
        
        return Response(resp.content, resp.status_code, headers)


def init_route(app):
    # 注册一组soar的路由
    from core.view_soar.login import r as r_login
    from core.view_soar.user import r as r_user
    from core.view_soar.type import r as r_type
    from core.view_soar.variablen import r as r_variablen
    from core.view_soar.system import r as r_system
    from core.view_soar.apps import r as r_apps
    from core.view_soar.workflow import r as r_workflow
    from core.view_soar.logs import r as r_logs
    from core.view_soar.dashboard import r as r_dashboard
    from core.view_soar.api import r as r_api
    from core.view_soar.report import r as r_report
    from core.view_soar.timer import r as r_timer
    from core.view_soar.workflow import ws as ws_workflow
    from core.view_soar.audit import r as r_audit
    from core.view_soar.alert import r as r_alert
    from core.view_soar.analysis import r as r_analysis
    from core.view_soar.rectification import r as r_rectification

    soar_route_list = [r_login, r_user, r_type, r_variablen, r_system, r_apps, r_workflow, r_logs, r_dashboard, r_api,
                  r_report, r_timer, r_audit, r_alert, r_analysis, r_rectification]

    for route in soar_route_list:
        app.register_blueprint(route, url_prefix="/api/v1/soar")

    sockets.register_blueprint(ws_workflow, url_prefix=r'/')

    # 注册第二组alert的路由
    # 例如：from core.view_alert import r as r_alert
    # from core.view_alert.alert_test import r as r_test
    from core.view_alert.alert_rule import r as r_rule
    from core.view_alert.alert_events import r as r_events
    from core.view_alert.alert_Intelligence import r as r_intelligence
    # alert_route_list = [r_rule, r_events, r_intelligence]
    from core.view_alert.alert_data_overview import r as r_data_overview
    # alert_route_list = [r_rule, r_events, r_data_overview, r_intelligence]
    from core.view_alert.data_analysis import r as r_data_analysis
    from core.view_alert.log import r as r_log
    from core.view_alert.alert_sql import r as r_sql
    alert_route_list = [r_rule, r_events, r_intelligence, r_data_analysis, r_log, r_data_overview, r_sql]
    
    for route in alert_route_list:
        app.register_blueprint(route, url_prefix="/api/v1/alert")
    
    # 注册第三组decision的路由
    # 注册第三组decision的路由
    # 三个跨组核心API和一个测试用练手API
    from core.view_decision.decision_test        import r as r_test
    from core.view_decision.ids_info             import r as r_get_idsinfo
    from core.view_decision.update_status        import r as r_update_status
    from core.view_decision.response_suggestions import r as r_response_suggestions
    # 数据集管理的注册
    from core.view_decision.data_getall    import r as dataset_getall
    from core.view_decision.data_getone    import r as dataset_getone
    from core.view_decision.data_create    import r as dataset_create
    from core.view_decision.data_delete    import r as dataset_delete
    from core.view_decision.data_upload    import r as dataset_upload
    from core.view_decision.data_uprecords import r as dataset_uprecords
    from core.view_decision.data_geturls   import r as dataset_geturls
    from core.view_decision.data_logs      import r as dataset_getlogs
    from core.view_decision.data_chstatus  import r as dataset_chstatus
    from core.view_decision.data_preview  import r as dataset_preview
    
    # 图知识库扩展功能的API
    from core.view_decision.kgraph_insert import r as kgraph_insert
    from core.view_decision.kgraph_delete import r as kgraph_delete
    from core.view_decision.kgraph_update import r as kgraph_update
    from core.view_decision.kgraph_get    import r as kgraph_get
    from core.view_decision.kgraph_export import r as kgraph_export
    from core.view_decision.kgraph_rebuild import r as kgraph_rebuild
    from core.view_decision.kgraph_visualization import r as kgraph_visualization
    from core.view_decision.kgraph_getUptime import r as kgraph_getUptime
    # 大屏的API
    from core.view_decision.screen_decisionCount import r as screen_decisionCount
    from core.view_decision.screen_updateTime import r as screen_updateTime
    from core.view_decision.screen_yesterdayCount import r as screen_yesterdayCount
    from core.view_decision.screen_realityCount import r as screen_realityCount
    from core.view_decision.screen_countall import r as screen_countall
    
    from core.view_decision.decision_num import r as r_num#姿态大屏大模型接口路由
    # 悬浮球决策的API
    from core.view_decision.hoverball_GraphDecision import r as hoverball_GraphDecision
    from core.view_decision.hoverball_DPDecision import r as hoverball_DPDecision
   # 全流程决策与翻译API
    from core.view_decision.alert_decision import r as alert_decision
    from core.view_decision.alert_parseaction import r as alert_parseaction
   #大模型API
    from core.view_decision.decision_chat import r as r_chat# 用户与大模型对话路由
    from core.view_decision.decision_create import r as r_create#创建新对话路由
    from core.view_decision.decision_delete import r as r_delete  #删除指定对话路由
    from core.view_decision.decision_clear import r as r_clear # 清除模型记忆路由
    from core.view_decision.decision_bug import r as r_bug # 提交报错功能路由
    from core.view_decision.decision_apikey import r as r_apikey  #生成APIKEY路由
    from core.view_decision.decision_hiskey import r as r_hiskey#查询历史APIKEY路由
    from core.view_decision.decision_apichat import r as r_apichat#API功能实现路由
    from core.view_decision.decision_deletekey import r as r_deletekey#清除已生成的APIKEY路由
    from core.view_decision.decision_userhis import r as r_userhis#lishijiluluyou
    from core.view_decision.decision_userse import r as r_userse#duihualiebiao
    decision_route_list = [r_test, r_get_idsinfo, r_update_status,r_response_suggestions, 
                           dataset_getall,dataset_getone,dataset_create,dataset_delete,dataset_upload,dataset_uprecords,dataset_geturls,dataset_getlogs,dataset_chstatus,dataset_preview,
                           kgraph_insert,kgraph_delete,kgraph_update,kgraph_get,kgraph_export,kgraph_rebuild,kgraph_visualization,kgraph_getUptime,
                           screen_decisionCount, screen_updateTime, screen_yesterdayCount, screen_realityCount,screen_countall,
                           r_chat,r_create,r_delete,r_clear,r_bug,r_apikey,r_hiskey,r_apichat,r_num,r_deletekey,r_userhis,r_userse,
                           hoverball_GraphDecision,hoverball_DPDecision,
                           alert_decision,alert_parseaction]

    for route in decision_route_list:
        app.register_blueprint(route, url_prefix="/api/v1/decision")

def init_web(app):
    @app.route('/')
    def index():
        return app.send_static_file("index.html")

    @app.route('/<path:file>')
    def route(file):
        return app.send_static_file(file)


def init_apps(app):
    @app.route('/app/<path:file>')
    def app_icon(file):
        file_arr = str(file).split("/")

        file_name = file_arr[1]

        file_path = app.config['apps_path'] + "/" + file_arr[0]

        return send_from_directory(directory=file_path, filename=file_name)


def init_public(app):
    @app.route('/public/<path:file_name>')
    def public(file_name):
        return send_from_directory(directory=app.config['public_path'], filename=file_name)


def init_config(app):
    app.config['project_path'] = os.getcwd()
    app.config['tmp_path'] = app.config['project_path'] + "/tmp"
    app.config['apps_path'] = app.config['project_path'] + "/apps"
    app.config['web_path'] = app.config['project_path'] + "/core/web"
    app.config['public_path'] = app.config['project_path'] + "/core/public"
    app.config['update_path'] = "https://s.w5soar.com"

    cf = configparser.RawConfigParser()
    cf.read(app.config['project_path'] + '/config.ini')

    global lose_time, max_instances, w5_apps_path
    lose_time = cf.get("setting", "lose_time")
    max_instances = cf.get("setting", "max_instances")
    w5_apps_path = app.config['apps_path']

    if os.getenv('MYSQL_HOST'):
        app.config['ORATOR_DATABASES'] = {
            'development': {
                'driver': 'mysql',
                'host': os.getenv('MYSQL_HOST'),
                'port': int(os.getenv('MYSQL_PORT')),
                'database': os.getenv('MYSQL_DATABASE'),
                'user': os.getenv('MYSQL_USER'),
                'password': os.getenv('MYSQL_PASSWORD')
            }
        }
    else:
        app.config['ORATOR_DATABASES'] = {
            'development': {
                'driver': 'mysql',
                'host': cf.get("mysql", "host"),
                'port': int(cf.get("mysql", "port")),
                'database': cf.get("mysql", "database"),
                'user': cf.get("mysql", "user"),
                'password': cf.get("mysql", "password")
            }
        }

    if os.getenv('REDIS_HOST'):
        redis_host = os.getenv('REDIS_HOST')
        redis_port = os.getenv('REDIS_PORT')
        redis_database = os.getenv('REDIS_DATABASE')
        redis_password = os.getenv('REDIS_PASSWORD')
    else:
        redis_host = cf.get("redis", "host")
        redis_port = cf.get("redis", "port")
        redis_database = cf.get("redis", "database")
        redis_password = cf.get("redis", "password")

    if str(redis_password) == "":
        app.config['REDIS_URL'] = "redis://{host}:{port}/{db}".format(
            host=redis_host,
            port=redis_port,
            db=redis_database
        )
    else:
        app.config['REDIS_URL'] = "redis://:{password}@{host}:{port}/{db}".format(
            password=redis_password,
            host=redis_host,
            port=redis_port,
            db=redis_database
        )


def init_db(app):
    db.init_app(app=app)
    redis.init_app(app=app)


def init_cors(app):
    CORS(
        app=app,
        resources={
            r"/api/*": {"origins": "*"},
            r"/app/*": {"origins": "*"},
            r"/public/*": {"origins": "*"}
        }
    )


def init_decorator(app):
    soar_no_path = [
        "/api/v1/soar/login",
        "/api/v1/soar/webhook",
        "/api/v1/soar/get/workflow_exec",
        "/api/v1/soar/get/workflow_success_fail",
        "/api/v1/soar/get/workflow_logs",
        "/api/v1/soar/get/executing",
        "/api/v1/soar/post/workflow/import"
    ]

    DecoratorSoar(app=app, no_path=soar_no_path)
    
    # 此处设置alert的路由，no_path为不需要认证的路由
    alert_no_path = [
        "/api/v1/alert/test"
    ]
    
    DecoratorAlert(app=app, no_path=alert_no_path)
    
    # 此处设置decision的路由，no_path为不需要认证的路由
    decision_no_path = [
    	"/api/v1/decision/hoverball/GraphDecision",
        "/api/v1/decision/hoverball/DPDecision",
        "/api/v1/decision/decision_test",
        "/api/v1/decision/event/send",
        "/api/v1/decision/datasets/create",
        "/api/v1/decision/datasets/delete",
        "/api/v1/decision/datasets/upload",
        "api/v1/desicion/datasets/uprecords",
        "/api/v1/decision/datasets/update/status",
        "/api/v1/decision/kgraph/insert",
        "/api/v1/decision/kgraph/delete",
        "/api/v1/decision/kgraph/update",
        "/api/v1/decision/kgraph/get",
        "/api/v1/decision/kgraph/rebuild",
        "/api/v1/decision/kgraph/visualization",
        "/api/v1/decision/alert/decision",
        "/api/v1/decision/alert/parseaction",
        "/api/v1/decision/chat",#用户与大模型交互对话
        "/api/v1/decision/create",#用户创建新的对话
        "/api/v1/decision/delete",#用户删除指定对话
        "/api/v1/decision//memory/<user_id>",#清除历史记忆
        "/api/v1/decision/bug-report",#提交BUG信息 
        "/api/v1/decision/apikey",#生成专属APIKEY
        "/api/v1/decision/hisapikeys/<user_id>",#查询历史APIKEY
        "/api/v1/decision/apichat",#APIKEY验证与使用
        "/api/v1/decision/chat_num/<user_id>",#姿态大屏大模型APIKEY
        "/api/v1/decision/deletekey/<user_id>",#删除历史APIKEY
        "/api/v1/decision/userse/<user_id>",#查询用户对话列表
        "/api/v1/decision/userhis"#查询指定对话历史内容
    ]
    
    DecoratorDecision(app=app, no_path=decision_no_path)
    


def init_web_sockets(app):
    sockets.init_app(app=app)


def init_w5(app):
    from core.view_soar.system.view import init_key, init_timer, init_async
    init_key()
    init_timer()
    init_async()


def init_app():
    app = Flask(__name__, static_folder="web")
    swagger = Swagger(app)
    init_config(app=app)
    init_cors(app=app)
    init_proxy_route(app)
    init_db(app=app)
    init_web(app=app)
    init_public(app=app)
    init_apps(app=app)
    init_route(app=app)
    init_decorator(app=app)
    init_web_sockets(app=app)
    init_w5(app=app)

    app.app_context().push()

    return app


start = init_app()


def sign_out(signum, frame):
    try:
        redis.delete("manage_timer_lock")
    except:
        pass

    sys.exit()


signal.signal(signal.SIGINT, sign_out)
