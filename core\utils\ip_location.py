import geoip2.database
import os

# 设置数据库路径（也可以通过 config 管理）
GEOIP_DB_PATH = os.path.abspath("GeoLite2-City.mmdb")

# 初始化 reader
reader = geoip2.database.Reader(GEOIP_DB_PATH)
def get_ip_location(ip_str):
    try:
        response = reader.city(ip_str)
        
        # 优先使用中文名称（没有则用英文）
        country = response.country.names.get('zh-CN') or response.country.name or ''
        province = response.subdivisions.most_specific.names.get('zh-CN') or response.subdivisions.most_specific.name or ''
        city = response.city.names.get('zh-CN') or response.city.name or ''

        # 拼接为 “国家 省 市” 格式
        location_parts = [country, province, city]
        location = ' '.join([part for part in location_parts if part])  # 去掉空字符串
        return location or "未知"
    except Exception:
        return "未知"


# 只有国家版本
# def get_ip_location(ip_str):
#     try:
#         response = reader.city(ip_str)
#         country = response.country.names.get('zh-CN') or response.country.name
#         province = response.subdivisions.most_specific.names.get('zh-CN') or response.subdivisions.most_specific.name
#         city = response.city.names.get('zh-CN') or response.city.name
#         return f"{country or ''} {province or ''} {city or ''}".strip()
#     except Exception:
#         return "未知"
