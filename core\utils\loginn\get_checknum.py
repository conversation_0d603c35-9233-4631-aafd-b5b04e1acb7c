import base64
import requests
import time
from .config import user, password, ip, ocr_url
from ddddocr import DdddOcr  # 引入 ddddocr 库

# 动态生成 t 参数作为当前时间的时间戳（13 位）
current_timestamp = str(int(time.time() * 1000))

# 请求的 URL 和参数
url = f"http://{ip}/php/common/checknum_creat.php"
params = {
    'module': 'config_authnum',
    't': current_timestamp  # 使用动态生成的时间戳
}

headers = {
    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    'Accept': 'image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8',
    'Referer': f'http://{ip}/login.html',
    'Accept-Encoding': 'gzip, deflate',
    'Accept-Language': 'zh-CN,zh;q=0.9'
}

# 初始化 ddddocr
ocr = DdddOcr()  # 创建 ddddocr 实例


def get_check_num_base64(session):
    # 发送 GET 请求获取验证码图片
    response = session.get(url, headers=headers, params=params)
    encoded_string = base64.b64encode(response.content).decode('utf-8')
    return encoded_string


def get_check_num(session):
    # 获取验证码图片的 Base64 编码
    image_base64 = get_check_num_base64(session)
    # 将 Base64 编码解码为二进制数据
    image_bytes = base64.b64decode(image_base64)
    # 使用 ddddocr 进行验证码识别
    result = ocr.classification(image_bytes)
    return result


# 示例调用
if __name__ == "__main__":
    session = requests.Session()
    check_num = get_check_num(session)
    print("识别的验证码:", check_num)
