from Crypto.Cipher import AES  # 导入 AES 加密模块
import base64                  # 导入 base64 编码模块
import random                  # 导入 random 模块用于生成随机 
from urllib.parse import quote

def getfcgitoken():
    # 固定的待加密数据，与 JavaScript 中的 data 相同
    data = "KI3+gX1Qg9b7GhyZ9J/Jvg=="
    # 固定的密钥（16 字符），与 JavaScript 中的 temp_key 相同
    temp_key = 'root%$#@!1234567'
    # 定义随机 IV 的字符集，与 JavaScript 中的 chars 数组相同
    chars = '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz'
    # 生成 16 字符的随机 IV，模仿 JavaScript 中的 getRandNum 函数
    temp_iv = ''.join(random.choice(chars) for _ in range(16))

    # 将密钥转换为 Latin-1 编码的字节（16 字节），与 JavaScript 的 CryptoJS.enc.Latin1.parse 一致
    key_bytes = temp_key.encode('latin1')
    # 将随机 IV 转换为 Latin-1 编码的字节（16 字节）
    iv_bytes = temp_iv.encode('latin1')

    # 将待加密数据转换为 UTF-8 字节，与 CryptoJS 的默认行为一致
    data_bytes = data.encode('utf-8')
    # 计算需要填充的零字节数，使数据长度为 16 的倍数（零填充）
    pad_len = (16 - len(data_bytes) % 16) % 16
    # 添加零字节进行填充，与 CryptoJS.pad.ZeroPadding 一致
    padded_data = data_bytes + b'\x00' * pad_len

    # 创建 AES 加密器，使用 CBC 模式，指定密钥和 IV
    cipher = AES.new(key_bytes, AES.MODE_CBC, iv_bytes)
    # 对填充后的数据进行 AES 加密
    ciphertext = cipher.encrypt(padded_data)

    # 将加密后的密文进行 base64 编码，并转换为字符串
    encrypted_b64 = base64.b64encode(ciphertext).decode('utf-8')
    # 将 base64 编码的密文与 IV 字符串连接，模仿 JavaScript 的输出格式
    result = encrypted_b64 + temp_iv

    # 返回最终结果
    return result

# 示例用法：运行代码并打印结果
if __name__ == "__main__":
    token = getfcgitoken()
    print(token)
    encoded_token = quote(token, safe='')
    print("URL编码后:", encoded_token)