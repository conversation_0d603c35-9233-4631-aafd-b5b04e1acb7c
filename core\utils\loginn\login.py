import requests
from .get_checknum import get_check_num
from .password_encrypt import encrypt_password
from .config import user, password, ip, ocr_url
from .get_token import getfcgitoken
import time
import json

session = None

def loginn():
    max_attempt = 5
    current_attempt = 0
    # 使用 session 模拟浏览器行为
    global session
    while current_attempt < max_attempt:
        session = requests.Session()
        login_page_url = f"http://{ip}/login.html"
        check_url = f'http://{ip}/webui/?g=sys_admin_jsondata'
        session.get(login_page_url)
# 获取检查码和加密密码
        vldcode = get_check_num(session)  # 调用get_check_num以获得验证码
        encrypted_pwd = encrypt_password(password)  # 使用encrypt_password函数加密原始密码
# 准备提交的数据
        data = {
            'user': user,
            'pwd': encrypted_pwd,
            'vldcode': vldcode,
            'lang': 'cn',
            'encrypt': '1'
        }
# 设置请求头
        headers = {
            'Referer': f'http://{ip}/login.html',
        }
        session.post(f'http://{ip}/login_auth.php', headers=headers, data=data)
        # check_response = session.get(f'http://{ip}/login_auth.php')
        new_check_response = session.get(check_url)
        # print(f"响应状态码: {new_check_response.status_code}")
        # print(f"响应URL: {new_check_response.url}")
        # print("响应内容:")
        # print(new_check_response.text)

        try:
            response_json = new_check_response.json()  # 直接解析JSON
            group_list = response_json.get('group', [])
            if any(user.get('name') == 'admin' for user in group_list):
                print("登录成功")
                return session
            else:
                print("登录失败，未找到 admin 用户")
        except Exception as e:
            print(f"登录失败，解析响应出错: {e}")

        current_attempt += 1
        time.sleep(2)

    print("已达到最大尝试次数登录失败")
    return None

# 检查session是否有效
def is_session_valid(session):
    # 模拟检查session是否有效，可以根据实际情况修改
    # 这里简单地通过访问一个页面来判断
    check_url = f'http://{ip}/webui/?g=sys_admin_jsondata'
    new_check_response = session.get(check_url)    
    try:
        response_json = new_check_response.json()  # 直接解析JSON
        group_list = response_json.get('group', [])
        if any(user.get('name') == 'admin' for user in group_list):
            return True
        else:
            return False
    except Exception as e:
        return False

