from cryptography.hazmat.primitives import serialization
from cryptography.hazmat.primitives.asymmetric import padding
from cryptography.hazmat.backends import default_backend
import base64


def encrypt_password(userPassword):
    # Define the RSA public key in PEM format (multi-line for readability)
    pubkey = """-----BEGIN PUBLIC KEY-----
MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQC3//sR2tXw0wrC2DySx8vNGlqt
3Y7ldU9+LBLI6e1KS5lfc5jlTGF7KBTSkCHBM3ouEHWqp1ZJ85iJe59aF5gIB2kl
Bd6h4wrbbHA2XE1sq21ykja/Gqx7/IRia3zQfxGv/qEkyGOx+XALVoOlZqDwh76o
2n1vP1D+tD3amHsK7QIDAQAB
-----E<PERSON> PUBLIC KEY-----"""

    # Load the public key from the PEM string
    public_key = serialization.load_pem_public_key(
        pubkey.encode('utf-8'),
        backend=default_backend()
    )
    # Encrypt the input string using RSA with PKCS1 v1.5 padding
    encrypted = public_key.encrypt(
        userPassword.encode('utf-8'),
        padding.PKCS1v15()
    )

    # Convert the encrypted bytes to a base64-encoded string
    encrypted_b64 = base64.b64encode(encrypted).decode('utf-8')

    return encrypted_b64
