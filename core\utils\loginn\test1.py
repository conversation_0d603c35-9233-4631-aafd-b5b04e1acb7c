import requests

def get_request():
    url = "http://**************/webui/"
    # 请求参数
    params = {
        "g": "monitor_users_statistics_jsondata",
        "range": "1"
    }
    # 请求头，模拟浏览器请求
    headers = {
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:136.0) Gecko/20100101 Firefox/136.0",
        "Accept": "*/*",
        "Accept-Language": "zh-CN,zh;q=0.8,zh-TW;q=0.7,zh-HK;q=0.5,en-US;q=0.3,en;q=0.2",
        "Accept-Encoding": "gzip, deflate",
        "Referer": "http://**************/webui/?g=monitor_users_statistics_show",
        "X-Requested-With": "XMLHttpRequest",
        "Cookie": "USGSESSID=05763be37a5b2b2643d882b7b8fb9221",
        "Connection": "close"
    }

    response = requests.get(url, params=params, headers=headers)
    print("GET response:")
    print(response.text)

def post_request():
    url = "http://**************/webui/"
    params = {
        "g": "monitor_users_statistics_jsondata",
        "range": "1"
    }
    headers = {
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:136.0) Gecko/20100101 Firefox/136.0",
        "Accept": "application/json, text/javascript, */*; q=0.01",
        "Accept-Language": "zh-CN,zh;q=0.8,zh-TW;q=0.7,zh-HK;q=0.5,en-US;q=0.3,en;q=0.2",
        "Accept-Encoding": "gzip, deflate",
        "Referer": "http://**************/webui/?g=monitor_users_statistics_show",
        "Content-Type": "application/x-www-form-urlencoded; charset=UTF-8",
        "X-Requested-With": "XMLHttpRequest",
        "Origin": "http://**************",
        "Cookie": "USGSESSID=05763be37a5b2b2643d882b7b8fb9221",
        "Connection": "close"
    }
    # POST 表单数据
    data = {
        "Token": "B5N3PYMSOf1QfiSsw3C0UxfaVm9EGPa2sUvRf4SSWPGD9C1AuZEU8roOQ0MnxY7hPPupf3oDsXJHW2%2FdYZQvxfXcIGjWJcGc15AG1tltua7otNrDpaCq5yssMLnYcVmkSAq4ovzRtKlghzHG8Kp1EbwfSfIoT3igR0xOepnlgOM"
    }

    response = requests.post(url, params=params, headers=headers, data=data)
    print("POST response:")
    print(response.text)

if __name__ == "__main__":
    print("发起 GET 请求：")
    get_request()
    print("\n发起 POST 请求：")
    post_request()
