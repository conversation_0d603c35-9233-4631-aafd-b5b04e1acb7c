from . import *

#SERVER_SLIPS = "http://*************:20004"
SERVER_SLIPS = "http://**************:55000"

@r.route("/events/intelligence/list", methods = ['GET'])
def get_alert_ip():
    url = f"{SERVER_SLIPS}/analysis/profiles_tws"
    page = int(request.args.get('page', 1))  # 默认页数是1
    limit = int(request.args.get('limit', 10))  # 默认每页10条数据
    try:
        response = requests.get(url, verify = False)
        all_data = response.json().get('data', [])
        total_count = len(all_data)
        total_pages = (total_count + limit - 1) // limit  # 向上取整
        
        # 计算当前页的数据范围
        start_idx = (page - 1) * limit
        end_idx = start_idx + limit
        paged_data = all_data[start_idx:end_idx]
        return {
            "code": str(response.status_code),
            "data": {
                "items": paged_data,
                "pagination": {
                    "total": total_count,
                    "page": page,
                    "limit": limit,
                    "pages": total_pages
                }
            },
            "msg": "success"
        }
    except Exception as err:
        return {
            "code" : str(response.status_code),
            "data" : {},
            "msg" : str(err)
        }

@r.route("/events/intelligence/info", methods = ['GET'])
def get_events_intelligence_info():
    ip = request.args.get('ip')
    page = int(request.args.get('page', 1))  # 默认页数是 1
    limit = int(request.args.get('limit', 10))  # 默认每页 5 条数据
    url = f'{SERVER_SLIPS}/analysis/timeline/{ip}/timewindow1?_='
    try:
        response = requests.get(url, verify=False)
        
        # 获取所有事件数据
        all_events = response.json().get('data', [])
        
        # 计算总条数和总页数
        total_count = len(all_events)
        total_pages = (total_count + limit - 1) // limit  # 向上取整，确保能够包含所有数据
        
        # 计算当前页的索引范围
        start_idx = (page - 1) * limit
        end_idx = start_idx + limit
        
        # 获取当前页的数据
        paged_data = all_events[start_idx:end_idx]
        
        # 返回分页结果
        return {
            "code": str(response.status_code),
            "data": {
                "events": paged_data,
                "page": page,
                "limit": limit,
                "total_count": total_count,
                "total_pages": total_pages
            },
            "msg": "success"
        }
    except Exception as err:
        return {
            "code" : str(response.status_code),
            "data" : {},
            "msg" : str(err)
        }
