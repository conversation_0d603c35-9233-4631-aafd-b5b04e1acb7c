from . import *
import requests
from urllib.parse import urlencode
import json
# 从utils导入loginn
from core.utils.loginn.login import loginn, is_session_valid
from core.utils.loginn.config import ip
from io import BytesIO
from zipfile import ZipFile
from flask import request, jsonify, send_file
from core.utils.loginn.selks import get_selks_info
session = None
import uuid
from datetime import datetime, date, time, timedelta
from core.utils.ip_location import get_ip_location

@r.route("/get/userflow", methods=['GET'])
def get_userflow():
    # 尝试获取session 
    global session
    if session is None:
        print("Session是空,尝试登录")
        session = loginn()
    elif not is_session_valid(session):
        print("Session失效,重新登录")
        session = loginn()
    else:
        print("session良好重复使用")

    url = f"http://{ip}/webui/?g=monitor_users_statistics_jsondata"
    new_response = session.get(url)
    if new_response.status_code == 200:
         # 解析响应数据
        data = json.loads(new_response.text)
        return {
            "code" : str(new_response.status_code),
            "data" : data,
            "msg" : "success"
        }
    else:
        return {
            "code" : str(new_response.status_code),
            "data" : {},
            "msg" : "error"
        }
        # return jsonify(data=data)

@r.route("/post/systemlog", methods=['POST'])
def post_systemlog():
    # 尝试获取session 
    global session
    if session is None:
        print("Session是空,尝试登录")
        session = loginn()
    elif not is_session_valid(session):
        print("Session失效,重新登录")
        session = loginn()
    else:
        print("session良好重复使用")

    url = f"http://{ip}/webui/?g=log_fw_system_jsondata"
    data = request.json
    params = {
            "date": data.get('date', ''),
            "end_date": data.get('end_date', ''),
            "content": data.get('content', ''),
            "level_name": data.get('level_name', ''),
            "cas_addr": data.get('cas_addr', '')
        }
    #print(params)
    params_encoded = urlencode(params)
    full_url = f"{url}&{params_encoded}"
    post_data = {
            "page": data.get('page', 1),
            "rows": data.get('rows', 1),
        }
    #print(test_data)
    new_response = session.post(full_url, data=post_data)
    if new_response.status_code == 200:
         # 解析响应数据
        data = json.loads(new_response.text)
        return {
            "code" : str(new_response.status_code),
            "data" : data,
            "msg" : "success"
        }
        # return jsonify(data=data)
    else:
        return {
            "code" : str(new_response.status_code),
            "data" : {},
            "msg" : "error"
        }
    
@r.route("/post/operationlog", methods=['POST'])
def post_operationlog():
    # 尝试获取session 
    global session
    if session is None:
        print("Session是空,尝试登录")
        session = loginn()
    elif not is_session_valid(session):
        print("Session失效,重新登录")
        session = loginn()
    else:
        print("session良好重复使用")

    url = f"http://{ip}/webui/?g=log_fw_operate_jsondata"
    data = request.json
    params = {
            "date": data.get('date', ''),
            "end_date": data.get('end_date', ''),
            "content": data.get('content', ''),
            "level_name": data.get('level_name', ''),
            "admin": data.get('admin', ''),
            "admin_fuzzy": data.get('admin_fuzzy', ''),
            "log_ip": data.get('log_ip', ''),
            "cas_addr": data.get('cas_addr', '')
        }
    params_encoded = urlencode(params)
    full_url = f"{url}&{params_encoded}"
    post_data = {
            "page": data.get('page', 1),
            "rows": data.get('rows', 1),
        }
    new_response = session.post(full_url, data=post_data)
    if new_response.status_code == 200:
         # 解析响应数据
        data = json.loads(new_response.text)
        return {
            "code" : str(new_response.status_code),
            "data" : data,
            "msg" : "success"
        }
        # return jsonify(data=data)
    else:
        return {
            "code" : str(new_response.status_code),
            "data" : {},
            "msg" : "error"
        }
    
@r.route("/post/wafweb", methods=['POST'])
def post_waf_web():
    # 尝试获取session 
    global session
    if session is None:
        print("Session是空,尝试登录")
        session = loginn()
    elif not is_session_valid(session):
        print("Session失效,重新登录")
        session = loginn()
    else:
        print("session良好重复使用")

    url = f"http://{ip}/webui/?g=log_fw_waf_scan_jsondata"
    data = request.json
    params = {
    "date": data.get('date', ''),
    "end_date": data.get('end_date', ''),
    "waf_url": data.get('waf_url', ''),
    "waf_rule_id": data.get('waf_rule_id', ''),
    "waf_rule_type": data.get('waf_rule_type', ''),
    "action": data.get('action', ''),
    "waf_rule_msg": data.get('waf_rule_msg', ''),
    "sip": data.get('sip', ''),
    "dip": data.get('dip', '')
    }
    params_encoded = urlencode(params)
    full_url = f"{url}&{params_encoded}"
    post_data = {
            "page": data.get('page', 1),
            "rows": data.get('rows', 20),
        }
    #print(post_data)
    new_response = session.post(full_url, data=post_data)
    print(new_response.text)
    if new_response.status_code == 200:
         # 解析响应数据
        data = json.loads(new_response.text)
        return {
            "code" : str(new_response.status_code),
            "data" : data,
            "msg" : "success"
        }
        # return jsonify(data=data)
    else:
        return {
            "code" : str(new_response.status_code),
            "data" : {},
            "msg" : "error"
        }

@r.route("/post/policy", methods=['POST'])
def post_plicy():
    # 尝试获取session 
    global session
    if session is None:
        print("Session是空,尝试登录")
        session = loginn()
    elif not is_session_valid(session):
        print("Session失效,重新登录")
        session = loginn()
    else:
        print("session良好重复使用")

    url = f"http://{ip}/webui/?g=sec_policy_filter_jsondata&t="
    data = request.json
    post_data = {
            "policy_group": "/",
            "page": data.get('page', 1),
            "rows": data.get('rows', 20),
        }
    new_response = session.post(url, data=post_data)
    if new_response.status_code == 200:
         # 解析响应数据
        data = json.loads(new_response.text)
        return {
            "code" : str(new_response.status_code),
            "data" : data,
            "msg" : "success"
        }
        # return jsonify(data=data)
    else:
        return {
            "code" : str(new_response.status_code),
            "data" : {},
            "msg" : "error"
        }

# 按时间、类型、关键字查询监控日志
@r.route("/post/monitor_log", methods=['POST'])
def post_monitor_log():
    # 尝试获取session 
    global session
    if session is None:
        print("Session是空,尝试登录")
        session = loginn()
    elif not is_session_valid(session):
        print("Session失效,重新登录")
        session = loginn()
    else:
        print("session良好重复使用")

    url_sys = f"http://{ip}/webui/?g=log_fw_system_jsondata"
    url_opt = f"http://{ip}/webui/?g=log_fw_operate_jsondata"

    data = request.json
    params_sys = {
            "date": data.get('date', ''),
            "end_date": data.get('end_date', ''),
            "content": data.get('content', ''),
            "level_name": data.get('level_name', ''),
            "cas_addr": data.get('cas_addr', '')
        }
    
    params_opt = {
            "date": data.get('date', ''),
            "end_date": data.get('end_date', ''),
            "content": data.get('content', ''),
            "level_name": data.get('level_name', ''),
            "admin": data.get('admin', ''),
            "admin_fuzzy": data.get('admin_fuzzy', ''),
            "log_ip": data.get('log_ip', ''),
            "cas_addr": data.get('cas_addr', '')
        }
    
    params_sys_encoded = urlencode(params_sys)
    params_opt_encoded = urlencode(params_opt)
    full_url_sys = f"{url_sys}&{params_sys_encoded}"
    full_url_opt = f"{url_opt}&{params_opt_encoded}"
    post_data = {
            "page": data.get('page', 1),
            "rows": data.get('rows', 1),
        }
    response_sys = session.post(full_url_sys, data=post_data)
    response_opt = session.post(full_url_opt, data=post_data)
    if response_sys.status_code == 200 & response_opt.status_code == 200:
         # 解析响应数据
        data_sys = json.loads(response_sys.text)
        data_opt = json.loads(response_opt.text)
        # 添加类别属性
        for group in data_sys['group']:
            group['category'] = 'system'  # 为每个日志项添加类别属性
        for group in data_opt['group']:
            group['category'] = 'operate'
        # 合并数据
        merged_data = {
        'group': data_sys['group'] + data_opt['group'],  # 合并两个日志条目列表
        'page': {
            'total': data_sys['page']['total'] + data_opt['page']['total']  # 计算总数
        }
    }
        return {
            "code" : str(response_opt.status_code),
            "data" : merged_data,
            "msg" : "success"
        }
    else:
       return {
            "code" : str(response_opt.status_code),
            "data" : {},
            "msg" : "error"
        }

@r.route("/get/export_log", methods=['GET'])
def get_export_log():
    # 尝试获取session 
    global session
    if session is None:
        print("Session是空,尝试登录")
        session = loginn()
    elif not is_session_valid(session):
        print("Session失效,重新登录")
        session = loginn()
    else:
        print("session良好重复使用")

    # 从前端获取请求参数
    stime = request.args.get('stime')
    etime = request.args.get('etime')
    max_count = request.args.get('max_count', '100000')  # 默认为100000
    stime_hour = request.args.get('stime_hour')
    etime_hour = request.args.get('etime_hour')

     # 第一次请求获得操作日志
    url_1 = f"http://{ip}/webui/"
    params_1_operate = {
        "g": "log_fw_export_notice",
        "log_type": "operate",
        "stime": stime,
        "etime": etime,
        "max_count": max_count,
        "stime_hour": stime_hour,
        "etime_hour": etime_hour
    }
    # 第二次请求获得系统日志
    params_1_event = {
        "g": "log_fw_export_notice",
        "log_type": "event",
        "stime": stime,
        "etime": etime,
        "max_count": max_count,
        "stime_hour": stime_hour,
        "etime_hour": etime_hour
    }
    # 发送第一次请求
    response_1_operate = session.get(url_1, params=params_1_operate)
    response_1_event = session.get(url_1, params=params_1_event)
    # 检查响应是否为1，表示操作成功
    if response_1_operate.text == "1" and response_1_event.text == "1":
        # 第二次请求的参数
        url_2_operate = f"http://{ip}/webui/"
        params_2_operate = {
            "g": "log_fw_export",
            "log_type": "operate"
        }
        
        params_2_event = {
            "g": "log_fw_export",
            "log_type": "event"
        }
        response_2_operate = session.get(url_2_operate, params=params_2_operate)
        response_2_event = session.get(url_2_operate, params=params_2_event)

        # 如果响应为ZIP文件
        if response_2_operate.status_code == 200 & response_2_event.status_code == 200:
            combined_zip = BytesIO()
            with ZipFile(combined_zip, 'w') as zip_out:
                # 合并第一个ZIP文件 (operate)
                with ZipFile(BytesIO(response_2_operate.content), 'r') as zip_ref_2:
                    for file_info in zip_ref_2.infolist():
                        zip_out.writestr(file_info, zip_ref_2.read(file_info))
                
                 # 合并第二个ZIP文件 (event)
                with ZipFile(BytesIO(response_2_event.content), 'r') as zip_ref_4:
                    for file_info in zip_ref_4.infolist():
                        zip_out.writestr(file_info, zip_ref_4.read(file_info))
            
            # 将ZIP文件直接返回给前端进行下载
            combined_zip.seek(0)  # 移动到文件的开始位置
            return send_file(combined_zip, as_attachment=True, attachment_filename="logs.zip", mimetype="application/zip")
        else:
            return jsonify({"error": "Failed to fetch ZIP file"}), 400
    else:
        return jsonify({"error": "Failed to initiate export notice"}), <EMAIL>("/get/export_log", methods=['GET'])
def get_export_log():
    # 尝试获取session 
    global session
    if session is None:
        print("Session是空,尝试登录")
        session = loginn()
    elif not is_session_valid(session):
        print("Session失效,重新登录")
        session = loginn()
    else:
        print("session良好重复使用")

    # 从前端获取请求参数
    stime = request.args.get('stime')
    etime = request.args.get('etime')
    max_count = request.args.get('max_count', '100000')  # 默认为100000
    stime_hour = request.args.get('stime_hour')
    etime_hour = request.args.get('etime_hour')

     # 第一次请求获得操作日志
    url_1 = f"http://{ip}/webui/"
    params_1_operate = {
        "g": "log_fw_export_notice",
        "log_type": "operate",
        "stime": stime,
        "etime": etime,
        "max_count": max_count,
        "stime_hour": stime_hour,
        "etime_hour": etime_hour
    }
    # 第二次请求获得系统日志
    params_1_event = {
        "g": "log_fw_export_notice",
        "log_type": "event",
        "stime": stime,
        "etime": etime,
        "max_count": max_count,
        "stime_hour": stime_hour,
        "etime_hour": etime_hour
    }
    # 发送第一次请求
    response_1_operate = session.get(url_1, params=params_1_operate)
    response_1_event = session.get(url_1, params=params_1_event)
    # 检查响应是否为1，表示操作成功
    if response_1_operate.text == "1" and response_1_event.text == "1":
        # 第二次请求的参数
        url_2_operate = f"http://{ip}/webui/"
        params_2_operate = {
            "g": "log_fw_export",
            "log_type": "operate"
        }
        
        params_2_event = {
            "g": "log_fw_export",
            "log_type": "event"
        }
        response_2_operate = session.get(url_2_operate, params=params_2_operate)
        response_2_event = session.get(url_2_operate, params=params_2_event)

        # 如果响应为ZIP文件
        if response_2_operate.status_code == 200 & response_2_event.status_code == 200:
            combined_zip = BytesIO()
            with ZipFile(combined_zip, 'w') as zip_out:
                # 合并第一个ZIP文件 (operate)
                with ZipFile(BytesIO(response_2_operate.content), 'r') as zip_ref_2:
                    for file_info in zip_ref_2.infolist():
                        zip_out.writestr(file_info, zip_ref_2.read(file_info))
                
                 # 合并第二个ZIP文件 (event)
                with ZipFile(BytesIO(response_2_event.content), 'r') as zip_ref_4:
                    for file_info in zip_ref_4.infolist():
                        zip_out.writestr(file_info, zip_ref_4.read(file_info))
            
            # 将ZIP文件直接返回给前端进行下载
            combined_zip.seek(0)  # 移动到文件的开始位置
            return send_file(combined_zip, as_attachment=True, attachment_filename="logs.zip", mimetype="application/zip")
        else:
            return jsonify({"error": "Failed to fetch ZIP file"}), 400
    else:
        return jsonify({"error": "Failed to initiate export notice"}), 400
    
@r.route("/post/diff_type_count", methods=['POST'])
def get_diff_type_count():
    # 尝试获取session 
    global session
    if session is None:
        print("Session是空,尝试登录")
        session = loginn()
    elif not is_session_valid(session):
        print("Session失效,重新登录")
        session = loginn()
    else:
        print("session良好重复使用")

    attack_types_count = {}
    # 请求参数模板
    params_template = {
        "date": '',
        "end_date": '',
        "waf_url": '', 
        "waf_rule_id": '',
        "waf_rule_type": '',  # 会被替换为攻击类型
        "action": '',
        "waf_rule_msg": '',
        "sip": '',
        "dip": '',
    }

    url = f"http://{ip}/webui/?g=log_fw_waf_scan_jsondata"
    data = request.json
    #进行日期的选择读取到日期
    #定义攻击类型
    attack_types = [
    "HTTP协议检查", "通用攻击", "SQL注入攻击", "XSS攻击", "目录遍历",
    "恶意扫描与爬虫", "木马攻击", "会话劫持", "敏感信息泄露", "服务器防护", "CMS漏洞防护"
]
    for attack_type in attack_types:
        # print(f"正在获取 {attack_type} 的数据...")
        # 更新请求参数，设置当前攻击类型, 时间
        params = params_template.copy()
        params["date"] = data.get('date', '')
        params["end_date"] = data.get('end_date', '')
        params["waf_rule_type"] = attack_type
        params_encoded = urlencode(params)
        full_url = f"{url}&{params_encoded}"
        post_data = {
            "page": 1,
            "rows": 1
        }
        # 发送请求并获取结果
        try:
            response = session.post(full_url, data=post_data)
            print(response.text)
            if response.text:
                re_data = json.loads(response.text)
            else:
                # 处理空响应情况
                print("响应体为空")
            response.raise_for_status()  # 如果响应状态码不是 200，会抛出异常
            re_data = json.loads(response.text)  # 响应数据是 JSON 格式
            total_attacks = re_data.get('page', {}).get('total', 0)
            attack_types_count[attack_type] = total_attacks
        except requests.RequestException as e:
            # print(f"请求失败: {e}")
            #000代表请求错误
            attack_types_count[attack_type] = 000 
    return {
            "code" : str(response.status_code),
            "data" : attack_types_count,
            "msg" : "success"
        }

#内置规则显示
@r.route("/get/waf_rule_base_jsondata", methods=['GET'])
def get_waf_rule_base_jsondata():
    # 尝试获取session
    global session
    if session is None:
        print("Session是空,尝试登录")
        session = loginn()
    elif not is_session_valid(session):
        print("Session失效,重新登录")
        session = loginn()
    else:
        print("session良好重复使用")

    url = f"http://{ip}/webui/?g=waf_rule_base_jsondata"
    new_response = session.post(url)
    print(new_response.text)
    if new_response.status_code == 200:
        # 解析响应数据
        data = json.loads(new_response.text)
        return {
            "code" : str(new_response.status_code),
            "data" : data,
            "msg" : "success"
        }
    else:
        return {
            "code" : str(new_response.status_code),
            "data" : {},
            "msg" : "error"
        }


#自定义规则显示
@r.route("/post/waf_protection_rules_show", methods=['POST'])
def post_waf_protection_rules_show():
    # 尝试获取session 
    global session
    if session is None:
        print("Session是空,尝试登录")
        session = loginn()
    elif not is_session_valid(session):
        print("Session失效,重新登录")
        session = loginn()
    else:
        print("session良好重复使用")

    url = f"http://{ip}/webui/?g=waf_protection_rules_show_jsondata"
    data = request.json
    params = {
        "name": "",
        "src_addr": "",
        "dst_addr": "",
        "domain": "",
        "desc": "",
        "state": "",
        "rule_defend_enable": "",
        "access_control_enable": "",
        "anti_steal_link_enable": "",
        "csrf_defend_enable": "",
        "cc_defend_enable": "",
        "app_hide_enable": "",
        "anti_tamper_enable": "",
    }
    # 更新参数（以传入的为主）
    params.update(params)
    params_encoded = urlencode(params)
    full_url = f"{url}&{params_encoded}"
    post_data = {
        "page": data.get('page', 1),
        "rows": data.get('rows', 1),
    }
    new_response = session.post(full_url, data=post_data)
    # print(new_response.text)
    if new_response.status_code == 200:
         # 解析响应数据
        data = json.loads(new_response.text)
        return {
            "code" : str(new_response.status_code),
            "data" : data,
            "msg" : "success"
        }
    else:
        return {
            "code" : str(new_response.status_code),
            "data" : {},
            "msg" : "error"
        }
    
#自定义规则添加
@r.route("/post/waf_protection_rules_add", methods=['POST'])
def post_waf_protection_rules_add():
    # 尝试获取session
    global session
    if session is None:
        print("Session是空,尝试登录")
        session = loginn()
    elif not is_session_valid(session):
        print("Session失效,重新登录")
        session = loginn()
    else:
        print("session良好重复使用")

    t = time.time()
    url = f"http://{ip}/webui/?g=waf_protection_rules_add&t={t}"
    #获取请求参数
    post_data = request.json
    # 初始化需要的参数
    params = {
        "anti_tamper_cache_count": "",
        "enable": 1,
        "name": "test",
        "src_addr": "any",
        "dst_addr": "any",
        "server_port": 80,
        "domain": "",
        "discription": "",
        "wrp_enable": 1,
        "wrp_log": 1,
        "protection_level": 2,
        "defendScope": 0,
        "anti_steal_file_suffix": "",
        "anti_steal_action": 0,
        "anti_steal_redirect": "",
        "anti_steal_log": 7,
        "ccDefend": 0,
        "cc_interval": 60,
        "cc_access_times": 600,
        "ccAction": 0,
        "cc_block_time": 600,
        "cc_syslog": 7,
        "anti_tamper_url": "",
        "conf_netaddr": "",
        "ipaddr_items": "",
        "anti_tamper_url_hidden": "",
        "ipaddr_items_hidden": [],
        "conf_exclude_hidden": "",
        "anti_tamper_action": 0,
        "anti_tamper_redirect": "",
        "anti_tamper_tip": "",
        "anti_tamper_syslog": 7,
        "app_syslog": 7,
        "id": "",
        "submit_post": "waf_protection_rules_addsave",
        "anti_steal_url_cid": "",
        "anti_steal_white_list_cid": "",
        "cc_url_cid": "",
    }
    # 更新参数（以传入的为主）
    params.update(post_data)
    response = session.post(url, data=params)
    if response.status_code == 200:
        # 解析响应数据
        data = json.loads(response.text)
        return {
            "code" : str(response.status_code),
            "data" : {},
            "msg" : "success"
        }
    else:
        return {
            "code" : str(response.status_code),
            "data" : {},
            "msg" : "error"
        }
    
#自定义规则启用
@r.route("/get/waf_protection_rules_enable", methods=['GET'])
def get_waf_protection_rules_enable():
    global session
    if session is None:
        print("Session是空,尝试登录")
        session = loginn()
    elif not is_session_valid(session):
        print("Session失效,重新登录")
        session = loginn()
    else:
        print("session良好重复使用")

    # 从请求头里取 id
    rule_id = request.args.get('id')
    if not rule_id:
        return jsonify({'error': 'Missing id in args'}), 400
    target_url = f"http://{ip}/webui/?g=waf_protection_rules_enable&id={rule_id}"
    # 发起 GET 请求
    response = session.get(target_url)
    res_json = response.json()

    # 检查返回结果
    if res_json.get('code') == 0:
        return {
            "code" : str(response.status_code),
            "data" : {"启动成功"},
            "msg" : "success"
        }
    else:
        return {
            "code" : str(response.status_code),
            "data" : {"启动失败"},
            "msg" : "error"
        }
    
#自定义规则禁用
@r.route("/get/waf_protection_rules_disable", methods=['GET'])
def get_waf_protection_rules_disable():
    global session
    if session is None:
        print("Session是空,尝试登录")
        session = loginn()
    elif not is_session_valid(session):
        print("Session失效,重新登录")
        session = loginn()
    else:
        print("session良好重复使用")

    # 从请求头里取 id
    rule_id = request.args.get('id')
    if not rule_id:
        return jsonify({'error': 'Missing id in args'}), 400
    target_url = f"http://{ip}/webui/?g=waf_protection_rules_disable&id={rule_id}"
    # 发起 GET 请求
    response = session.get(target_url)
    res_json = response.json()

    # 检查返回结果
    if res_json.get('code') == 0:
        return {
            "code" : str(response.status_code),
            "data" : {"禁用成功"},
            "msg" : "success"
        }
    else:
        return {
            "code" : str(response.status_code),
            "data" : {"禁用失败"},
            "msg" : "error"
        }

#自定义规则删除
@r.route("/get/waf_protection_rules_del", methods=['GET'])
def get_waf_protection_rules_del():
    global session
    if session is None:
        print("Session是空,尝试登录")
        session = loginn()
    elif not is_session_valid(session):
        print("Session失效,重新登录")
        session = loginn()
    else:
        print("session良好重复使用")

    # 从请求头里取 id
    rule_id = request.args.get('id')
    if not rule_id:
        return jsonify({'error': 'Missing id in args'}), 400
    target_url = f"http://{ip}/webui/?g=waf_protection_rules_del&id={rule_id}"
    # 发起 GET 请求
    response = session.get(target_url)
    res_json = response.json()

    # 检查返回结果
    if res_json.get('code') == 0:
        return {
            "code" : str(response.status_code),
            "data" : {"删除成功"},
            "msg" : "success"
        }
    else:
        return {
            "code" : str(response.status_code),
            "data" : {"删除失败"},
            "msg" : "error"
        }

#规则预设等级设置
@r.route("/get/waf_rule_base_list_level_change", methods=['GET'])
def get_waf_rule_base_list_level_change():
    global session
    if session is None:
        print("Session是空,尝试登录")
        session = loginn()
    elif not is_session_valid(session):
        print("Session失效,重新登录")
        session = loginn()
    else:
        print("session良好重复使用")

#从请求头里取参数
    name = request.args.get('name')
    protection_level = request.args.get('protection_level')

    # 获取参数
    name = request.args.get('name', '')
    protection_level = request.args.get('protection_level')

    # 参数校验
    if not protection_level:
        return jsonify({'error': 'Missing protection_level in args'}), 400
    
    # 构造请求 URL
    target_url = f"http://{ip}/webui/?g=waf_rule_base_list_level_change&name={name}&protection_level={protection_level}"
     # 发起 GET 请求
    response = session.get(target_url)

    try:
        res_json = response.json()
    except Exception as e:
        return jsonify({'error': 'Invalid JSON response', 'details': str(e)}), 500

    return jsonify({
        "code": str(response.status_code),
        "data": res_json,
        "msg": "success" if response.status_code == 200 else "error"
    })

#防火墙详细防护等级显示
@r.route("/get/waf_rule_base_details_list_ajax", methods=['GET'])
def get_waf_rule_base_details_list_ajax():
    global session
    if session is None:
        print("Session是空,尝试登录")
        session = loginn()
    elif not is_session_valid(session):
        print("Session失效,重新登录")
        session = loginn()
    else:
        print("session良好重复使用")

    #从请求头里取参数
    name = request.args.get('name')
    print(name)
    protection_level = request.args.get('protection_level')
    print(protection_level)

        # 参数校验
    if not protection_level:
        return jsonify({'error': 'Missing protection_level in args'}), 400
    
    # 构造请求 URL
    target_url = f"http://{ip}/webui/?g=waf_rule_base_details_list_ajax&name={name}&protection_level={protection_level}"
    print(target_url)
    headers={
            'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8',
            'HTTP_X_REQUESTED_WITH': 'xmlhttprequest'
        }
     # 发起 GET 请求
    response = session.post(target_url,headers=headers)
    print(response.text)
    try:
        res_json = response.json()
    except Exception as e:
        return jsonify({'error': 'Invalid JSON response', 'details': str(e)}), 500

    return jsonify({
        "code": str(response.status_code),
        "data": res_json,
        "msg": "success" if response.status_code == 200 else "error"
    })

@r.route("/get/waf_rule_base_details_list_ajax2", methods=['GET'])
def get_waf_rule_base_details_list_ajax2():
    global session
    if session is None:
        print("Session是空,尝试登录")
        session = loginn()
    elif not is_session_valid(session):
        print("Session失效,重新登录")
        session = loginn()
    else:
        print("session良好重复使用")

    # 从请求体中获取参数
    node_id = request.args.get('node_id')
    mode_name = request.args.get('mode_name')
    rule_id = request.args.get('id')

    # 参数校验（例：id和node_id不能为空）
    if not node_id or not rule_id or not mode_name:
        return jsonify({'error': 'Missing node_id or id or mode_name'}), 400

    # URL 构造
    target_url = (
        f"http://{ip}/webui/?g=waf_rule_base_details_list_ajax"
        f"&node_id={node_id}&mode_name={mode_name}&id={rule_id}"
        f"&level=&rule_id=&sig_name=&log=&act=&protection_level=&protection_name=&root=1"
    )
    print("目标URL：", target_url)

    # 发起 POST 请求
    try:
        response = session.post(target_url)
        res_json = json.loads(response.text)
    except Exception as e:
        return jsonify({'error': 'Invalid JSON response', 'details': str(e)}), 500

    return jsonify({
        "code": str(response.status_code),
        "data": res_json,
        "msg": "success" if response.status_code == 200 else "error"
    })

@r.route("/post/sec_waf_sig_node_mod", methods=['POST'])
def post_sec_waf_sig_node_mod():
    global session
    if session is None:
        print("Session是空,尝试登录")
        session = loginn()
    elif not is_session_valid(session):
        print("Session失效,重新登录")
        session = loginn()
    else:
        print("session良好重复使用")
    
    # 从请求体中获取参数
    sig_id = request.form.get('sig_id','')
    root = request.form.get('root','')
    node_id = request.form.get('node_id','')
    enable = request.form.get('enable', '')  # 如果没有传递enable，默认为空
    log = request.form.get('log', '')  # 如果没有传递log，默认为空
    action = request.form.get('action', '')  # 如果没有传递action，默认为空

# 构造POST请求的目标URL
    target_url = f"http://{ip}/webui/?g=sec_waf_sig_node_mod%t={time.time()}"
    #print("目标URL：", target_url)

    payload = {
        'sig_id': sig_id,
        'root': root,
        'node_id': node_id,
        'enable': enable,
        'log': log,
        'action': action
    }
    #发起 POST 请求
    try:
        response = session.post(target_url, data=payload)
        res_json = json.loads(response.text)
    except Exception as e:
        return jsonify({'error': 'Invalid JSON response', 'details': str(e)}), 500

    return jsonify({
        "code": str(response.status_code),
        "data": res_json,
        "msg": "success" if response.status_code == 200 else "error"
    })

@r.route("/post/events_merge", methods=['POST'])
def post_events_merge():
    global session
    if session is None:
        print("Session是空,尝试登录")
        session = loginn()
        print("成功获取session")
    elif not is_session_valid(session):
        print("Session失效,重新登录")
        session = loginn()
    else:
        print("session良好重复使用")

    url = f"http://{ip}/webui/?g=log_fw_waf_scan_jsondata"
    data = request.json
    # print(data)
    params = {
    "date": data.get('date', ''),
    "end_date": data.get('end_date', ''),
    "waf_url": data.get('waf_url', ''),
    "waf_rule_id": data.get('waf_rule_id', ''),
    "waf_rule_type": data.get('waf_rule_type', ''),
    "action": data.get('action', ''),
    "waf_rule_msg": data.get('waf_rule_msg', ''),
    "sip": data.get('sip', ''),
    "dip": data.get('dip', '')
    }
    params_encoded = urlencode(params)
    full_url = f"{url}&{params_encoded}"
    post_data = {
            "page": data.get('page', 1),
            "rows": data.get('rows', 100),
        }
    #print(post_data)
    new_response = session.post(full_url, data=post_data)
    # print(new_response.text)
    #访问selks的信息组合后一起返回
    # 获取原始时间字符串
    start_raw = data.get('date')          # '2025-03-17 00:00'
    end_raw = data.get('end_date')        # '2025-03-18 23:59'

    # 解析成 datetime 对象
    start_dt = datetime.strptime(start_raw, '%Y-%m-%d %H:%M')
    end_dt = datetime.strptime(end_raw, '%Y-%m-%d %H:%M')

    # 转换为指定格式的字符串
    #待修复的传入参数
    #start_time_str = f"{start_dt.year}-{start_dt.month}-{start_dt.day} {end_dt.hour}:{end_dt.minute}"
    #end_time_str = f"{end_dt.year}-{end_dt.month}-{end_dt.day} {end_dt.hour}:{end_dt.minute}"

    start_time_str = start_dt.strftime("%Y-%m-%d %H:%M")  # 输出示例：2025-07-01 00:00
    end_time_str = end_dt.strftime("%Y-%m-%d %H:%M")     # 输出示例：2025-07-04 23:59
    print(f"开始时间: {start_time_str}, 结束时间: {end_time_str}")
    selks = []
    selks = get_selks_info(start_time_str=start_time_str, end_time_str=end_time_str)
    selks_data = selks.get('count', {})
    print(selks_data)
    event_list = []
    if new_response.status_code == 200:
        try:
            # 如果响应内容不为空，尝试解析 JSON
            if new_response.text.strip():
                data = json.loads(new_response.text)
            else:
                # 响应内容为空，填空数据
                data = {}
        except json.JSONDecodeError:
            # 解析失败，也填空数据
            print(f"[ERROR] JSON 解码失败，内容为: {repr(new_response.text)}")
            data = {}

        # 初始化统计计数器
        #high_count = selks_data.get('高危攻击数')
        #mid_count = selks_data.get('中危攻击数')
        #low_count = selks_data.get('低危攻击数')

        for item in data.get("group", []):
            # 等级转换逻辑
            level_raw = item.get("log_pri", "")
            level_str = "未知"

            if isinstance(level_raw, list):
                level_raw = level_raw[0] if level_raw else ""

            if isinstance(level_raw, int):
                if level_raw in [0, 2]:
                    level_str = "High"
                    #high_count += 1
                elif level_raw in [1, 3]:
                    level_str = "Medium"
                    #mid_count += 1
                elif level_raw in [4, 5, 6, 7]:
                    level_str = "Low"
                    #low_count += 1
                else:
                    level_str = f"未知({level_raw})"


            event = {
                "事件类型": item.get("waf_rule_type", ""),
                "src_ip": item.get("waf_sip", ""),
                "dst_ip": item.get("waf_dip", ""),
                "src_port": item.get("waf_sport", ""),
                "dst_port": item.get("waf_dport", ""),
                "级别": level_str,
                "事件描述": item.get("waf_rule_msg", ""),
                "detect_time": item.get("log_time", ""),
                "detection_system": "waf" ,
                "alert_id" : str(uuid.uuid4())
            }
            event_list.append(event)


        combined = selks["data"] + event_list #合并了防火墙和selks的事件列表
        # 接下来要进行数据库的插入操作
        # 构造插入数据
        alert_data_list = []
        for event in combined:
            exists = Alert.where("source_ip", event["src_ip"])\
                        .where("destination_ip", event["dst_ip"])\
                        .where("source_port", int(event["src_port"]))\
                        .where("destination_port", int(event["dst_port"]))\
                        .where("signature", event["事件描述"])\
                        .where("timestamp", event["detect_time"])\
                        .first()
            if exists:
                print("已存在")
                continue  # 跳过已存在

            alert_data = {
                "alert_id": event["alert_id"],
                "timestamp": event["detect_time"],
                "source_ip": event["src_ip"],
                "destination_ip": event["dst_ip"],
                "source_port": int(event["src_port"]),
                "destination_port": int(event["dst_port"]),
                "protocol": "TCP/UDP",  # 可动态识别或写死
                "attack_type": event["事件类型"],
                "severity": event["级别"],
                "signature": event["事件描述"],
                "detection_system": event["detection_system"],  # 可根据需要填具体来源
                "correlation_id": str(uuid.uuid4()),
                "status": 0,
                "create_time": Time.get_date_time(),
                "update_time": Time.get_date_time(),
                "detail": json.dumps(event.get("detail")) if event.get("detail") else None,
                "ip_location": get_ip_location(event["src_ip"]) #一个来源地ip的判断
            }
            alert_data_list.append(alert_data)
        # 批量插入：Orator 支持多条插入
        if alert_data_list:
            Alert.insert(alert_data_list)
            #print("成功插入数据")

        db_alerts = Alert.where("timestamp", ">=", start_time_str)\
                        .where("timestamp", "<=", end_time_str)\
                        .order_by("timestamp", "desc")\
                        .get()
        
        db_result = []
        for alert in db_alerts:
            #location = get_ip_location(alert.source_ip)
            db_result.append({
                "alert_id": alert.alert_id,
                "timestamp": alert.timestamp.strftime("%Y-%m-%d %H:%M:%S") if isinstance(alert.timestamp, datetime) else str(alert.timestamp),
                "src_ip": alert.source_ip,
                "src_ip_location": alert.ip_location,
                "dst_ip": alert.destination_ip,
                "src_port": alert.source_port,
                "dst_port": alert.destination_port,
                "事件类型": alert.attack_type,
                "级别": alert.severity,
                "事件描述": alert.signature,
                "detection_system": alert.detection_system,
                "detail": json.loads(alert.detail) if alert.detail else None,
            })
        

        # 重新统计等级数量
        high_count = sum(1 for alert in db_result if alert["级别"] == "High")
        mid_count = sum(1 for alert in db_result if alert["级别"] == "Medium")
        low_count = sum(1 for alert in db_result if alert["级别"] == "Low")

        return {
            "code" : str(new_response.status_code),
            # "data" : combined,
            "data": {
                "events": db_result,
                "攻击总数": len(db_result),
                "高危攻击数": high_count,
                "中危攻击数": mid_count,
                "低危攻击数": low_count
            },
            "msg" : "success"
        }

    else:
        return {
            "code" : str(new_response.status_code),
            "data" : {},
            "msg" : "error"
        }

@r.route("/post/compare_sql", methods=['POST'])
def post_compare_sql():
    data = request.json
    # 参数校验，约定是否是进行年的同比
    # 校验：必须是一个列表
    if not isinstance(data, list):
        return {
            "code": "400",
            "data": {},
            "msg": "参数必须为列表结构"
        }
    
    result_data = []

    for item in data:
        # 校验是否包含year
        if 'year' not in item:
            return {
                "code": "400",
                "data": {},
                "msg": "缺少参数 year"
            }
    
        try:
            year = int(item.get('year'))
        except (ValueError, TypeError):
            return {
                "code": "400",
                "data": {},
                "msg": "年份参数格式不正确"
            }
        
        # 情况一：只传 year（返回该年每月统计数量）
        if 'month' not in item:
            monthly_data = []
            total_count = 0

            for month in range(1, 13):
                start = datetime(year, month, 1)
                end = datetime(year + 1, 1, 1) if month == 12 else datetime(year, month + 1, 1)
                count = Alert.where('timestamp', '>=', start).where('timestamp', '<', end).count()
                monthly_data.append({f"{year}-{month:02d}": count})
                total_count += count

            result_data.append({
                "year": year,
                "month": None,
                "type": "yearly",
                "monthly": monthly_data,
                "total": total_count
            })
        
        # 情况二：传 year + month（返回该月每日统计数量）
        # 情况二：包含year和month，返回每日统计
        else:
            try:
                month = int(item.get('month'))
                start_date = date(year, month, 1)
                end_date = date(year + 1, 1, 1) if month == 12 else date(year, month + 1, 1)
            except (TypeError, ValueError):
                return {
                    "code": "400",
                    "data": {},
                    "msg": "month参数格式不正确"
                }

            daily_result = []
            total_account = 0
            current = start_date

            while current < end_date:
                next_day = current + timedelta(days=1)
                start_dt = datetime.combine(current, time.min)
                end_dt = datetime.combine(next_day, time.min)
                count = Alert.where('timestamp', '>=', start_dt).where('timestamp', '<', end_dt).count()
                daily_result.append({current.strftime('%Y-%m-%d'): count})
                total_account += count
                current = next_day

            result_data.append({
                "year": year,
                "month": month,
                "type": "monthly",
                "daily": daily_result,
                "total": total_account
            })

    return {
        "code": "200",
        "data": result_data,
        "msg": "success"
    }

@r.route("/post/waf_blacklist", methods=['POST'])
def add_ip_to_blacklist():
    # 尝试获取session
    global session
    if session is None:
        print("Session是空,尝试登录")
        session = loginn()
        print("成功获取session")
    elif not is_session_valid(session):
        print("Session失效,重新登录")
        session = loginn()
    else:
        print("session良好重复使用")

    # 获取请求数据
    data = request.get_json()
    if not data or 'ip' not in data:
        return jsonify({"status": 2, "result": "缺少参数 ip"}), 400
    add_ip = data['ip']
    age = int(data.get('age', 300))  # 默认为300秒

    try:
        # 1. 获取 token 页面
        page_url = f"http://{ip}/webui/?g=sec_ad_blacklist_add"
        res = session.get(page_url)
        if res.status_code != 200:
            return jsonify({"status": 2, "result": f"无法访问添加页面: {res.status_code}"}), 500

        #构造 POST 数据
        data = {
            'enable': '1',
            'src_ip': add_ip,
            'age': str(age),
            'custom': '0',
            'submit_post': 'sec_ad_blacklist_addsave',
        }
        # 提交黑名单请求
        res = session.post(page_url, data=data)
        if "操作成功" in res.text:
            return {
            "code" : 200,
            "data" : f"IP {add_ip} 已添加到黑名单",
            "msg" : "success"
        }
        elif "频繁操作" in res.text:
            return {
            "code" : 429,
            "data" : f"操作频繁，请稍后重试",
            "msg" : "error"
        }
        else:
            return {
            "code" : 500,
            "data" : f"添加失败，未知响应",
            "msg" : "error"
        }

    except Exception as e:
        return jsonify({"status": 2, "result": f"添加失败: {str(e)}"}), 500