from . import *

BASE_URL = 'https://**************:443'
#BASE_URL = 'https://*************:12601'
TOKEN = '4d84a049bbb21b8b09cfe5ea5093b2bb8e4bc37f'
RULE_ENDPOINT = '/rest/rules/rule'
SOURCE_ENDPOINT = '/rules/source/add_public'
ALERT_MESSAGE_COUNT = '/rest/rules/es/alerts_count'


HEADERS = {
    "Accept": "*/*",
    "Accept-Encoding": "gzip, deflate, br, zstd",
    "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
    "Origin": "https://*************:12601",
    "Referer": "https://*************:12601/rules/source/add_public",
    "Sec-Fetch-Dest": "empty",
    "Sec-Fetch-Mode": "cors",
    "Sec-Fetch-Site": "same-origin",
    "X-CSRFToken": "j2v9l0KmRd1S6QivxMPjFhEPXFZH2hXdXYrg8DGHXdpLPanNBwQiSsXKyGDVskxv",
    "X-Requested-With": "XMLHttpRequest",
    "sec-ch-ua": '"Microsoft Edge";v="135", "Not-A.Brand";v="8", "Chromium";v="135"',
    "sec-ch-ua-mobile": "?0",
    "sec-ch-ua-platform": '"Windows"',
    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********",
    'Authorization': f'Token {TOKEN}',
    'Accept': 'application/json'
}

COOKIE = {
    'csrftoken': 'XXQj9KA3fPjP7AqkvqssSLoB1A4UA4SCLg6b5drjaWzf1WpZwyvltHZCguRlHjgk',
    'sessionid': 'z59l79ooq3gc4nz3hk3f5rf6g0ny8flg'
}

@r.route("/ruleset/list", methods=['GET'])
def get_all_rules():
    page = request.args.get('number', default=1, type=int)
    url = f"{BASE_URL}{RULE_ENDPOINT}?page={page}"
    print("开始获取规则集，当前第{page}页")

    try:
        response = requests.get(url, headers=HEADERS, cookies = COOKIE, verify=False)
        if response.status_code == 200:    
            re = response.json()
            result = []
            for item in re.get('results', []):
                new_item = {
                    'category': {
                        'pk': item['category']['pk'],
                        'name': item['category']['name'],
                        'descr': item['category']['descr']
                    },
                    'desc': item['msg'],
                    'versions': [
                        {
                            'id': version['id'],
                            'content': version['content'],
                            'imported_date': version['imported_date'],
                            'updated_date': version['updated_date'],
                            'created': version['created'],
                            'updated': version['updated']
                        }
                        for version in item.get('versions', [])
                    ]
                }
                result.append(new_item)
            data = {
                "code" : str(response.status_code),
                "data" : result,
                "msg" : "success" 
            }
            return jsonify(data)
        else:
            data = {
                "code" : str(response.status_code),
                "data" : "",
                "msg" : str(e) 
            }
            return {'error': f"请求错误: {str(e)}"}
    except Exception as e:
        data = {
            "code" : str(response.status_code),
            "data" : "",
            "msg" : str(e) 
        }
        return {'error': f"请求错误: {str(e)}"}

@r.route("/source_list", methods=['GET'])
def get_source_list():
    url = f"{BASE_URL}/rules/source/add_public"

    try:
        response = requests.get(url, headers=HEADERS, cookies=COOKIE, verify=False)
        response.raise_for_status()  # 如果状态码不是 200，会抛出 HTTPError 异常

        re = response.json()
        data = []
        for k,v in re.items():
            if v.get("added", True):
                continue
            data.append({k: v})
        return {
            "code" : str(response.status_code),
            "data" : data,
            "msg" : "success"
        } 
    except Exception as e:
        return {
            "code" : str(response.status_code),
            "data" : {},
            "msg" : str(e)
        }

@r.route("/ruleset/download/list", methods = ['GET'])
def get_ruleset_download_list():
    url = f'{BASE_URL}/rest/rules/ruleset'
    try:
        response = requests.get(url, headers = HEADERS, cookies = COOKIE, verify = False)
        if response.status_code == 200:
            data = response.json()
            ruleset_list = []
            for result in data.get("results", []):
                item = {
                    "pk" : result.get("pk"),
                    "name" : result.get("name"),
                    "rules_count" : result.get("rules_count"),
                    "sources" : result.get("sources")
                }
                ruleset_list.append(item)
            content = {
                "count" : data.get("count"),
                "results" : ruleset_list
            }
            data = {
                "code" : str(response.status_code),
                "data" : content,
                "msg" : "success" 
            }
            return data
    except Exception as e:
        data = {
            "code" : str(response.status_code),
            "data" : "",
            "msg" : str(e) 
        }
        return data

from flask import Response, stream_with_context, request

@r.route("/ruleset/download/export", methods = ['GET'])
def get_ruleset_download_export():
    pk = request.args.get("pk")

    if not pk:
        return {"error" : "未指定下载规则集id"}
    
    url = f'{BASE_URL}/rules/ruleset/{pk}/export'
    
    try:
        # 拉取远程文件
        response = requests.get(url, headers=HEADERS, cookies=COOKIE, verify=False, stream=True)

        if response.status_code == 200:
            # 尝试获取远程响应的文件名
            content_disposition = response.headers.get("Content-Disposition", "")
            if "filename=" in content_disposition:
                filename = content_disposition.split("filename=")[-1].strip('"')
            else:
                filename = f"ruleset_{pk}.tgz"  # 默认文件名，可根据实际改为 .xlsx 等

            # 返回下载响应
            return Response(
                stream_with_context(response.iter_content(chunk_size=8192)),
                content_type=response.headers.get("Content-Type", "application/octet-stream"),
                headers={
                    "Content-Disposition": f'attachment; filename="{filename}"'
                }
            )
        else:
            return {"error": f"远程服务器返回错误：{response.status_code}"}, response.status_code

    except Exception as e:
        return {"error": str(e)}, 500


@r.route("/add/ruleset", methods = ['GET'])
def add_ruleset():
    pk = request.args.get('pk')
    url = f'{BASE_URL}/rules/source/{pk}/update'
    try:
        response = requests.post(url, headers=HEADERS, cookies=COOKIE, verify=False)
        return {
            "code" : str(response.status_code),
            "data" : response.json(),
            "msg" : response.json().get("status")
        }
    except Exception as err:
        return {
            "code" : str(response.status_code),
            "data" : {},
            "msg" : str(err)
        }


@r.route("/ruleset", methods = ['GET'])
def get_ruleset():
    page = request.args.get("page")
    url = f'{BASE_URL}/rest/rules/rule/?page={page}'

    try:
        response = requests.get(url, headers=HEADERS, verify=False)
        data = response.json()
        # data.pop('next', None)
        # data.pop('previous', None)
        filter_data = []
        for result in data.get('results', []):
            item = {
                "pk" : result.get("pk"),
                "msg" : result.get("msg"),
                "rule": result["versions"][0]["content"],
                # "state": result["versions"][0]["state"],
                "pk_ruleset" : result["category"]["source"]
            }
            filter_data.append(item)
        total = {
            "count" : data.get("count"),
            "info" : filter_data
        }
        print("原始响应：", response.text)
        return {
            "code" : str(response.status_code),
            "data" : total,
            "msg" : "success"
        }
    except Exception as err:
        return {
            "code" : str(response.status_code),
            "data" : {},
            "msg" : str(err)
        }

@r.route("/ruleset/disable")
def disable_ruleset():
    pk = request.args.get("pk")
    pk_ruleset = request.args.get("pk_ruleset")
    data = {
        "ruleset": pk_ruleset
    }

    url = f"{BASE_URL}/rest/rules/rule/{pk}/disable/"
    try:
        response = requests.post(url, headers=HEADERS, json=data, verify=False)
        return {
            "code" : str(response.status_code),
            "data" : response.json(),
            "msg" : "success"
        }

    except Exception as err:
        return {
            "code" : str(response.status_code),
            "data" : {},
            "msg" : str(err)
        }

@r.route("/ruleset/enable")
def enable_ruleset():
    pk = request.args.get("pk")
    pk_ruleset = request.args.get("pk_ruleset")
    data = {
        "ruleset": pk_ruleset
    }

    url = f"{BASE_URL}/rest/rules/rule/{pk}/enable/"
    try:
        response = requests.post(url, headers=HEADERS, json=data, verify=False)
        return {
            "code" : str(response.status_code),
            "data" : response.json(),
            "msg" : "success"
        }

    except Exception as err:
        return {
            "code" : str(response.status_code),
            "data" : {},
            "msg" : str(err)
        }

# @r.route("/ruleset/status", methods=['GET'])
# def status_ruleset():
#     pk_rule = request.args.get("pk_rule")
#     url = f'{BASE_URL}/rest/rules/rule/{pk_rule}/status/'

#     try:
#         response = requests.post(url, headers=HEADERS, verify=False)
#         data = response.json()
#         for 
#         return {
#             "code" : response.status_code,
#             "data" :{
#                 "status" : data.
#             }
#         }