from core.model import Alert
import json
#from core.
from core.utils.loginn.selks import get_selks_info
from . import *

@r.route("/post/sql_zgd_insert", methods=['POST'])
def post_sql_zgd_insert():
    try:
        data = request.get_json()
        # 必填字段校验（按需添加）
        required_fields = ['rectify_order_id', 'vuln_description', 'vuln_ip']
        for field in required_fields:
            if field not in data:
                return jsonify({'code': 400, 'msg': f'Missing required field: {field}'}), 400
            
        # 插入数据
        W5Vulnerability.insert({
            'rectify_order_id': data.get('rectify_order_id'),
            'source_company': data.get('source_company'),
            'source_code': data.get('source_code'),
            'issue_date': data.get('issue_date'),
            'vuln_description': data.get('vuln_description'),
            'vuln_type': data.get('vuln_type'),
            'vuln_count': data.get('vuln_count', 1),
            'vuln_ip': data.get('vuln_ip'),
            'responsible_dept': data.get('responsible_dept'),
            'is_rectified': int(data.get('is_rectified', False)),
            'rectify_date': data.get('rectify_date'),
            'is_feedback': int(data.get('is_feedback', False)),
            'rectify_status': data.get('rectify_status'),
            'notes': data.get('notes'),
            'recheck_status': data.get('recheck_status'),
            'recheck_202409': data.get('recheck_202409'),
            'recheck_202410': data.get('recheck_202410'),
            'recheck_202411': data.get('recheck_202411'),
            'recheck_202412': data.get('recheck_202412'),
            'recheck_202501': data.get('recheck_202501'),
            'create_time': datetime.now(),
            'update_time': datetime.now()
        })

        return jsonify({'code': 200, 'msg': '插入成功'})
    
    except Exception as e:
        return jsonify({'code': 500, 'msg': f'服务器错误: {str(e)}'}), 500
        


