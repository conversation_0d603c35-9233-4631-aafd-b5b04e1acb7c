from . import *
# 从utils导入loginn
from core.utils.loginn.login import loginn, is_session_valid
from core.utils.loginn.config import ip
from urllib.parse import urlencode
import json 
session = None

BASE_URL = 'https://**************:443/'
#BASE_URL = 'https://*************:12601/'
MESSAGE_ENDPOINT = 'rest/rules/es/'
CONTENT = 'alerts_tail/'
TOKEN = '4d84a049bbb21b8b09cfe5ea5093b2bb8e4bc37f'
HEADERS = {
    'Authorization': f'Token {TOKEN}',
    'Content-Type': 'application/json',
    'x-requested-with': 'XMLHttpRequest'
}

COOKIE = {
    'Cookie': 'csrftoken=j2v9l0KmRd1S6QivxMPjFhEPXFZH2hXdXYrg8DGHXdpLPanNBwQiSsXKyGDVskxv; sessionid=n7gsswq0tunox9etyosdu1ntx4y39wa7'
}

@r.route("/latest/message", methods=['GET'])
def get_latest_message():
    count = request.args.get('count', default=1, type=int)
    # from_time = datetime(2025, 1, 1, 0, 0, 0, tzinfo=timezone.utc)
    # from_timestamp_ms = int(from_time.timestamp() * 1000)

    to_timestamp_ms = int(datetime.utcnow().replace(tzinfo=timezone.utc).timestamp() * 1000)
    url = f'{BASE_URL}{MESSAGE_ENDPOINT}{CONTENT}?from_date=0&to_date={to_timestamp_ms}'
    try:
        response = requests.get(url, headers=HEADERS, cookies=COOKIE,verify=False)
        data = response.json()
        extracted_results = []
        for result in data.get("results", []):
            extracted_result = {
                "timestamp": result.get("@timestamp"),
                "src_ip": result.get("src_ip"),
                "src_port": result.get("src_port"),
                "dest_ip": result.get("dest_ip"),
                "dest_port": result.get("dest_port"),
                "category": result.get("alert", {}).get("category"),
                "signature": result.get("alert", {}).get("signature"),
                "severity": result.get("alert", {}).get("metadata", {}).get("signature_severity", [])
            }
            extracted_results.append(extracted_result)
        extracted_results.sort(key=lambda x: x["timestamp"], reverse=True)
        recent_results = extracted_results[:count]
        return {
            "code" : str(response.status_code),
            "data" : recent_results,
            "msg" : "success"
        }
    except Exception as err:
        print(f"Request failed with status code {response.status_code}")
        print(f"Response content: {response.text}")
        return {
            "code" : str(response.status_code),
            "data" : {},
            "msg" : str(err)
        }

@r.route("/geography/ip", methods=['GET'])
def get_ip_geography():
    to_timestamp_ms = int(datetime.utcnow().replace(tzinfo=timezone.utc).timestamp() * 1000)
    url = f'{BASE_URL}{MESSAGE_ENDPOINT}{CONTENT}?from_date=0&to_date={to_timestamp_ms}'
    id = 1
    try:
        response = requests.get(url, headers=HEADERS, cookies=COOKIE,verify=False)
        data = response.json()
        items = []
        for result in data.get("results", []):
            item = {
                "id" : id,
                "geoip": {
                    "continent_code": result.get("geoip", {}).get("continent_code"),
                    "country_code2": result.get("geoip", {}).get("country_code2"),
                    "country_code3": result.get("geoip", {}).get("country_code3"),
                    "country_name": result.get("geoip", {}).get("country_name"),
                    "ip": result.get("geoip", {}).get("ip"),
                    "latitude": result.get("geoip", {}).get("latitude"),
                    "location": result.get("geoip", {}).get("location"),
                    "longitude": result.get("geoip", {}).get("longitude"),
                    "timezone": result.get("geoip", {}).get("timezone")
                },
            }
            id += 1
            items.append(item)
        return {
            "code" : str(response.status_code),
            "data" : items,
            "msg" : "success"
        }
    except Exception as err:
        return {
            "code" : str(response.status_code),
            "data" : {},
            "msg" : str(err)
        }

# @r.route("/post/diff_type_count", methods=['POST'])
# def get_diff_type_count():
#     # 尝试获取session 
#     global session
#     if session is None:
#         print("Session是空,尝试登录")
#         session = loginn()
#     elif not is_session_valid(session):
#         print("Session失效,重新登录")
#         session = loginn()
#     else:
#         print("session良好重复使用")

#     attack_types_count = {}
#     # 请求参数模板
#     params_template = {
#         "date": '',
#         "end_date": '',
#         "waf_url": '', 
#         "waf_rule_id": '',
#         "waf_rule_type": '',  # 会被替换为攻击类型
#         "action": '',
#         "waf_rule_msg": '',
#         "sip": '',
#         "dip": '',
#     }

#     url = f"http://{ip}/webui/?g=log_fw_waf_scan_jsondata"
#     data = request.json
#     #进行日期的选择读取到日期
#     #定义攻击类型
#     attack_types = [
#     "HTTP协议检查", "通用攻击", "SQL注入攻击", "XSS攻击", "目录遍历",
#     "恶意扫描与爬虫", "木马攻击", "会话劫持", "敏感信息泄露", "服务器防护", "CMS漏洞防护"
# ]
#     for attack_type in attack_types:
#         # print(f"正在获取 {attack_type} 的数据...")
#         # 更新请求参数，设置当前攻击类型, 时间
#         params = params_template.copy()
#         params["date"] = data.get('date', '')
#         params["end_date"] = data.get('end_date', '')
#         params["waf_rule_type"] = attack_type
#         params_encoded = urlencode(params)
#         full_url = f"{url}&{params_encoded}"
#         post_data = {
#             "page": 1,
#             "rows": 1
#         }
#         # 发送请求并获取结果
#         try:
#             response = session.post(full_url, data=post_data)
#             print(response.text)
#             if response.text:
#                 re_data = json.loads(response.text)
#             else:
#                 # 处理空响应情况
#                 print("响应体为空")
#             response.raise_for_status()  # 如果响应状态码不是 200，会抛出异常
#             re_data = json.loads(response.text)  # 响应数据是 JSON 格式
#             total_attacks = re_data.get('page', {}).get('total', 0)
#             attack_types_count[attack_type] = total_attacks
#         except requests.RequestException as e:
#             # print(f"请求失败: {e}")
#             #000代表请求错误
#             attack_types_count[attack_type] = 000 
#     return {
#             "code" : str(response.status_code),
#             "data" : attack_types_count,
#             "msg" : "success"
#         }
    # return jsonify(data=attack_types_count)
