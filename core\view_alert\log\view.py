from . import *
import requests
from urllib.parse import urlencode
import json
# 从utils导入loginn
from core.utils.loginn.login import loginn, is_session_valid
from core.utils.loginn.config import ip
from io import BytesIO
from zipfile import ZipFile
from flask import request, jsonify, send_file
session = None

BASE_URL = 'https://**************:443/'
#BASE_URL = 'https://*************:12601/'
MESSAGE_ENDPOINT = 'rest/rules/es/'
CONTENT = 'alerts_tail/'
TOKEN = '4d84a049bbb21b8b09cfe5ea5093b2bb8e4bc37f'
HEADERS = {
    'Authorization': f'Token {TOKEN}',
    'Content-Type': 'application/json',
    'x-requested-with': 'XMLHttpRequest'
}

COOKIE = {
    'Cookie': 'csrftoken=j2v9l0KmRd1S6QivxMPjFhEPXFZH2hXdXYrg8DGHXdpLPanNBwQiSsXKyGDVskxv; sessionid=n7gsswq0tunox9etyosdu1ntx4y39wa7'
}

@r.route("/query/log/history", methods=['GET'])
def query_log_history():
    page_size = request.args.get('page_size')
    page = request.args.get('page')
    url = f'{BASE_URL}rest/rules/history/?ordering=-date&page_size={page_size}&page={page}'
    try:
        response = requests.get(url, cookies=COOKIE, headers= HEADERS, verify=False)
        return {
            "code" : str(response.status_code),
            "data" : response.json(),
            "msg" : "success"
        }
    except Exception as err:
        return {
            "code" : str(response.status_code),
            "data" : {},
            "msg" : str(err)
        }
    

# # 按时间、类型、关键字查询监控日志
# @r.route("/post/monitor_log", methods=['POST'])
# def post_monitor_log():
#     # 尝试获取session 
#     global session
#     if session is None:
#         print("Session是空,尝试登录")
#         session = loginn()
#     elif not is_session_valid(session):
#         print("Session失效,重新登录")
#         session = loginn()
#     else:
#         print("session良好重复使用")

#     url_sys = f"http://{ip}/webui/?g=log_fw_system_jsondata"
#     url_opt = f"http://{ip}/webui/?g=log_fw_operate_jsondata"

#     data = request.json
#     params_sys = {
#             "date": data.get('date', ''),
#             "end_date": data.get('end_date', ''),
#             "content": data.get('content', ''),
#             "level_name": data.get('level_name', ''),
#             "cas_addr": data.get('cas_addr', '')
#         }
    
#     params_opt = {
#             "date": data.get('date', ''),
#             "end_date": data.get('end_date', ''),
#             "content": data.get('content', ''),
#             "level_name": data.get('level_name', ''),
#             "admin": data.get('admin', ''),
#             "admin_fuzzy": data.get('admin_fuzzy', ''),
#             "log_ip": data.get('log_ip', ''),
#             "cas_addr": data.get('cas_addr', '')
#         }
    
#     params_sys_encoded = urlencode(params_sys)
#     params_opt_encoded = urlencode(params_opt)
#     full_url_sys = f"{url_sys}&{params_sys_encoded}"
#     full_url_opt = f"{url_opt}&{params_opt_encoded}"
#     post_data = {
#             "page": data.get('page', 1),
#             "rows": data.get('rows', 1),
#         }
#     response_sys = session.post(full_url_sys, data=post_data)
#     response_opt = session.post(full_url_opt, data=post_data)
#     if response_sys.status_code == 200 & response_opt.status_code == 200:
#          # 解析响应数据
#         data_sys = json.loads(response_sys.text)
#         data_opt = json.loads(response_opt.text)
#         # 添加类别属性
#         for group in data_sys['group']:
#             group['category'] = 'system'  # 为每个日志项添加类别属性
#         for group in data_opt['group']:
#             group['category'] = 'operate'
#         # 合并数据
#         merged_data = {
#         'group': data_sys['group'] + data_opt['group'],  # 合并两个日志条目列表
#         'page': {
#             'total': data_sys['page']['total'] + data_opt['page']['total']  # 计算总数
#         }
#     }
#         return {
#             "code" : str(response_opt.status_code),
#             "data" : merged_data,
#             "msg" : "success"
#         }
#     else:
#        return {
#             "code" : str(response_opt.status_code),
#             "data" : {},
#             "msg" : "error"
#         }
      
# @r.route("/get/export_log", methods=['GET'])
# def get_export_log():
#     # 尝试获取session 
#     global session
#     if session is None:
#         print("Session是空,尝试登录")
#         session = loginn()
#     elif not is_session_valid(session):
#         print("Session失效,重新登录")
#         session = loginn()
#     else:
#         print("session良好重复使用")

#     # 从前端获取请求参数
#     stime = request.args.get('stime')
#     etime = request.args.get('etime')
#     max_count = request.args.get('max_count', '100000')  # 默认为100000
#     stime_hour = request.args.get('stime_hour')
#     etime_hour = request.args.get('etime_hour')

#      # 第一次请求获得操作日志
#     url_1 = f"http://{ip}/webui/"
#     params_1_operate = {
#         "g": "log_fw_export_notice",
#         "log_type": "operate",
#         "stime": stime,
#         "etime": etime,
#         "max_count": max_count,
#         "stime_hour": stime_hour,
#         "etime_hour": etime_hour
#     }
#     # 第二次请求获得系统日志
#     params_1_event = {
#         "g": "log_fw_export_notice",
#         "log_type": "event",
#         "stime": stime,
#         "etime": etime,
#         "max_count": max_count,
#         "stime_hour": stime_hour,
#         "etime_hour": etime_hour
#     }
#     # 发送第一次请求
#     response_1_operate = session.get(url_1, params=params_1_operate)
#     response_1_event = session.get(url_1, params=params_1_event)
#     # 检查响应是否为1，表示操作成功
#     if response_1_operate.text == "1" and response_1_event.text == "1":
#         # 第二次请求的参数
#         url_2_operate = f"http://{ip}/webui/"
#         params_2_operate = {
#             "g": "log_fw_export",
#             "log_type": "operate"
#         }
        
#         params_2_event = {
#             "g": "log_fw_export",
#             "log_type": "event"
#         }
#         response_2_operate = session.get(url_2_operate, params=params_2_operate)
#         response_2_event = session.get(url_2_operate, params=params_2_event)

#         # 如果响应为ZIP文件
#         if response_2_operate.status_code == 200 & response_2_event.status_code == 200:
#             combined_zip = BytesIO()
#             with ZipFile(combined_zip, 'w') as zip_out:
#                 # 合并第一个ZIP文件 (operate)
#                 with ZipFile(BytesIO(response_2_operate.content), 'r') as zip_ref_2:
#                     for file_info in zip_ref_2.infolist():
#                         zip_out.writestr(file_info, zip_ref_2.read(file_info))
                
#                  # 合并第二个ZIP文件 (event)
#                 with ZipFile(BytesIO(response_2_event.content), 'r') as zip_ref_4:
#                     for file_info in zip_ref_4.infolist():
#                         zip_out.writestr(file_info, zip_ref_4.read(file_info))
            
#             # 将ZIP文件直接返回给前端进行下载
#             combined_zip.seek(0)  # 移动到文件的开始位置
#             return send_file(combined_zip, as_attachment=True, attachment_filename="logs.zip", mimetype="application/zip")
#         else:
#             return jsonify({"error": "Failed to fetch ZIP file"}), 400
#     else:
#         return jsonify({"error": "Failed to initiate export notice"}), 400

