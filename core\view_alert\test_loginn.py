import requests
from urllib.parse import urlencode
import json
from core.utils.loginn.login import loginn  # 从utils导入loginn

# 调用 loginn 函数获取 session
session = loginn()

# 进行操作日志的获取
url = "http://**************/webui/?g=log_fw_operate_jsondata"
test_params = {
    "date": "",
    "end_date": "",
    "content": "",
    "level_name": "",
    "admin": "",
    "admin_fuzzy": "",
    "log_ip": "",
    "cas_addr": ""
}
params = urlencode(test_params)
full_url = f"{url}&{params}"
test_data = {
    "page": 1,
    "rows": 20,
}

try:
    new_response = session.post(full_url, data=test_data)
    new_response.raise_for_status()  # 检查请求是否成功
    data = json.loads(new_response.text)
    formatted_json = json.dumps(data, indent=4, ensure_ascii=False)
    print(formatted_json)
except requests.exceptions.RequestException as e:
    print(f"请求失败，错误信息：{e}")
    print("请检查网页链接的合法性，或稍后重试。")
