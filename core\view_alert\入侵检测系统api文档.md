
# SELKS 发现的API端点

| api端点                                                               | 说明        |
| ------------------------------------------------------------------- | --------- |
| /rest/rules/es/alerts_tail/?ordering=-timestamp&page_size=10&page=1 | 警报信息      |
| /rules/source/3/update                                              | 增加规则集     |
| /rules/source/add_public                                            | 现有的可添加规则集 |

-------------
# IDS/IPS 接口列表

| 接口 ID |             接口 API             | 请求方式 |     功能说明      |
| :---: | :----------------------------: | :--: | :-----------: |
|   1   |         /alert/ruleset         | GET  | 获取正在使用的ids规则集 |
|   2   |       /alert/source_list       | GET  |  获得可增加的规则集列表  |
|   3   |       /alert/events/list       | GET  |   获取警报详细信息    |
|   4   |      /alert/events/count       | GET  |    获取警报数量     |
|   5   |  /alert/ruleset/download/list  | GET  |  获取可下载的规则集列表  |
|   6   | /alert/ruleset/download/export | GET  |   下载指定的规则集    |

## 1.  获取正在使用的规则集

API：GET `/alert/ruleset`
请求参数：
```json
{
	...
	"page" : 2  // 规则集第几页面
	...
}
```

返回参数：
```json
{
    "data": [
        {
            "category": {
                "descr": "",
                "name": "emerging-exploit",  // 规则所属分类
                "pk":  //规则所属分类的主键
            },
            "desc": "",  // 规则描述
            "versions": [
                {
                    "content": "alert tcp $EXTERNAL_NET any -> $HOME_NET 23",  // 具体规则内容
                    "created": "",  // 规则创建时间
                    "id": ,  // 规则id
                    "imported_date": "", // 规则导入ids时间
                    "updated": "",  // 更新规则时间
                    "updated_date": ""  // 更新导入ids规则时间
                }
            ]
        }
    ]
    ...
}
```

## 2. 获得可增加的规则集列表

API : `alert/source_list`
响应：
```json
{
	{
	"desrciption": "Proofpoint ET Open is a timely and accurate rule set for detecting and blocking advanced threats\n",
	"id": 1,
	"name": "et/open",
	"summary": "Emerging Threats Open Ruleset"
	}
}
```


## 3. 获取警报详细信息

API : `alert/events/list`
请求参数：
```json
{
	"time" : [hour, day, week, month] (default 1)
- optional
	"page_size": ""
	"page" : ""
	"qfilter" : ""
}
```
响应自查询时间段内的告警详细信息，以下为实例：
```json
{
    "count": 1,
    "results": [
        {
            "alert": {
                "action": "allowed",
                "category": "Potentially Bad Traffic",
                "metadata": {
                    "confidence": [
                        "Medium"
                    ],
                    "created_at": [
                        "2010_09_23"
                    ],
                    "signature_severity": [
                        "Informational"
                    ]
                },
                "rev": 7,
                "severity": 2,
                "signature": "GPL ATTACK_RESPONSE id check returned root",
                "signature_id": 2100498
            },
            "app_proto": "http",
            "capture_file": "/var/log/suricata/fpc//log-1741937897-6.pcap",
            "dest_ip": "**************",
            "dest_port": 58714,
            "direction": "to_client",
            "ether": {
                "dest_mac": "bc:24:11:bf:2f:ef",
                "src_mac": "3e:f8:21:4a:03:44"
            },
            "event_type": "alert",
            "flow": {
                "bytes_toclient": 876,
                "bytes_toserver": 496,
                "dest_ip": "*************",
                "dest_port": 80,
                "pkts_toclient": 5,
                "pkts_toserver": 6,
                "src_ip": "**************",
                "src_port": 58714,
                "start": "2025-03-14T07:56:10.004145+0000"
            },
            "flow_id": 580755219552391,
            "geoip": {
                "continent_code": "NA",
                "country_code2": "US",
                "country_code3": "US",
                "country_name": "United States",
                "ip": "*************",
                "latitude": 37.751,
                "location": {
                    "lat": 37.751,
                    "lon": -97.822
                },
                "longitude": -97.822,
                "timezone": "America/Chicago"
            },
            "in_iface": "ens18",
            "proto": "TCP",
            "src_ip": "*************",
            "src_port": 80,
            "stream": 1,
            "tags": [
                "_geoip_lookup_failure"
            ],
            "timestamp": "2025-03-14T07:56:10.165Z",
            "type": "SELKS"
        }
    ]
}
```
## 4. 获取警报数量

API : `/alert/events/count`
请求参数：
```json
{
	"time" : [hour, day, week, month] (default 1)
}
```

响应：
```json
{
    "alert_count": 2, // 当前请求周期内的警报数量
    "pre_alert_count": 0 // 上个周期的警报数量
}
```

## 5. 获取可下载的规则集列表

API : `/alert/ruleset/download/list`
响应：
```json
{
    "count": 2,
    "results": [
        {
            "name": "Default ruleset",
            "pk": 1,
            "rules_count": 41758,
            "sources": [  // 规则源的主键
                1,
                2,
                12,
                14
            ]
        },
        {
            "name": "test",
            "pk": 2,
            "rules_count": 6848,
            "sources": [
                1,
                5,
                6,
                7,
                12
            ]
        }
    ]
}
```
## 6. 下载指定的规则集

API : `/ruleset/download/export`
请求参数：
```json
{
	"pk" : 2  // 请求下载的列表规则集id
}
```

响应：
```json
// 返回 .tgz 后缀压缩文件
```
# 状态码规范

| 状态码 | 事件        |
| --- | --------- |
| 400 | 缺少对应字段    |
| 404 | 未找到字段或无响应 |
| 500 | 操作失败      |
| 200 | 操作成功      |


H3C防火墙api接口
# api接口文档

|接口ID |接口api    |请求方式   |功能说明   |
|----   |-----      |----   |----   |
|1      |/get/userflow|GET|得到用户的流量数据|
|2      |/post/systemlog|POST|得到系统日志|
|3      |/post/operationlog|POST|得到操作日志|
|4      |/post/wafweb|POST|得到网络防护日志最后有数量|
|5       |/get/                       |GET       |获取规则策略                   |
|       |                       |           |               |

1的返回结果示例：
```JSON
{
    "code": 0,
    "data": "{\"group\":[]}",
    "msg": "Success"
}
```
2的POST样例：
```
{
    "date": "2025-03-17 00:00",             // 开始时间
    "end_date": "2025-03-17 18:59",         // 结束时间
    "content": "",                          // 事件内容
    "level_name": "0,1,2,3,4,5,6,7",        // 等级从0到7以此是紧急 告警 严重 错误 警告 通知 信息 调试
    "cas_addr": "",                         // 页码
    "page": "1",                            // 每页显示行数
    "rows" : "1"
}
```
2的返回结果示例：
```JSON
{
    "code": 0,
    "data": {
        "group": [
            {
                "content": "会话超时，用户 admin@*************28 退出WEB",
                "id": "98",
                "log_pri": "5",
                "log_time": "2025-03-17 18:47:21"
            }
        ],
        "page": {
            "total": 6
        }
    },
    "msg": "Success"
}
```
3的post请求样例
```
{
    "date": "2025-03-17 00:00",
    "end_date": "2025-03-17 18:59",
    "content": "",
    "level_name": "0,1,2,3,4,5,6,7",
    "admin":"admin",                        // 操作管理员
    "admin_fuzzy":"",                       // 模糊查询
    "log_ip":"*************",               // 操作员ip
    "cas_addr": "",
    "page": "1",
    "rows" : "1"
}
```



3的返回结果示例：
```JSON
{
    "code": 0,
    "data": {
        "group": [
            {
                "content": "提交 web防护策略 配置 => name=test, enable=1, ",
                "id": "27",
                "log_ip": "*************",
                "log_pri": "6",
                "log_time": "2025-03-17 18:03:59",
                "log_user": "admin",
                "managestyle": "WEB",
                "reason": "提交",
                "result": "成功"
            }
        ],
        "page": {
            "total": 9
        }
    },
    "msg": "Success"
}
```
4的post样例
```
{
    "date": "2025-03-17 00:00",             // 开始时间
    "end_date": "2025-03-18 23:59",         // 结束时间
    "waf_url": "",                          // WAF规则URL
    "waf_rule_id": "",                      // 规则ID
    "waf_rule_type": "SQL注入攻击",          // 事件类型（可选类型包括：全部、HTTP协议检查、通用攻击、SQL注入攻击、XSS攻击、目录遍历、恶意扫描与爬虫、木马攻击、会话劫持、敏感信息泄露、服务器防护、CMS漏洞防护、空着是全部）
    "action": "2",                          // 处理动作（1是允许，2是拒绝、0是全部也可以不填）
    "waf_rule_msg": "1",                    // 事件描述
    "sip": "***************",               // 源地址
    "dip": "************",                  // 目的地址
    "page": "1",                            //页码
    "rows": "1"                             // 每页显示行数
}



{
    "date": "2025-03-17 00:00",
    "end_date": "2025-03-18 23:59",
    "waf_url": "",
    "waf_rule_id": "",
    "waf_rule_type": "SQL注入攻击",
    "action": "2",
    "waf_rule_msg": "1",
    "sip": "***************",
    "dip": "************",
    "page": "1",
    "rows": "1"
}
```

4的返回样例
```
{
    "code": 0,
    "data": {
        "group": [
            {
                "0": 19,
                "1": "2025-03-17 23:27:47",
                "10": 49254,
                "11": 5000,
                "12": "拒绝",
                "13": "Deny",
                "14": "903109",
                "15": "SQL注入攻击",
                "16": "SQL Injection Attacks",
                "17": "请求体中包含Mysql延迟注入尝试 1/2, 攻击字符串\"sleep(\", 原始字符串\"username=admin&password=password') and(select 5396 from(select(sleep(5)))naue) and('xfah'='xfah\"",
                "18": "MYSQL time-based in request body 1/2, Attack string \"sleep(\", Source string \"username=admin&password=password') and(select 5396 from(select(sleep(5)))naue) and('xfah'='xfah\"",
                "19": "局域网",
                "2": 4,
                "20": "",
                "21": "",
                "22": "",
                "23": "",
                "24": "",
                "3": "test",
                "4": "POST",
                "5": "http://************:5000/login",
                "6": "00:0c:29:a4:48:e1",
                "7": "00:0c:29:82:83:c9",
                "8": "***************",
                "9": "************",
                "id": 19,
                "log_pri": 4,
                "log_time": "2025-03-17 23:27:47",
                "waf_city": "",
                "waf_city_en": "",
                "waf_country": "局域网",
                "waf_country_en": "",
                "waf_dip": "************",
                "waf_dmac": "00:0c:29:82:83:c9",
                "waf_dport": 5000,
                "waf_method": "POST",
                "waf_plyname": "test",
                "waf_province": "",
                "waf_province_en": "",
                "waf_rule_action": "拒绝",
                "waf_rule_action_en": "Deny",
                "waf_rule_id": "903109",
                "waf_rule_msg": "请求体中包含Mysql延迟注入尝试 1/2, 攻击字符串\"sleep(\", 原始字符串\"username=admin&password=password') and(select 5396 from(select(sleep(5)))naue) and('xfah'='xfah\"",
                "waf_rule_msg_en": "MYSQL time-based in request body 1/2, Attack string \"sleep(\", Source string \"username=admin&password=password') and(select 5396 from(select(sleep(5)))naue) and('xfah'='xfah\"",
                "waf_rule_type": "SQL注入攻击",
                "waf_rule_type_en": "SQL Injection Attacks",
                "waf_sip": "***************",
                "waf_smac": "00:0c:29:a4:48:e1",
                "waf_sport": 49254,
                "waf_url": "http://************:5000/login"
            }
        ],
        "page": {
            "total": 15
        }
    },
    "msg": "Success"
}
```

5的测试样例
```
{
    "page": "1",
    "rows" : "1"
}
```


5的返回样例
```
{
    "code": 0,
    "data": {
        "group": [
            {
                "action": "2",
                "active": "0",
                "app_names": "全部",
                "av_profile": "0",
                "ddi_term_type": {
                    "group": {
                        "description": "any",
                        "name": "any"
                    }
                },
                "description": "",
                "description_policy": "",
                "dst_addr": {
                    "group": [
                        {
                            "name": "ChinaEducation"
                        },
                        {
                            "name": "ChinaMobile"
                        }
                    ]
                },
                "dst_zone": "any",
                "enable": "0",
                "ex_list": "",
                "first_match": "0",
                "id": "1",
                "in_list": "",
                "ips_profile": "0",
                "is_add": "0",
                "log": "0",
                "match_cnt": "1",
                "members": "any",
                "nbc_policy": "0",
                "policy_group": "default",
                "schedule": "always",
                "service_items": {
                    "group": {
                        "name": "any"
                    }
                },
                "src_addr": {
                    "group": [
                        {
                            "name": "ChinaUnicom"
                        },
                        {
                            "name": "private"
                        }
                    ]
                },
                "src_zone": "any",
                "term_type": {
                    "group": {
                        "name": "any"
                    }
                },
                "timeout": "0",
                "tunnel_name": "",
                "xml_app_items": {
                    "group": {
                        "id": "0"
                    }
                },
                "xml_tag_items": ""
            }
        ],
        "page": {
            "count": "1",
            "current": "1",
            "total": "1"
        }
    },
    "msg": "Success"
}

```