import pymysql
from flask import request, jsonify
from datetime import datetime
from . import *

DB_CONFIG = {
    "host": ServerHost,
    "user": MysqlUSER,
    "password": MysqlPWD,
    "database": MysqlBase,
    "charset": SQLCharset,
    "cursorclass": pymysql.cursors.DictCursor
}

def get_db_connection():
    return pymysql.connect(**DB_CONFIG)

def call_deepseek_model(user_prompt):
    import requests
    system_prompt = "你是一位网络安全事件决策专家，能够根据我提供的安全事件和可选的程序与程序行为，给出且只给出格式化后的应对策略"
    url = "https://api.deepseek.com/chat/completions"
    headers = {
        "Authorization": dp_apikey,
        "Content-Type": "application/json"
    }
    data = {
        "model": "deepseek-chat",
        "messages": [
            {"role": "system", "content": system_prompt},
            {"role": "user", "content": user_prompt}
        ],
        "stream": False
    }
    response = requests.post(url, headers=headers, json=data)
    response.raise_for_status()
    return response.json()["choices"][0]["message"]["content"]

def generate_prompt(event_name, event_description, complexity_level, format_output, decision_count, required_steps, optimize_solution, deep_reasoning):
    action_list = """1 base64 加密 encryption
2 base64 解密 decrypt
3 dingding 钉钉通知 send
4 email 邮件发送 send
5 feishu 飞书通知 send
6 h3c 添加黑名单 add_blacklist
7 ip IP查询 ip
8 linux 执行命令 execute
9 md5 加密 encryption
10 mysql 查询 query
11 mysql 增删改 update
12 nmap 端口扫描 scan
13 redis GET get
14 redis SET set
15 redis DEL delete
16 redis 清空数据-单个DB flushdb
17 redis 清空数据-全部DB flushall
18 url 生成 make
19 windows 执行命令 execute
"""
    base_prompt = f"目前遇到的攻击事件名称：{event_name}。事件描述：{event_description}。\n"
    base_prompt += "请从下面可选程序行为清单中，组合你认为的最好的应对策略。\n"
    # 必须包含的行为
    if required_steps:
        base_prompt += f"请务必包含以下程序行为步骤（按编号），若括号内为空则没有必须步骤要求，注意你是一名安全专家，决策中不应该只有我要求的行为，你应该自行决断: {', '.join(map(str, required_steps))}。\n"
    if complexity_level == 1:
        base_prompt += "请尽量给出完善的决策链，确保决策方案有效性。\n"
    elif complexity_level >= 3:
        base_prompt += "深度思考给出你认为的最好的决策链，长度适中，不要只有两三项\n"
    if decision_count > 1:
        base_prompt += f"请提供 {decision_count} 套不同的完整决策方案，每套方案之间用换行分隔，命名为方案一、方案二等，便于阅读，只有一套的话则不需要，返回且仅返回一条决策链的信息\n"
    if optimize_solution:
        base_prompt += "请综合考虑最佳应对效果，再给出了全部决策后加上推荐最优解。\n"
    if deep_reasoning:
        base_prompt += "请结合深度推理分析思考应对链条的合理性。\n"
    if format_output == 0:
        base_prompt += "决策应对方案请严格使用格式返回：事件名称->开始->程序 行为1->程序 行为2->...->结束。其中程序直接用清单中程序名，行为直接用清单中所选程序对应中文名，返回且仅返回决策链。\n"
    else:
        base_prompt += "请将决策方案按照下面自然语言方式返回，如：1. 事件是：xx；2. 开始；3. 调用程序xx 执行xx...\n"
    base_prompt += "\n程序清单格式如下，第一列行号，第二列程序名，第三列程序可选行为，第四列程序英文名（请不要使用）。清单具体内容如下：\n"
    base_prompt += action_list
    return base_prompt

@r.route("/alert/decision", methods=['POST'])
def alert_decision():
    connection = None
    try:
        data = request.json or {}
        alert_id = data.get("alert_id")
        complexity_level = int(data.get("complexity_level", 1))
        decision_count = int(data.get("decision_count", 1))
        required_steps = data.get("required_steps", []) or []
        optimize_solution = int(data.get("optimize_solution", 1))
        deep_reasoning = int(data.get("deep_reasoning", 0))

        if not alert_id:
            return jsonify({"code": 400, "msg": "alert_id为必填", "data": None})

        connection = get_db_connection()
        with connection.cursor() as cursor:
            # 查找w5_alert
            cursor.execute("SELECT * FROM w5_alert WHERE alert_id = %s", (alert_id,))
            alert_row = cursor.fetchone()
            if not alert_row:
                return jsonify({"code": 404, "msg": "alert_id不存在", "data": None})

            event_name = alert_row["attack_type"]
            event_description = f"严重性: {alert_row['severity']}, 签名: {alert_row['signature']}"

            # 生成deepseek提示词
            prompt_natural = generate_prompt(
                event_name, event_description, complexity_level, 1, decision_count, required_steps, optimize_solution, deep_reasoning
            )
            prompt_structured = generate_prompt(
                event_name, event_description, complexity_level, 0, decision_count, required_steps, optimize_solution, deep_reasoning
            )

        # 调用deepseek
        response_natural = call_deepseek_model(prompt_natural)
        response_structured = call_deepseek_model(prompt_structured)

        # 更新is_decided
        with connection.cursor() as cursor:
            cursor.execute("UPDATE w5_alert SET is_decided = 1 WHERE alert_id = %s", (alert_id,))
            # 获取修改后的alert行
            cursor.execute("SELECT * FROM w5_alert WHERE alert_id = %s", (alert_id,))
            updated_alert = cursor.fetchone()

        # 插入w5_analysis和w5_alert_analysis前，先删除已存在的记录
        now = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        with connection.cursor() as cursor:
            # 删除w5_analysis中已有的analysis_id=alert_id的记录
            cursor.execute("DELETE FROM w5_analysis WHERE analysis_id = %s", (alert_id,))
            # 删除w5_alert_analysis中已有的analysis_id=alert_id或alert_id=alert_id的记录
            cursor.execute("DELETE FROM w5_alert_analysis WHERE analysis_id = %s OR alert_id = %s", (alert_id, alert_id,))

            # 插入w5_analysis
            insert_analysis_sql = """
            INSERT INTO w5_analysis (analysis_id, timestamp, attack_summary, affected_systems, potential_risk, recommended_actions, notes, status, create_time, update_time)
            VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
            """
            cursor.execute(insert_analysis_sql, (
                alert_id, now, '', '', '', response_structured, response_natural, 0, now, now
            ))
            # 插入w5_alert_analysis
            insert_alert_analysis_sql = "INSERT INTO w5_alert_analysis (analysis_id, alert_id, create_time) VALUES (%s, %s, %s)"
            cursor.execute(insert_alert_analysis_sql, (alert_id, alert_id, now))
            connection.commit()

        result_data = {
            "natural_language_decision": response_natural,
            "structured_decision": response_structured,
            "updated_alert": updated_alert
        }
        return jsonify({"code": 200, "msg": "决策生成并已记录", "data": result_data})

    except pymysql.MySQLError as e:
        return jsonify({"code": 500, "msg": f"数据库错误: {e}", "data": None})
    except Exception as e:
        return jsonify({"code": 500, "msg": f"服务器错误: {str(e)}", "data": None})
    finally:
        try:
            if connection:
                connection.close()
        except NameError:
            pass
