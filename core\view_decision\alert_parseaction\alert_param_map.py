# dynamic_field_map 和 static_content_map 可直接导入使用

dynamic_field_map = {
    "md5": {
        "encryption": {
            "text": "signature"  # w5_alert.signature
            # type字段无合适w5_alert字段，建议静态赋值
        }
    },
    "nmap": {
        "scan": {
            "target": "destination_ip",  # w5_alert.destination_ip
            "ports": "destination_port",  # w5_alert.destination_port
            "protocol": "protocol"        # w5_alert.protocol
        }
    },
    "bankcard": {
        "query": {
            "card": "source_ip"   # 假设用source_ip作为卡号示例
        }
    },
    "serverjiang": {
        "send": {
            "key": "alert_id",    # w5_alert.alert_id
            "text": "attack_type",  # w5_alert.attack_type
            "desp": "description"   # w5_alert.description
        }
    },
    "phone": {
        "query": {
            "phone": "source_ip"  # 假设用source_ip作为手机号示例
        }
    },
    "otx": {
        "is_ioc": {
            "ioc": "source_ip"    # w5_alert.source_ip
            # api_key, pulse_id无合适字段
        }
    },
    "mysql": {
        "query": {
            "host": "source_ip",
            "port": "source_port"
            # 其它建议静态
        },
        "update": {
            "host": "source_ip",
            "port": "source_port"
        }
    },
    "zhfc": {
        "make": {
            "text": "description"  # w5_alert.description
        }
    },
    "linux": {
        "execute": {
            "host": "source_ip",
            "port": "source_port"
            # user, passwd, shell建议静态
        }
    },
    "redis": {
        "get": {
            "host": "source_ip",
            "port": "source_port"
            # db, password, key建议静态
        },
        "set": {
            "host": "source_ip",
            "port": "source_port"
        },
        "delete": {
            "host": "source_ip",
            "port": "source_port"
        },
        "flushdb": {
            "host": "source_ip",
            "port": "source_port"
        },
        "flushall": {
            "host": "source_ip",
            "port": "source_port"
        }
    },
    "dingding": {
        "send": {
            "msg": "description"  # w5_alert.description
            # access_token建议静态
        }
    },
    "windows": {
        "execute": {
            "host": "source_ip",
            "port": "source_port"
        }
    },
    "qq": {
        "query": {
            "qq": "source_ip"  # 假设用source_ip
        }
    },
    "intelligent_decision-making": {
        "send_event": {
            "event_id": "alert_id",
            "event_type": "attack_type",
            "severity": "severity",
            "detected_at": "timestamp",
            "details": "description"
        },
        "get_response_suggestions": {
            "event_id": "alert_id"
        },
        "update_event_status": {
            "event_id": "alert_id"
        }
    },
    "email": {
        "send": {
            "text": "description",
            "title": "attack_type"
            # 其它建议静态
        }
    },
    "icp": {
        "query": {
            "domain": "source_ip"  # 假设用source_ip
        }
    },
    "base64": {
        "encryption": {
            "text": "description"
        },
        "decrypt": {
            "text": "description"
        }
    },
    "firewalld": {
        "add_port_to_zone": {
            "host": "source_ip",
            "port": "source_port",
            "target_port": "destination_port"
            # user, passwd, zone静态
        },
        "remove_port_from_zone": {
            "host": "source_ip",
            "port": "source_port",
            "target_port": "destination_port"
        },
        "reload_firewalld": {
            "host": "source_ip",
            "port": "source_port"
        },
        "block_ip": {
            "host": "source_ip",
            "port": "source_port",
            "ip_address": "destination_ip"
        }
    },
    "feishu": {
        "send": {
            "msg": "description"
            # hook_uuid静态
        }
    },
    "fscan": {
        "scan": {
            "target": "destination_ip"
            # ports静态
        }
    },
    "splunk": {
        "scan": {
            "user": "owner",
            "body": "description"
            # domain, passwd静态
        }
    },
    "suricata": {
        "process_logs": {
            "log_type": "attack_type"
        }
    },
    "es": {
        "scan": {
            "host": "source_ip",
            "port": "source_port"
            # index, body静态
        }
    },
    "helloworld": {
        "hello_world": {
            "name": "owner"
        }
    },
    "clickhouse": {
        "query": {
            "user": "owner"
            # url, passwd, db, sql静态
        }
    },
    "url": {
        "make": {
            "url": "destination_ip"
        }
    },
    "ip": {
        "ip": {
            "ip": "source_ip"
        }
    },
    "threatbook": {
        "ip_query": {
            "ip": "source_ip"
            # key静态
        },
        "ip_reputation": {
            "ip": "source_ip"
        },
        "domain_query": {
            "domain": "destination_ip"
        }
    },
    "myhelloworld": {
        "my_hello_world": {
            "name1": "owner"
            # name2静态
        }
    },
    "whois": {
        "query": {
            "domain": "destination_ip"
        }
    },
    "honming": {
        "query": {
            "domain": "destination_ip"
        }
    }
}

static_content_map = {
    "md5": {
        "encryption": {
            "type": "md5"
        }
    },
    "nmap": {
        "scan": {
            "ports": "80,443",
            "protocol": "tcp"
        }
    },
    "bankcard": {
        "query": {
            "card": "622202xxxxxxxxxxx"
        }
    },
    "serverjiang": {
        "send": {
            "key": "your_serverjiang_key",
            "desp": "自动告警"
        }
    },
    "phone": {
        "query": {
            "phone": "***********"
        }
    },
    "otx": {
        "is_ioc": {
            "api_key": "otx_api_key",
            "pulse_id": "otx_pulse_id"
        }
    },
    "mysql": {
        "query": {
            "user": "root",
            "passwd": "password",
            "db": "testdb",
            "sql": "SELECT 1"
        },
        "update": {
            "user": "root",
            "passwd": "password",
            "db": "testdb",
            "sql": "UPDATE test SET value=1"
        }
    },
    "zhfc": {
        "make": {
            "text": "待分词内容"
        }
    },
    "linux": {
        "execute": {
            "user": "root",
            "passwd": "password",
            "shell": "ls"
        }
    },
    "redis": {
        "get": {
            "db": 0,
            "password": "",
            "key": "mykey"
        },
        "set": {
            "db": 0,
            "password": "",
            "key": "mykey",
            "value": "myvalue"
        },
        "delete": {
            "db": 0,
            "password": "",
            "key": "mykey"
        },
        "flushdb": {
            "db": 0,
            "password": ""
        },
        "flushall": {
            "password": ""
        }
    },
    "dingding": {
        "send": {
            "access_token": "dingding_token"
        }
    },
    "windows": {
        "execute": {
            "user": "Administrator",
            "passwd": "password",
            "cmd": "whoami"
        }
    },
    "qq": {
        "query": {
            "qq": "10000"
        }
    },
    "intelligent_decision-making": {
        "send_event": {
            "event_id": "evt-001",
            "event_type": "未知事件",
            "severity": "中",
            "detected_at": "2025-06-12 12:00:00",
            "details": "自动生成"
        },
        "get_response_suggestions": {
            "event_id": "evt-001"
        },
        "update_event_status": {
            "event_id": "evt-001",
            "new_status": "已处理"
        }
    },
    "email": {
        "send": {
            "host": "smtp.example.com",
            "port": 25,
            "user": "<EMAIL>",
            "passwd": "password",
            "encrypt": "ssl",
            "sender": "<EMAIL>",
            "to": "<EMAIL>",
            "type": "plain"
        }
    },
    "icp": {
        "query": {
            "domain": "example.com"
        }
    },
    "base64": {
        "encryption": {},
        "decrypt": {}
    },
    "firewalld": {
        "add_port_to_zone": {
            "user": "root",
            "passwd": "password",
            "zone": "trusted"
        },
        "remove_port_from_zone": {
            "user": "root",
            "passwd": "password",
            "zone": "trusted"
        },
        "reload_firewalld": {
            "user": "root",
            "passwd": "password"
        },
        "block_ip": {
            "user": "root",
            "passwd": "password"
        }
    },
    "feishu": {
        "send": {
            "hook_uuid": "your_feishu_hook"
        }
    },
    "fscan": {
        "scan": {
            "ports": "80,443"
        }
    },
    "splunk": {
        "scan": {
            "domain": "test.com",
            "passwd": "password"
        }
    },
    "suricata": {
        "process_logs": {}
    },
    "es": {
        "scan": {
            "index": "test-index",
            "body": "{}",
            "account": "es_user",
            "password": "es_pass"
        }
    },
    "helloworld": {
        "hello_world": {}
    },
    "clickhouse": {
        "query": {
            "url": "http://localhost:8123",
            "passwd": "password",
            "db": "default",
            "sql": "SELECT 1"
        }
    },
    "url": {
        "make": {
            "url": "http://example.com"
        }
    },
    "ip": {
        "ip": {
            "ip": "127.0.0.1"
        }
    },
    "threatbook": {
        "ip_query": {
            "key": "tb_key"
        },
        "ip_reputation": {
            "key": "tb_key"
        },
        "domain_query": {
            "key": "tb_key"
        }
    },
    "myhelloworld": {
        "my_hello_world": {
            "name2": "world"
        }
    },
    "whois": {
        "query": {
            "domain": "example.com"
        }
    },
    "honming": {
        "query": {
            "domain": "example.com"
        }
    }
}
