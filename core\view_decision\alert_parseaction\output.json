{"name": "DDoS", "remarks": "", "node_info": {"node1": {"app_id": "start", "app_type": 0}, "node2": {"app_id": "end", "app_type": 0}, "node3": {"app_id": "h3c", "app_type": 1, "information": {"action": [{"name": "添加黑名单", "func": "add_blacklist"}], "app_dir": "h3c", "args": {"add_blacklist": [{"key": "ip", "type": "text", "required": true}, {"key": "age", "type": "number", "required": false}]}, "description": "自动登录H3C系统并添加IP到黑名单", "icon": "h3c/icon.png", "identification": "w5soar", "is_public": true, "name": "H3C黑名单管理", "type": "安全防护", "version": "0.1", "data": {"node_name": "H3C黑名单管理", "action": "add_blacklist", "action_name": "添加黑名单", "description": "自动登录H3C系统并添加IP到黑名单", "ip": "**********", "age": 3600}}}, "node4": {"app_id": "linux", "app_type": 1, "information": {"action": [{"name": "执行命令", "func": "execute"}], "app_dir": "linux", "args": {"execute": [{"key": "host", "type": "text", "required": true}, {"key": "port", "type": "number", "required": true, "default": 22}, {"key": "user", "type": "text", "required": true, "default": "root"}, {"key": "passwd", "type": "text", "required": true}, {"key": "shell", "type": "textarea", "required": true}]}, "description": "Linux SSH 客户端，执行远程命令", "icon": "linux/icon.png", "identification": "w5soar", "is_public": true, "name": "Linux远程命令", "type": "命令执行", "version": "0.1", "data": {"node_name": "Linux远程命令", "action": "execute", "action_name": "执行命令", "description": "Linux SSH 客户端，执行远程命令", "host": "**********", "port": 54321, "user": "root", "passwd": "password", "shell": "ls"}}}, "node5": {"app_id": "dingding", "app_type": 1, "information": {"action": [{"name": "钉钉通知", "func": "send"}], "app_dir": "dingding", "args": {"send": [{"key": "access_token", "type": "text", "required": true}, {"key": "msg", "type": "text", "required": true}]}, "description": "钉钉消息通知", "icon": "dingding/icon.png", "identification": "w5soar", "is_public": true, "name": "钉钉通知", "type": "消息通知", "version": "0.1", "data": {"node_name": "钉钉通知", "action": "send", "action_name": "钉钉通知", "description": "钉钉消息通知", "access_token": "2e6ac91ee756ff39e74d9105a9aabe8cec59c8d10747a061a02d08562e5411fc", "msg": "1. 本次决策处理效果简述：\n成功执行DDoS防御流程，完成H3C黑名单添加、Linux命令执行、钉钉和邮件通知全链路自动化处置。\n\n2. 处理涉及的关键参数：\n- H3C黑名单IP：**********（有效期3600秒）\n- Linux命令：在**********:54321执行\"ls\"\n- 通知内容：\"DDoS Flood\"\n\n3. 处理方式简介：\n自动封禁攻击IP→验证服务器状态→双通道告警通知（钉钉+邮件）\n\n4. 运维友好通知内容：\n【紧急】检测到DDoS攻击，已自动封禁IP **********并完成服务检查，详情请查收钉钉/邮件。"}}}, "node6": {"app_id": "email", "app_type": 1, "information": {"action": [{"name": "邮件发送", "func": "send"}], "app_dir": "email", "args": {"send": [{"key": "host", "type": "text", "required": true}, {"key": "port", "type": "number", "required": true, "default": 25}, {"key": "user", "type": "text", "required": true}, {"key": "passwd", "type": "text", "required": true}, {"key": "encrypt", "type": "select", "required": true, "default": "none", "data": ["none", "tsl", "ssl"]}, {"key": "sender", "type": "text", "required": true}, {"key": "to", "type": "text", "required": true}, {"key": "title", "type": "text", "required": true}, {"key": "type", "type": "select", "required": true, "default": "text", "data": ["text", "html"]}, {"key": "text", "type": "textarea", "required": true}]}, "description": "可以发送邮件的APP", "icon": "email/icon.png", "identification": "w5soar", "is_public": true, "name": "E-Mail", "type": "消息通知", "version": "0.1", "data": {"node_name": "E-Mail", "action": "send", "action_name": "邮件发送", "description": "可以发送邮件的APP", "host": "smtp.163.com", "port": 465, "user": "<EMAIL>", "passwd": "UTJAgddhdBDsaU7m", "encrypt": "ssl", "sender": "<EMAIL>", "to": "<EMAIL>", "title": "DDoS", "type": "plain", "text": "1. 本次决策处理效果简述：\n成功执行DDoS防御流程，完成H3C黑名单添加、Linux命令执行、钉钉和邮件通知全链路自动化处置。\n\n2. 处理涉及的关键参数：\n- H3C黑名单IP：**********（有效期3600秒）\n- Linux命令：在**********:54321执行\"ls\"\n- 通知内容：\"DDoS Flood\"\n\n3. 处理方式简介：\n自动封禁攻击IP→验证服务器状态→双通道告警通知（钉钉+邮件）\n\n4. 运维友好通知内容：\n【紧急】检测到DDoS攻击，已自动封禁IP **********并完成服务检查，详情请查收钉钉/邮件。"}}}}, "edge_info": [{"source": {"cell": "node1", "port": "right"}, "target": {"cell": "node3", "port": "left"}}, {"source": {"cell": "node3", "port": "right"}, "target": {"cell": "node4", "port": "left"}}, {"source": {"cell": "node4", "port": "right"}, "target": {"cell": "node5", "port": "left"}}, {"source": {"cell": "node5", "port": "right"}, "target": {"cell": "node6", "port": "left"}}, {"source": {"cell": "node6", "port": "right"}, "target": {"cell": "node2", "port": "left"}}], "local_var_data": [], "controller_data": {}}