import os
import json
import pymysql
import requests
from flask import request, jsonify
from . import *

# 引入dynamic_field_map和static_content_map（请确保已正确导入/粘贴）

# 数据库配置
DB_CONFIG = {
    "host": ServerHost,
    "user": MysqlUSER,
    "password": MysqlPWD,
    "database": MysqlBase,
    "charset": SQLCharset,
    "cursorclass": pymysql.cursors.DictCursor
}

def get_db_connection():
    return pymysql.connect(**DB_CONFIG)

def call_deepseek_model(user_prompt):
    system_prompt = "你是一位网络安全事件决策专家，能够根据我提供的安全事件和可选的程序与程序行为，给出且只给出格式化后的应对策略"
    url = "https://api.deepseek.com/chat/completions"
    headers = {
        "Authorization": dp_apikey,  # 请确保dp_apikey已配置
        "Content-Type": "application/json"
    }
    data = {
        "model": "deepseek-chat",
        "messages": [
            {"role": "system", "content": system_prompt},
            {"role": "user", "content": user_prompt}
        ],
        "stream": False
    }
    response = requests.post(url, headers=headers, json=data)
    response.raise_for_status()
    return response.json()["choices"][0]["message"]["content"]

def parse_input_and_generate_json(input_string, start_node, end_node):
    steps = input_string.split("->")

    result = {
        "name": steps[0],  # 第一步内容作为 name
        "remarks": "",
        "node_info": {
            "node1": start_node,
            "node2": end_node
        },
        "edge_info": [],
        "local_var_data": [],
        "controller_data": {}
    }

    node_counter = 3  # node3 开始计数
    previous_node = "node1"  # 初始起点为 node1
    for step in steps[2:-1]:  # 跳过 第一步和最后一步
        try:
            app_id, action_desc = step.strip().split(" ", 1)
        except ValueError:
            raise ValueError(f"步骤 '{step}' 无法正确解析为 app_id 和 action 描述")

        base_dir = os.path.dirname(__file__)
        app_dir = os.path.join(base_dir, "../../../apps", app_id)
        app_json_path = os.path.join(app_dir, "app.json")
        if not os.path.exists(app_json_path):
            raise FileNotFoundError(f"{app_json_path} 文件不存在")

        with open(app_json_path, "r", encoding="utf-8") as f:
            app_config = json.load(f)

        node_key = f"node{node_counter}"

        actions = app_config.get("action", [])
        matched_action = None
        for action in actions:
            if action_desc == action.get("name", ""):
                matched_action = action
                break

        if not matched_action:
            raise ValueError(f"未找到与 '{action_desc}' 完全匹配的动作")

        action_func = matched_action.get("func", "")
        args = app_config.get("args", {}).get(action_func, [])

        data_data = {
            "node_name": app_config.get("name", ""),
            "action": action_func,
            "action_name": matched_action.get("name", ""),
            "description": app_config.get("description", "")
        }

        for arg in args:
            data_data[arg.get("key", "")] = f"@({node_key}.result)"

        result["node_info"][node_key] = {
            "app_id": app_id,
            "app_type": 1,
            "information": {
                "action": actions,
                "app_dir": app_id,
                "args": app_config.get("args", {}),
                "description": app_config.get("description", ""),
                "icon": f"{app_id}/icon.png",
                "identification": app_config.get("identification", ""),
                "is_public": app_config.get("is_public", True),
                "name": app_config.get("name", ""),
                "type": app_config.get("type", ""),
                "version": app_config.get("version", ""),
                "data": data_data
            }
        }

        result["edge_info"].append({
            "source": {"cell": previous_node, "port": "right"},
            "target": {"cell": node_key, "port": "left"}
        })

        node_counter += 1
        previous_node = node_key

    result["edge_info"].append({
        "source": {"cell": previous_node, "port": "right"},
        "target": {"cell": "node2", "port": "left"}
    })

    return result

def fill_params_with_mapping(output_data, alert_row):
    """
    按照 dynamic_field_map 和 static_content_map 填充 output_data 的参数
    """
    nodes = output_data.get("node_info", {})
    for node_id, node_info in nodes.items():
        information = node_info.get("information", {})
        data = information.get("data", {})
        if not data:
            continue
        app_id = node_info.get("app_id")
        action_func = data.get("action")
        # 获取映射字典
        dyn_map = dynamic_field_map.get(app_id, {}).get(action_func, {})
        stat_map = static_content_map.get(app_id, {}).get(action_func, {})
        for field in data.keys():
            # 动态字段优先
            if field in dyn_map and dyn_map[field] in alert_row:
                data[field] = alert_row[dyn_map[field]]
            # 静态字段
            elif field in stat_map:
                data[field] = stat_map[field]
        information["data"] = data
        node_info["information"] = information

    return output_data

def fill_signature_fields_with_deepseek(output_data, deepseek_content):
    """
    只处理dynamic_field_map中映射到signature的字段，用deepseek内容覆盖
    """
    nodes = output_data.get("node_info", {})
    for node_id, node_info in nodes.items():
        information = node_info.get("information", {})
        data = information.get("data", {})
        if not data:
            continue
        app_id = node_info.get("app_id")
        action_func = data.get("action")
        dyn_map = dynamic_field_map.get(app_id, {}).get(action_func, {})
        for field, alert_field in dyn_map.items():
            if alert_field == "signature":
                data[field] = deepseek_content
        information["data"] = data
        node_info["information"] = information
    return output_data

# 固定的开始和结束节点内容
start_node = {
    "app_id": "start",
    "app_type": 0,
}
end_node = {
    "app_id": "end",
    "app_type": 0,
}

@r.route("/alert/parseaction", methods=['POST'])
def alert_parse_action():
    connection = None
    try:
        data = request.json or {}
        analyse_id = data.get("analyse_id")
        token = data.get("token")  # 新增：从POST body获取token
        if not analyse_id:
            return jsonify({"code": 400, "msg": "analyse_id为必填", "data": None})
        if not token:
            return jsonify({"code": 400, "msg": "token为必填", "data": None})

        connection = get_db_connection()
        with connection.cursor() as cursor:
            # 取推荐动作
            cursor.execute("SELECT recommended_actions FROM w5_analysis WHERE analysis_id = %s", (analyse_id,))
            row = cursor.fetchone()
            if not row or not row.get("recommended_actions"):
                return jsonify({"code": 404, "msg": "analyse_id不存在或recommended_actions为空", "data": None})
            recommended_action = row["recommended_actions"]

            # 取alert信息（alert_id与analyse_id一致）
            cursor.execute("SELECT * FROM w5_alert WHERE alert_id = %s", (analyse_id,))
            alert_row = cursor.fetchone()
            if not alert_row:
                return jsonify({"code": 404, "msg": "alert_id不存在", "data": None})

        # 生成初步 output.json
        parsed_json = parse_input_and_generate_json(recommended_action, start_node, end_node)
        # 填充参数
        updated_json = fill_params_with_mapping(parsed_json, alert_row)

        # -------- 新增：调用deepseek生成运维通知并处理signature相关参数 --------
        # 1. 构造prompt
        deepseek_prompt = (
            "请根据以下结构化决策链及其参数，简明说明本次告警的处理效果、涉及参数，并用简洁明了的方式为运维人员生成一条通知内容。\n"
            f"结构化决策链: {recommended_action}\n"
            f"参数详情: {json.dumps(updated_json, ensure_ascii=False)}\n"
            "请输出：\n"
            "1. 本次决策处理效果简述\n"
            "2. 处理涉及的关键参数\n"
            "3. 处理方式简介\n"
            "4. 运维友好通知内容（只要一句话，直接可用于推送）"
        )
        notify_result = call_deepseek_model(deepseek_prompt)
        # 可选：将通知内容写入remarks
        # updated_json["remarks"] = notify_result

        # 2. 用deepseek内容覆盖signature相关动态映射字段
        updated_json = fill_signature_fields_with_deepseek(updated_json, notify_result)

        # -------- 写入output.json --------
        output_file = os.path.join(os.path.dirname(__file__), "output.json")
        with open(output_file, "w", encoding="utf-8") as f:
            json.dump(updated_json, f, ensure_ascii=False, indent=4)

        # -------- 调用 /post/workflow/import 接口，token用POST body传入的 --------
        headers = {
            "Content-Type": "application/json",
            "token": token
        }
        import_data = {
            "workflow_data": updated_json
        }
        response = requests.post(ImportUrl, headers=headers, json=import_data)
        response.raise_for_status()
        import_result = response.json()
        workflow_uuid = import_result.get("data", {}).get("uuid") or import_result.get("data", {}).get("workflow_id")

        # 若获得workflow_uuid内容，则更新w5_analysis的status字段为1
        if workflow_uuid:
            with get_db_connection().cursor() as cursor3:
                cursor3.execute("UPDATE w5_analysis SET status = 1 WHERE analysis_id = %s", (analyse_id,))
                cursor3.connection.commit()

        # 更新w5_alert的is_processed字段为1
        with get_db_connection().cursor() as cursor2:
            cursor2.execute("UPDATE w5_alert SET is_processed = 1 WHERE alert_id = %s", (analyse_id,))
            cursor2.connection.commit()

            # 更新w5_alert_analysis表的uuid字段
            cursor2.execute("UPDATE w5_alert_analysis SET uuid = %s WHERE analysis_id = %s", 
                           (workflow_uuid, analyse_id))
            cursor2.connection.commit()

        return jsonify({
            "code": 200,
            "msg": f"参数已自动填充、剧本已处理并生成，workflow uuid: {workflow_uuid}",
            "data": {"workflow_uuid": workflow_uuid, "output": updated_json}
        })

    except Exception as e:
        return jsonify({"code": 500, "msg": f"服务器错误: {str(e)}", "data": None})
    finally:
        try:
            if connection:
                connection.close()
        except NameError:
            pass

@r.route("/alert/getworkflowuuid", methods=['POST'])
def alert_get_workflow_uuid():
    connection = None
    try:
        data = request.json or {}
        alert_id = data.get("alert_id")
        
        if not alert_id:
            return jsonify({"code": 400, "msg": "alert_id为必填", "data": None})

        connection = get_db_connection()
        with connection.cursor() as cursor:
            # 查询w5_alert_analysis表获取uuid
            cursor.execute("SELECT uuid FROM w5_alert_analysis WHERE alert_id = %s", (alert_id,))
            result = cursor.fetchone()
            
            if not result or not result.get("uuid"):
                return jsonify({"code": 404, "msg": "未找到对应的workflow uuid", "data": None})
            
            return jsonify({
                "code": 200,
                "msg": "查询成功",
                "data": {"workflow_uuid": result["uuid"]}
            })

    except Exception as e:
        return jsonify({"code": 500, "msg": f"服务器错误: {str(e)}", "data": None})
    finally:
        try:
            if connection:
                connection.close()
        except NameError:
            pass