import pymysql
from datetime import datetime

# 连接 MySQL 数据库
conn = pymysql.connect(
    host='localhost',
    user='root',
    password='root',
    database='w5_db',
    charset='utf8mb4'
)
cursor = conn.cursor()

# 1. 先删除同名表
cursor.execute("SET FOREIGN_KEY_CHECKS = 0")
cursor.execute("DROP TABLE IF EXISTS w5_alert_analysis")
cursor.execute("DROP TABLE IF EXISTS w5_analysis")
cursor.execute("DROP TABLE IF EXISTS w5_alert")
cursor.execute("SET FOREIGN_KEY_CHECKS = 1")

# 2. 创建表

# w5_alert
cursor.execute("""
CREATE TABLE `w5_alert` (
  `alert_id` varchar(36) NOT NULL,
  `timestamp` datetime DEFAULT NULL,
  `source_ip` varchar(45) DEFAULT NULL,
  `destination_ip` varchar(45) DEFAULT NULL,
  `source_port` int DEFAULT NULL,
  `destination_port` int DEFAULT NULL,
  `protocol` varchar(10) DEFAULT NULL,
  `attack_type` varchar(50) DEFAULT NULL,
  `severity` varchar(20) DEFAULT NULL,
  `signature` varchar(255) DEFAULT NULL,
  `detection_system` varchar(100) DEFAULT NULL,
  `correlation_id` varchar(36) DEFAULT NULL,
  `status` int DEFAULT '0',
  `is_decided` TINYINT(1) DEFAULT '0' COMMENT '是否决策',
  `is_processed` TINYINT(1) DEFAULT '0' COMMENT '是否处理',
  `create_time` datetime DEFAULT NULL,
  `update_time` datetime DEFAULT NULL,
  PRIMARY KEY (`alert_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
""")

# w5_analysis
cursor.execute("""
CREATE TABLE `w5_analysis` (
  `analysis_id` varchar(36) NOT NULL,
  `timestamp` datetime DEFAULT NULL,
  `attack_summary` text,
  `affected_systems` text,
  `potential_risk` text,
  `recommended_actions` text,
  `notes` text,
  `status` int DEFAULT '0',
  `create_time` datetime DEFAULT NULL,
  `update_time` datetime DEFAULT NULL,
  PRIMARY KEY (`analysis_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
""")

# w5_alert_analysis
cursor.execute("""
CREATE TABLE `w5_alert_analysis` (
  `analysis_id` varchar(36) NOT NULL,
  `alert_id` varchar(36) NOT NULL,
  `create_time` datetime DEFAULT NULL,
  PRIMARY KEY (`analysis_id`,`alert_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
""")

# 3. 插入初始数据
now = datetime.now().strftime('%Y-%m-%d %H:%M:%S')

# w5_alert 两条
alert_data = [
    (
        'alert-001', now, '************', '********', 12345, 80, 'TCP',
        'SQL Injection', 'High', 'SQLi Detected', 'IDS-1', 'corr-001', 0, 0, 0, now, now
    ),
    (
        'alert-002', now, '**********', '********', 54321, 443, 'UDP',
        'DDoS', 'Critical', 'DDoS Flood', 'IDS-2', 'corr-002', 0, 0, 0, now, now
    )
]
cursor.executemany("""
INSERT INTO w5_alert (
    alert_id, timestamp, source_ip, destination_ip, source_port, destination_port, protocol,
    attack_type, severity, signature, detection_system, correlation_id, status,
    is_decided, is_processed, create_time, update_time
) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
""", alert_data)

# w5_analysis 两条
analysis_data = [
    (
        'ana-001', now, 'SQL注入攻击分析', 'Web服务器A', '数据泄露风险高',
        '加强输入校验', '需进一步调查', 0, now, now
    ),
    (
        'ana-002', now, 'DDoS攻击分析', 'Web服务器B', '服务中断风险',
        '部署防火墙', '已通知管理员', 0, now, now
    )
]
cursor.executemany("""
INSERT INTO w5_analysis (
    analysis_id, timestamp, attack_summary, affected_systems, potential_risk,
    recommended_actions, notes, status, create_time, update_time
) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
""", analysis_data)

# w5_alert_analysis 两条
alert_analysis_data = [
    ('ana-001', 'alert-001', now),
    ('ana-002', 'alert-002', now)
]
cursor.executemany("""
INSERT INTO w5_alert_analysis (
    analysis_id, alert_id, create_time
) VALUES (%s, %s, %s)
""", alert_analysis_data)

# 提交并关闭
conn.commit()
cursor.close()
conn.close()

print("✅ 三张表已创建并插入初始数据！")
