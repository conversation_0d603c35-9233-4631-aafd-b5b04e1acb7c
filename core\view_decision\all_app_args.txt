app_id: md5
  func: encryption
    - key: text, type: text, required: True
    - key: type, type: select, required: True

app_id: nmap
  func: scan
    - key: target, type: text, required: True
    - key: ports, type: text, required: True
    - key: protocol, type: select, required: True

app_id: bankcard
  func: query
    - key: card, type: text, required: True

app_id: serverjiang
  func: send
    - key: key, type: text, required: True
    - key: text, type: text, required: True
    - key: desp, type: textarea, required: False

app_id: phone
  func: query
    - key: phone, type: text, required: True

app_id: otx
  func: is_ioc
    - key: api_key, type: text, required: True
    - key: pulse_id, type: text, required: True
    - key: ioc, type: text, required: True

app_id: mysql
  func: query
    - key: host, type: text, required: True
    - key: port, type: number, required: True
    - key: user, type: text, required: True
    - key: passwd, type: text, required: True
    - key: db, type: text, required: True
    - key: sql, type: textarea, required: True
  func: update
    - key: host, type: text, required: True
    - key: port, type: number, required: True
    - key: user, type: text, required: True
    - key: passwd, type: text, required: True
    - key: db, type: text, required: True
    - key: sql, type: textarea, required: True

app_id: zhfc
  func: make
    - key: text, type: textarea, required: True

app_id: linux
  func: execute
    - key: host, type: text, required: True
    - key: port, type: number, required: True
    - key: user, type: text, required: True
    - key: passwd, type: text, required: True
    - key: shell, type: textarea, required: True

app_id: intelligent_detection
  func: get_events

app_id: redis
  func: get
    - key: host, type: text, required: True
    - key: port, type: number, required: True
    - key: db, type: number, required: True
    - key: password, type: text, required: False
    - key: key, type: text, required: True
  func: set
    - key: host, type: text, required: True
    - key: port, type: number, required: True
    - key: db, type: number, required: True
    - key: password, type: text, required: False
    - key: key, type: text, required: True
    - key: value, type: text, required: True
  func: delete
    - key: host, type: text, required: True
    - key: port, type: number, required: True
    - key: db, type: number, required: True
    - key: password, type: text, required: False
    - key: key, type: text, required: True
  func: flushdb
    - key: host, type: text, required: True
    - key: port, type: number, required: True
    - key: db, type: number, required: True
    - key: password, type: text, required: False
  func: flushall
    - key: host, type: text, required: True
    - key: port, type: number, required: True
    - key: password, type: text, required: False

app_id: dingding
  func: send
    - key: access_token, type: text, required: True
    - key: msg, type: text, required: True

app_id: windows
  func: execute
    - key: host, type: text, required: True
    - key: port, type: number, required: True
    - key: user, type: text, required: True
    - key: passwd, type: text, required: True
    - key: cmd, type: textarea, required: True

app_id: qq
  func: query
    - key: qq, type: text, required: True

app_id: intelligent_decision-making
  func: send_event
    - key: event_id, type: text, required: True
    - key: event_type, type: text, required: True
    - key: severity, type: text, required: True
    - key: detected_at, type: text, required: True
    - key: details, type: text, required: True
  func: get_response_suggestions
    - key: event_id, type: text, required: True
  func: update_event_status
    - key: event_id, type: text, required: True
    - key: new_status, type: text, required: True

app_id: email
  func: send
    - key: host, type: text, required: True
    - key: port, type: number, required: True
    - key: user, type: text, required: True
    - key: passwd, type: text, required: True
    - key: encrypt, type: select, required: True
    - key: sender, type: text, required: True
    - key: to, type: text, required: True
    - key: title, type: text, required: True
    - key: type, type: select, required: True
    - key: text, type: textarea, required: True

app_id: icp
  func: query
    - key: domain, type: text, required: True

app_id: base64
  func: encryption
    - key: text, type: text, required: True
  func: decrypt
    - key: text, type: text, required: True

app_id: firewalld
  func: add_port_to_zone
    - key: host, type: text, required: True
    - key: port, type: text, required: True
    - key: user, type: text, required: True
    - key: passwd, type: text, required: True
    - key: target_port, type: text, required: True
    - key: zone, type: text, required: True
  func: remove_port_from_zone
    - key: host, type: text, required: True
    - key: port, type: text, required: True
    - key: user, type: text, required: True
    - key: passwd, type: text, required: True
    - key: target_port, type: text, required: True
    - key: zone, type: text, required: False
  func: reload_firewalld
    - key: host, type: text, required: True
    - key: port, type: text, required: True
    - key: user, type: text, required: True
    - key: passwd, type: text, required: True
  func: block_ip
    - key: host, type: text, required: True
    - key: port, type: text, required: True
    - key: user, type: text, required: True
    - key: passwd, type: text, required: True
    - key: ip_address, type: text, required: True

app_id: feishu
  func: send
    - key: hook_uuid, type: text, required: True
    - key: msg, type: text, required: True

app_id: fscan
  func: scan
    - key: target, type: text, required: True
    - key: ports, type: text, required: False

app_id: splunk
  func: scan
    - key: domain, type: text, required: True
    - key: user, type: text, required: True
    - key: passwd, type: text, required: True
    - key: body, type: textarea, required: True

app_id: suricata
  func: process_logs
    - key: log_type, type: text, required: True

app_id: es
  func: scan
    - key: host, type: text, required: True
    - key: port, type: number, required: True
    - key: index, type: text, required: True
    - key: body, type: textarea, required: True
    - key: account, type: text, required: False
    - key: password, type: text, required: False

app_id: helloworld
  func: hello_world
    - key: name, type: text, required: True

app_id: clickhouse
  func: query
    - key: url, type: text, required: True
    - key: user, type: text, required: True
    - key: passwd, type: text, required: True
    - key: db, type: text, required: True
    - key: sql, type: textarea, required: True

app_id: url
  func: make
    - key: url, type: text, required: True

app_id: ip
  func: ip
    - key: ip, type: text, required: True

app_id: threatbook
  func: ip_query
    - key: key, type: text, required: True
    - key: ip, type: text, required: True
  func: ip_reputation
    - key: key, type: text, required: True
    - key: ip, type: text, required: True
  func: domain_query
    - key: key, type: text, required: True
    - key: domain, type: text, required: True

app_id: myhelloworld
  func: my_hello_world
    - key: name1, type: text, required: True
    - key: name2, type: text, required: True

app_id: whois
  func: query
    - key: domain, type: text, required: True

app_id: honming
  func: query
    - key: domain, type: text, required: True

