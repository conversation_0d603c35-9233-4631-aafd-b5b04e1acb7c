import os
import json

def collect_app_args(apps_dir, output_file):
    written_app_ids = set()
    with open(output_file, "w", encoding="utf-8") as out_f:
        for app_id in os.listdir(apps_dir):
            app_path = os.path.join(apps_dir, app_id)
            app_json_path = os.path.join(app_path, "app.json")
            if not os.path.isdir(app_path):
                continue
            if app_id in written_app_ids:
                continue
            if not os.path.isfile(app_json_path):
                continue
            try:
                with open(app_json_path, "r", encoding="utf-8") as f:
                    app_json = json.load(f)
                out_f.write(f"app_id: {app_id}\n")
                args = app_json.get("args", {})
                for func, params in args.items():
                    out_f.write(f"  func: {func}\n")
                    for param in params:
                        out_f.write(f"    - key: {param.get('key')}, type: {param.get('type')}, required: {param.get('required')}\n")
                out_f.write("\n")
                written_app_ids.add(app_id)
            except Exception as e:
                print(f"Error reading {app_json_path}: {e}")

if __name__ == "__main__":
    # apps目录为脚本所在目录的上一级的apps
    base_dir = os.path.dirname(os.path.abspath(__file__))
    apps_dir = os.path.abspath(os.path.join(base_dir, "../../apps"))
    output_file = os.path.join(base_dir, "all_app_args.txt")
    collect_app_args(apps_dir, output_file)
    print(f"参数字段已写入 {output_file}")
