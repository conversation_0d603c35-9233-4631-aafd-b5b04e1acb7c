from flask import request, jsonify
import pymysql
from datetime import datetime
from . import *  # 确保这里是正确的导入方式

# 获取数据库连接
def get_db_connection():
    try:
        return pymysql.connect(
            host=ServerHost,
            user=MysqlUSER,
            password=MysqlPWD,
            database=MysqlBase,
            charset=SQLCharset
        )
    except pymysql.MySQLError as e:
        raise Exception(f"Database connection error: {e}")

# 记录日志
def write_log(dataset_id, log_type, message):
    conn = get_db_connection()
    cursor = conn.cursor()
    created_at = datetime.now().strftime('%Y-%m-%d %H:%M:%S')

    cursor.execute("""
        INSERT INTO dataset_logs (dataset_id, log_type, message, created_at)
        VALUES (%s, %s, %s, %s)
    """, (dataset_id, log_type, message, created_at))

    conn.commit()
    cursor.close()
    conn.close()

# 更新数据集状态的 API
@r.route("/datasets/update/status", methods=['POST'])
def update_dataset_status():
    conn = None
    try:
        data = request.get_json()

        # **检查必填字段**
        if 'dataset_id' not in data or 'new_status' not in data:
            return jsonify({'code': 1, 'msg': "Missing 'dataset_id' or 'new_status'.", 'data': {}}), 400

        dataset_id = data['dataset_id']
        new_status = data['new_status']

        # **检查数据类型**
        if not isinstance(dataset_id, int):
            return jsonify({'code': 1, 'msg': "Invalid 'dataset_id', it must be an integer.", 'data': {}}), 400

        # **数据库允许的状态值**
        valid_statuses = ['active', 'inactive']
        if new_status not in valid_statuses:
            return jsonify({'code': 1, 'msg': f"Invalid status. Allowed: {', '.join(valid_statuses)}", 'data': {}}), 400

        # **连接数据库**
        conn = get_db_connection()
        cursor = conn.cursor()

        # **查询数据集是否存在**
        cursor.execute("SELECT id FROM dataset_info WHERE id = %s", (dataset_id,))
        dataset = cursor.fetchone()

        if dataset:
            matched_dataset_id = dataset[0]  # 获取数据库中的 ID

            # **更新数据集状态**
            cursor.execute("UPDATE dataset_info SET status = %s WHERE id = %s", (new_status, matched_dataset_id))
            conn.commit()

            log_message = f"Dataset ID {matched_dataset_id} status updated to '{new_status}'."
            write_log(matched_dataset_id, 'update', log_message)
            return jsonify({'code': 0, 'msg': 'Success', 'data': {'message': log_message}}), 200
        else:
            matched_dataset_id = None
            log_message = f"Dataset ID {dataset_id} not found. Status update failed."
            write_log(matched_dataset_id, 'update', log_message)
            return jsonify({'code': 1, 'msg': log_message, 'data': {}}), 200

    except Exception as e:
        if conn:
            conn.rollback()  # 发生错误时回滚
        write_log(None, 'error', f"Error updating dataset status: {str(e)}")
        return jsonify({'code': 1, 'msg': f"Error: {str(e)}", 'data': {}}), 500

    finally:
        if conn:
            conn.close()
