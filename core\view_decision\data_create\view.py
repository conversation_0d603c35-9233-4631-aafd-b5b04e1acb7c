from flask import request, jsonify
import pymysql
import os
from datetime import datetime
from werkzeug.utils import secure_filename
from . import *  # 保持原有模块导入

# 获取当前文件所在目录
BASE_DIR = os.path.dirname(os.path.abspath(__file__))
UPLOAD_DIR = os.path.join(BASE_DIR, 'temp')
os.makedirs(UPLOAD_DIR, exist_ok=True)

def get_db_connection():
    try:
        return pymysql.connect(
            host=ServerHost,
            user=MysqlUSER,
            password=MysqlPWD,
            database=MysqlBase,
            charset=SQLCharset
        )
    except pymysql.MySQLError as e:
        raise Exception(f"Database connection error: {e}")

# 写入日志
def write_log(dataset_id, log_type, message):
    conn = get_db_connection()
    cursor = conn.cursor()
    created_at = datetime.now().strftime('%Y-%m-%d %H:%M:%S')

    cursor.execute("""
        INSERT INTO dataset_logs (dataset_id, log_type, message, created_at)
        VALUES (%s, %s, %s, %s)
    """, (dataset_id, log_type, message, created_at))

    conn.commit()
    cursor.close()
    conn.close()

# 创建数据集 API
@r.route("/datasets/create", methods=['POST'])
def create_dataset():
    try:
        # 检查字段
        required_fields = ['name', 'size', 'num_samples', 'format', 'storage_path', 'owner']
        data = request.form
        missing_fields = [field for field in required_fields if field not in data]

        if missing_fields:
            return jsonify({
                'code': 1,
                'msg': f"Missing required fields: {', '.join(missing_fields)}",
                'data': {}
            }), 400

        # 检查文件
        if 'file' not in request.files:
            return jsonify({
                'code': 1,
                'msg': "Missing file in upload",
                'data': {}
            }), 400

        # 保存上传的文件
        file = request.files['file']
        filename = secure_filename(file.filename)
        file_path = os.path.join(UPLOAD_DIR, filename)
        file.save(file_path)

        # 可选字段
        version = data.get('version', 'v1.0.0')
        status = data.get('status', 'active')
        description = data.get('description', '')
        tags = data.get('tags', '')
        data_schema = data.get('data_schema', '')
        download_url = data.get('download_url', f"http://localhost/download/{filename}")

        # 插入数据库
        conn = get_db_connection()
        cursor = conn.cursor()

        cursor.execute("""
            INSERT INTO dataset_info (name, description, tags, size, num_samples, format, 
                                      data_schema, storage_path, download_url, owner, version, status)
            VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
        """, (
            data['name'], description, tags, data['size'], data['num_samples'],
            data['format'], data_schema, file_path, download_url, data['owner'], version, status
        ))

        conn.commit()
        cursor.execute("SELECT LAST_INSERT_ID()")
        dataset_id = cursor.fetchone()[0]

        # 写入日志
        log_message = f"Created dataset with id {dataset_id}, name: {data['name']}"
        write_log(dataset_id, 'create', log_message)

        return jsonify({
            'code': 0,
            'msg': 'Success',
            'data': {
                'id': dataset_id,
                'name': data['name'],
                'description': description,
                'tags': tags,
                'size': data['size'],
                'num_samples': data['num_samples'],
                'format': data['format'],
                'data_schema': data_schema,
                'storage_path': file_path,
                'download_url': download_url,
                'owner': data['owner'],
                'version': version,
                'status': status,
                'created_at': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            }
        }), 201

    except Exception as e:
        return jsonify({
            'code': 1,
            'msg': f"Error creating dataset: {str(e)}",
            'data': {}
        }), 500

    finally:
        if 'conn' in locals():
            conn.close()
