#!/usr/bin/env python
# encoding:utf-8
from . import *
import pymysql
from flask import request, jsonify

@r.route("/datasets/delete", methods=['POST'])
def delete_dataset():
    # 获取请求的 JSON 数据
    data = request.get_json()

    # 检查请求体是否包含 id
    if not data or 'id' not in data:
        return jsonify({
            "code": 1,
            "msg": "Request must include a dataset ID.",
            "data": {}
        }), 400

    try:
        dataset_id = int(data['id'])  # 确保 dataset_id 是整数
    except ValueError:
        return jsonify({
            "code": 1,
            "msg": "ID must be a valid integer.",
            "data": {}
        }), 400

    # 连接数据库
    try:
        conn = pymysql.connect(
            host=ServerHost,
            user=MysqlUSER,
            password=MysqlPWD,
            database=MysqlBase,
            charset=SQLCharset
        )
        cursor = conn.cursor()
    except Exception as e:
        return jsonify({
            "code": 1,
            "msg": f"Database connection failed: {str(e)}",
            "data": {}
        }), 500

    # 查询数据集是否存在
    cursor.execute("SELECT id, name FROM dataset_info WHERE id = %s", (dataset_id,))
    result = cursor.fetchone()

    if not result:
        message = f"No dataset found with ID {dataset_id}. Deletion failed."
        log_dataset_id = -1  # 设为 -1 以满足 NOT NULL 约束
    else:
        try:
            # 删除 dataset_info 表中的数据
            cursor.execute("DELETE FROM dataset_info WHERE id = %s", (dataset_id,))
            conn.commit()
            message = f"Dataset with ID {dataset_id} ({result[1]}) successfully deleted."
            log_dataset_id = dataset_id  # 记录原始 ID
        except Exception as e:
            conn.rollback()
            return jsonify({
                "code": 1,
                "msg": f"Error deleting dataset: {str(e)}",
                "data": {}
            }), 500

    # **记录日志**
    try:
        cursor.execute("""
            INSERT INTO dataset_logs (dataset_id, log_type, message, created_at)
            VALUES (%s, 'delete', %s, NOW())
        """, (log_dataset_id, message))
        conn.commit()
    except Exception as e:
        conn.rollback()
        return jsonify({
            "code": 1,
            "msg": f"Error logging delete action: {str(e)}",
            "data": {}
        }), 500

    # 关闭数据库连接
    cursor.close()
    conn.close()

    return jsonify({
        "code": 0,
        "msg": "Success",
        "data": {
            "message": message
        }
    })
