from flask import Flask, request, jsonify
import pymysql
from datetime import datetime
from . import *  # 确保这里是正确的导入方式

# 数据库连接
def get_db_connection():
    try:
        return pymysql.connect(
            host=ServerHost,
            user=MysqlUSER,
            password=MysqlPWD,
            database=MysqlBase,
            charset=SQLCharset
        )
    except pymysql.MySQLError as e:
        raise Exception(f"Database connection error: {e}")

# 写入操作日志到 dataset_logs 表
def write_log(dataset_id, log_type, message):
    conn = get_db_connection()
    cursor = conn.cursor()
    created_at = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    cursor.execute("""
        INSERT INTO dataset_logs (dataset_id, log_type, message, created_at)
        VALUES (%s, %s, %s, %s)
    """, (dataset_id, log_type, message, created_at))
    conn.commit()
    cursor.close()
    conn.close()

# 获取所有数据集的 API
@r.route("/datasets", methods=['GET'])
def get_datasets():
    try:
        # 获取查询参数
        page = request.args.get('page', 1, type=int)
        limit = request.args.get('limit', 10, type=int)
        search = request.args.get('search', '')
        tags = request.args.get('tags', '')

        # 创建数据库连接
        conn = get_db_connection()
        cursor = conn.cursor(pymysql.cursors.DictCursor)

        # 构建查询条件
        where_clauses = []
        params = []

        if search:
            where_clauses.append("(name LIKE %s OR description LIKE %s)")
            params.extend([f"%{search}%", f"%{search}%"])

        if tags:
            where_clauses.append("tags LIKE %s")
            params.append(f"%{tags}%")

        # 查询条件拼接
        where_condition = " AND ".join(where_clauses) if where_clauses else "1=1"

        query = f"""
            SELECT * FROM dataset_info
            WHERE {where_condition}
            LIMIT %s OFFSET %s
        """

        params.extend([limit, (page - 1) * limit])

        # 执行查询
        cursor.execute(query, params)
        datasets = cursor.fetchall()

        # 获取当前查询的条件详细信息
        query_details = f"search: {search}, tags: {tags}, page: {page}, limit: {limit}"

        # 构建详细的日志信息
        dataset_info_list = [f"id: {dataset['id']}, name: {dataset['name']}, status: {dataset['status']}" for dataset in datasets]
        dataset_info_message = f"Query details - {query_details}. Returned datasets: {', '.join(dataset_info_list)}"

        # 写入日志
        write_log(0, 'download', dataset_info_message)

        return jsonify({
            'code': 0,
            'msg': "Success",
            'data': datasets
        }), 200

    except Exception as e:
        write_log(0, 'error', f"Error fetching datasets: {str(e)}")
        return jsonify({
            'code': 1,
            'msg': f"Error fetching datasets: {str(e)}",
            'data': {}
        }), 500

    finally:
        conn.close()
