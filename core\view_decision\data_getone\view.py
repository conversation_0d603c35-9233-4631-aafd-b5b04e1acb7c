from flask import Flask, request, jsonify
import pymysql
from datetime import datetime
from . import *  # 确保这里是正确的导入方式

def get_db_connection():
    try:
        return pymysql.connect(
            host=ServerHost,
            user=MysqlUSER,
            password=MysqlPWD,
            database=MysqlBase,
            charset=SQLCharset
        )
    except pymysql.MySQLError as e:
        raise Exception(f"Database connection error: {e}")

# 写入操作日志到 dataset_logs 表
def write_log(dataset_id, log_type, message):
    conn = get_db_connection()
    cursor = conn.cursor()

    created_at = datetime.now().strftime('%Y-%m-%d %H:%M:%S')

    cursor.execute("""
        INSERT INTO dataset_logs (dataset_id, log_type, message, created_at)
        VALUES (%s, %s, %s, %s)
    """, (dataset_id, log_type, message, created_at))

    conn.commit()
    cursor.close()
    conn.close()

# 获取单个数据集详情的API
@r.route("/datasets/<int:dataset_id>", methods=['GET'])
def get_dataset_detail(dataset_id):
    try:
        # 创建数据库连接
        conn = get_db_connection()
        cursor = conn.cursor(pymysql.cursors.DictCursor)

        # 查询数据集详情
        query = "SELECT * FROM dataset_info WHERE id = %s"
        cursor.execute(query, (dataset_id,))
        dataset = cursor.fetchone()

        if not dataset:
            # 如果没有找到数据集，返回错误
            error_message = f"Dataset with id {dataset_id} not found"
            write_log(dataset_id, 'error', error_message)
            return jsonify({
                'code': 1,
                'msg': error_message,
                'data': {}
            }), 404

        # 记录详细日志信息，包括所有字段
        dataset_info_message = f"Fetched dataset details: {dataset}"

        # 写入日志（记录获取数据集详情操作）
        write_log(dataset_id, 'download', dataset_info_message)

        # 返回响应，包含 dataset_info 表的所有字段
        return jsonify({
            'code': 0,
            'msg': 'Success',
            'data': dataset
        }), 200

    except Exception as e:
        # 写入日志（记录错误信息）
        write_log(dataset_id, 'error', f"Error fetching dataset details: {str(e)}")

        return jsonify({
            'code': 1,
            'msg': f"Error fetching dataset details: {str(e)}",
            'data': {}
        }), 500

    finally:
        conn.close()
