from flask import request, jsonify, send_from_directory
import pymysql
import os
from datetime import datetime
from . import *  # 确保这里是正确的导入方式

def get_db_connection():
    try:
        return pymysql.connect(
            host=ServerHost,
            user=MysqlUSER,
            password=MysqlPWD,
            database=MysqlBase,
            charset=SQLCharset
        )
    except pymysql.MySQLError as e:
        raise Exception(f"Database connection error: {e}")
# 写入操作日志到 dataset_logs 表
def write_log(dataset_id, log_type, message):
    conn = get_db_connection()
    cursor = conn.cursor()
    created_at = datetime.now()  # 直接传递 datetime 对象
    cursor.execute("""
        INSERT INTO dataset_logs (dataset_id, log_type, message, created_at)
        VALUES (%s, %s, %s, %s)
    """, (dataset_id, log_type, message, created_at))
    conn.commit()
    cursor.close()
    conn.close()

# 获取下载链接的API
@r.route("/datasets/download", methods=['GET'])
def get_download_link():
    try:
        dataset_id = request.args.get('dataset_id', type=int)
        if not dataset_id:
            return jsonify({
                'code': 1,
                'msg': "Missing required parameter: 'dataset_id'.",
                'data': {}
            }), 400

        conn = get_db_connection()
        cursor = conn.cursor(pymysql.cursors.DictCursor)

        cursor.execute("""
            SELECT id, download_url, storage_path FROM dataset_info WHERE id = %s
        """, (dataset_id,))
        dataset = cursor.fetchone()

        if not dataset:
            log_message = f"Dataset with id {dataset_id} not found"
            write_log(None, 'delete', log_message)
            return jsonify({
                'code': 1,
                'msg': f"Dataset with id {dataset_id} not found",
                'data': {}
            }), 404

        download_link = dataset['download_url'] if dataset['download_url'] else f"http://localhost:8888/datasets/download/{dataset['id']}"

        log_message = f"User requested download for dataset with id {dataset_id}, download link: {download_link}"
        write_log(dataset['id'], 'download', log_message)

        return jsonify({
            'code': 0,
            'msg': 'Success',
            'data': {
                'dataset_id': dataset_id,
                'download_link': download_link
            }
        }), 200

    except Exception as e:
        write_log(None, 'error', f"Error getting download link for dataset {dataset_id}: {str(e)}")
        return jsonify({
            'code': 1,
            'msg': f"Error getting download link: {str(e)}",
            'data': {}
        }), 500

    finally:
        conn.close()

# 文件下载的路由，返回文件内容
@r.route("/datasets/download/<int:dataset_id>", methods=['GET'])
def download_file(dataset_id):
    try:
        conn = get_db_connection()
        cursor = conn.cursor(pymysql.cursors.DictCursor)

        cursor.execute("""
            SELECT storage_path FROM dataset_info WHERE id = %s
        """, (dataset_id,))
        dataset = cursor.fetchone()

        if not dataset:
            return jsonify({
                'code': 1,
                'msg': f"Dataset with id {dataset_id} not found",
                'data': {}
            }), 404

        file_path = dataset['storage_path']
        if not os.path.exists(file_path):
            return jsonify({
                'code': 1,
                'msg': f"File not found at {file_path}",
                'data': {}
            }), 404

        # 这里直接用 send_from_directory 返回文件，前端会收到下载文件
        return send_from_directory(directory=os.path.dirname(file_path),
                                   filename=os.path.basename(file_path),
                                   as_attachment=True)

    except Exception as e:
        return jsonify({
            'code': 1,
            'msg': f"Error downloading file: {str(e)}",
            'data': {}
        }), 500

    finally:
        conn.close()
