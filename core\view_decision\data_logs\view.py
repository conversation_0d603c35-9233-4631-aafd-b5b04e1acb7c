from flask import request, jsonify
import pymysql
from datetime import datetime
from . import *  # 确保这里是正确的导入方式

def get_db_connection():
    try:
        return pymysql.connect(
            host=ServerHost,
            user=MysqlUSER,
            password=MysqlPWD,
            database=MysqlBase,
            charset=SQLCharset
        )
    except pymysql.MySQLError as e:
        raise Exception(f"Database connection error: {e}")

# 获取操作日志的 API
@r.route("/datasets/logs", methods=['GET'])
def get_logs():
    try:
        dataset_id = request.args.get('dataset_id', type=int)  # 可选参数，转成int
        log_type = request.args.get('log_type')  # 可选参数
        limit = request.args.get('limit', default=10, type=int)  # 最大条数限制

        conn = get_db_connection()
        cursor = conn.cursor(pymysql.cursors.DictCursor)

        query = """
        SELECT dl.id, dl.dataset_id, dl.log_type, dl.message, dl.created_at
        FROM dataset_logs dl
        LEFT JOIN dataset_info di ON dl.dataset_id = di.id
        WHERE 1=1
        """
        params = []

        if dataset_id is not None:
            query += " AND dl.dataset_id = %s"
            params.append(dataset_id)

        if log_type:
            query += " AND dl.log_type = %s"
            params.append(log_type)

        query += " ORDER BY dl.created_at DESC LIMIT %s"
        params.append(limit)

        cursor.execute(query, params)
        logs = cursor.fetchall()

        if not logs:
            return jsonify({
                'code': 0,
                'msg': 'No logs found for the given filters.',
                'data': []
            }), 200

        return jsonify({
            'code': 0,
            'msg': 'Success',
            'data': logs
        }), 200

    except Exception as e:
        return jsonify({
            'code': 1,
            'msg': f"Error retrieving logs: {str(e)}",
            'data': []
        }), 500

    finally:
        conn.close()
