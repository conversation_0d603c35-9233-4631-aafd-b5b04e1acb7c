from flask import request, jsonify
import os
from . import *  # 确保使用正确的 Blueprint
import chardet

# 假设你已有的日志写入函数
def write_log(dataset_id, log_type, message):
    pass  # 这里按你之前的实现写或导入

@r.route("/datasets/preview", methods=["GET"])
def preview_dataset():
    try:
        file_path = request.args.get("file_path")
        limit = request.args.get("limit", 10, type=int)

        # 简单防止目录穿越，保证路径安全（可根据你实际需求调整）
        if not file_path or ".." in file_path or not os.path.isfile(file_path):
            return jsonify({
                "code": 1,
                "msg": f"Invalid or missing file path: {file_path}",
                "data": []
            }), 400

        # 检测文件编码
        with open(file_path, 'rb') as f:
            raw_data = f.read(1024)
            encoding = chardet.detect(raw_data)['encoding'] or 'utf-8'

        preview_lines = []
        with open(file_path, 'r', encoding=encoding, errors='ignore') as f:
            for i, line in enumerate(f):
                if i >= limit:
                    break
                preview_lines.append(line.rstrip())

        # 记录日志，dataset_id这里用0或根据实际情况填充
        write_log(0, 'preview', f"Previewed file {file_path} with limit {limit}")

        return jsonify({
            "code": 0,
            "msg": "Success",
            "data": {
                "file_path": file_path,
                "preview": preview_lines
            }
        }), 200

    except Exception as e:
        return jsonify({
            "code": 1,
            "msg": f"Error previewing dataset: {str(e)}",
            "data": []
        }), 500
