from flask import request, jsonify
import pymysql
import os
from datetime import datetime
from werkzeug.utils import secure_filename
from . import *  # 确保这里是正确的导入方式

def get_db_connection():
    try:
        return pymysql.connect(
            host=ServerHost,
            user=MysqlUSER,
            password=MysqlPWD,
            database=MysqlBase,
            charset=SQLCharset
        )
    except pymysql.MySQLError as e:
        raise Exception(f"Database connection error: {e}")
    
def write_log(dataset_id, log_type, message):
    conn = get_db_connection()
    cursor = conn.cursor()
    created_at = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    cursor.execute("""
        INSERT INTO dataset_logs (dataset_id, log_type, message, created_at)
        VALUES (%s, %s, %s, %s)
    """, (dataset_id, log_type, message, created_at))
    conn.commit()
    cursor.close()
    conn.close()

@r.route("/datasets/upload", methods=['POST'])
def upload_dataset():
    try:
        data = request.form.to_dict()
        uploader = data.get('uploader')
        if not uploader:
            return jsonify({
                'status': 'error',
                'message': "Missing required field: uploader"
            }), 400

        uploaded_file = request.files.get('file')
        if not uploaded_file:
            return jsonify({
                'status': 'error',
                'message': "No file uploaded"
            }), 400

        dataset_id = data.get('dataset_id')
        if not dataset_id:
            return jsonify({
                'status': 'error',
                'message': "Missing required field: dataset_id"
            }), 400

        dataset_id = int(dataset_id)
        filename = secure_filename(uploaded_file.filename)

        upload_folder = "./"  # 请确保此路径存在且有写权限
        if not os.path.exists(upload_folder):
            os.makedirs(upload_folder)
        file_path = os.path.join(upload_folder, filename)

        uploaded_file.save(file_path)
        file_size = os.path.getsize(file_path)

        conn = get_db_connection()
        cursor = conn.cursor()

        # 插入 dataset_info，省略 id 字段让数据库自增
        cursor.execute("""
            INSERT INTO dataset_info (id, name, description, tags, size, num_samples, format,
                                      data_schema, storage_path, download_url, owner, version, status)
            VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
            ON DUPLICATE KEY UPDATE
                name=VALUES(name),
                description=VALUES(description),
                tags=VALUES(tags),
                size=VALUES(size),
                storage_path=VALUES(storage_path),
                owner=VALUES(owner),
                version=VALUES(version),
                status=VALUES(status)
        """, (
            dataset_id,
            data.get('name', 'Unnamed Dataset'),
            data.get('description', ''),
            data.get('tags', ''),
            str(file_size),
            0,
            data.get('format', 'CSV'),
            data.get('data_schema', ''),
            file_path,
            '',
            uploader,
            'v1.0.0',
            'active'
        ))

        # 插入 dataset_uploads，id自增省略
        cursor.execute("""
            INSERT INTO dataset_uploads (dataset_id, uploader, upload_time, file_path, file_size, status)
            VALUES (%s, %s, NOW(), %s, %s, %s)
        """, (
            dataset_id,
            uploader,
            file_path,
            str(file_size),
            'success'
        ))

        conn.commit()
        cursor.close()
        conn.close()

        log_message = f"Dataset uploaded by {uploader}, file: {filename}, size: {file_size} bytes"
        write_log(dataset_id, 'upload', log_message)

        return jsonify({
            'status': 'success',
            'message': 'Dataset uploaded successfully.',
            'data': {
                'dataset_id': dataset_id,
                'file_name': filename,
                'file_size': str(file_size),
                'upload_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            }
        }), 201

    except Exception as e:
        error_message = f"Error uploading dataset: {str(e)}"
        write_log(None, 'error', error_message)

        return jsonify({
            'status': 'error',
            'message': error_message
        }), 500
