from flask import Flask, request, jsonify
import pymysql
from datetime import datetime
from . import *  # 确保这里是正确的导入方式

def get_db_connection():
    try:
        return pymysql.connect(
            host=ServerHost,
            user=MysqlUSER,
            password=MysqlPWD,
            database=MysqlBase,
            charset=SQLCharset
        )
    except pymysql.MySQLError as e:
        raise Exception(f"Database connection error: {e}")

@r.route("/datasets/uprecords", methods=['GET'])
def get_uploads():
    try:
        dataset_id = request.args.get('dataset_id', type=int)
        uploader = request.args.get('uploader')
        upload_time = request.args.get('upload_time')
        status = request.args.get('status')

        conn = get_db_connection()
        cursor = conn.cursor(pymysql.cursors.DictCursor)

        where_clauses = []
        params = []

        if dataset_id is not None:
            where_clauses.append("dataset_id = %s")
            params.append(dataset_id)
        if uploader:
            where_clauses.append("uploader = %s")
            params.append(uploader)
        if upload_time:
            try:
                datetime.strptime(upload_time, '%Y-%m-%d')
                where_clauses.append("DATE(upload_time) = %s")
                params.append(upload_time)
            except ValueError:
                return jsonify({
                    'status': 'error',
                    'message': 'Invalid upload_time format. Use YYYY-MM-DD.'
                }), 400
        if status:
            if status not in ['pending', 'success', 'failed']:
                return jsonify({
                    'status': 'error',
                    'message': 'Invalid status value. Must be one of: pending, success, failed.'
                }), 400
            where_clauses.append("status = %s")
            params.append(status)

        where_condition = " AND ".join(where_clauses) if where_clauses else "1"
        query = f"""
            SELECT * FROM dataset_uploads
            WHERE {where_condition}
        """

        cursor.execute(query, params)
        uploads = cursor.fetchall()

        return jsonify({
            'status': 'success',
            'data': uploads
        }), 200

    except Exception as e:
        return jsonify({
            'status': 'error',
            'message': f"Error fetching uploads: {str(e)}"
        }), 500

    finally:
        if 'conn' in locals():
            conn.close()
