import pymysql

# 连接 MySQL 数据库
conn = pymysql.connect(
    host='localhost',
    user='root',
    password='root',
    database='w5_db',
    charset='utf8mb4'
)
cursor = conn.cursor()

# **🔹 先清空表数据，再删除表**
cursor.execute("SET FOREIGN_KEY_CHECKS = 0")  # 禁用外键检查，防止因依赖关系删除失败
cursor.execute("DELETE FROM dataset_logs")
cursor.execute("DELETE FROM dataset_uploads")
cursor.execute("DELETE FROM dataset_info")
cursor.execute("DROP TABLE IF EXISTS dataset_logs")
cursor.execute("DROP TABLE IF EXISTS dataset_uploads")
cursor.execute("DROP TABLE IF EXISTS dataset_info")
cursor.execute("SET FOREIGN_KEY_CHECKS = 1")  # 重新启用外键检查

# **🔹 重新创建 dataset_info（数据集主表）**
cursor.execute("""
CREATE TABLE dataset_info (
    id INT AUTO_INCREMENT PRIMARY KEY, 
    name VARCHAR(255) NOT NULL,
    description TEXT,
    tags VARCHAR(255),
    size VARCHAR(50),
    num_samples INT,
    format VARCHAR(50),
    data_schema TEXT,
    storage_path TEXT NOT NULL,
    download_url TEXT,
    owner VARCHAR(255),
    version VARCHAR(50),
    status ENUM('active', 'inactive') DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
)
""")

# **🔹 重新创建 dataset_uploads（上传记录）** 
cursor.execute("""
CREATE TABLE dataset_uploads (
    id INT AUTO_INCREMENT PRIMARY KEY,
    dataset_id INT,
    uploader VARCHAR(255),
    upload_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    file_path TEXT NOT NULL,
    file_size VARCHAR(50),
    status ENUM('pending', 'success', 'failed') DEFAULT 'pending'
)
""")

# **🔹 重新创建 dataset_logs（日志）**  
cursor.execute("""
CREATE TABLE dataset_logs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    dataset_id INT,
    log_type ENUM('create', 'update', 'delete', 'download') NOT NULL,
    message TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
)
""")

# **🔹 插入 dataset_info 示例数据**
dataset_data = [
    ("AI 训练集", "用于训练深度学习模型的数据集", "AI, NLP", "5GB", 100000, "CSV", '{"fields":["text","label"]}', "/data/ai_dataset", "http://localhost/download/ai.csv", "Alice", "v1.0.0", "active"),
    ("图像分类数据集", "用于图像分类模型训练", "Image, Vision", "10GB", 50000, "JPEG", '{"fields":["image","category"]}', "/home/<USER>/soar/text.txt", "http://localhost:8888/api/v1/desicion/datasets/download/2", "Bob", "v2.1.0", "active"),
    ("安全日志数据集", "包含网络安全日志", "Security, Logs", "2GB", 200000, "JSON", '{"fields":["timestamp","event_type","source_ip"]}', "/data/security_logs", "http://localhost/download/security.json", "Lina", "v3.0.1", "inactive")
]

cursor.executemany("""
INSERT INTO dataset_info (name, description, tags, size, num_samples, format, data_schema, storage_path, download_url, owner, version, status)
VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
""", dataset_data)

# 获取插入的 dataset_info 表的所有 id
cursor.execute("SELECT id FROM dataset_info")
dataset_ids = [row[0] for row in cursor.fetchall()]

# **🔹 插入 dataset_uploads 示例数据**
upload_data = [
    (dataset_ids[0], "Alice", "/uploads/ai_dataset.csv", "5GB", "success"),
    (dataset_ids[1], "Bob", "/uploads/image_classification.zip", "10GB", "success"),
    (dataset_ids[2], "Lina", "/uploads/security_logs.json", "2GB", "failed")
]

cursor.executemany("""
INSERT INTO dataset_uploads (dataset_id, uploader, upload_time, file_path, file_size, status)
VALUES (%s, %s, NOW(), %s, %s, %s)
""", upload_data)

# **🔹 插入 dataset_logs 示例数据**
log_data = [
    (dataset_ids[0], "create", "AI 训练集已创建"),
    (dataset_ids[1], "update", "图像分类数据集更新至 v2.1.0"),
    (dataset_ids[2], "delete", "安全日志数据集已被删除")
]

cursor.executemany("""
INSERT INTO dataset_logs (dataset_id, log_type, message, created_at)
VALUES (%s, %s, %s, NOW())
""", log_data)

# **🔹 提交更改并关闭连接**
conn.commit()
cursor.close()
conn.close()

print("✅ 数据表创建成功，并插入示例数据！")
