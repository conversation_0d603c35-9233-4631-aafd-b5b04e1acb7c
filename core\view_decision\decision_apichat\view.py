from . import *
from flask import request, jsonify
import requests
import mysql.connector

OLLAMA_API_URL = "http://127.0.0.1:11435/api/generate"

def get_db_connection():
    return mysql.connector.connect(
        host="localhost",
        user="root",
        password="root",
        database="w5_db"
    )

# 模型回复函数
def generate_reply(prompt):
    try:
        response = requests.post(OLLAMA_API_URL, json={
            "model": "llama418:latest",
            "prompt": prompt,
            "stream": False,
            "options": {
                "temperature": 0.8,
                "top_p": 0.7,  # 控制词汇范围
                "num_predict": 600  # 控制词汇数量
            }
        })
        if response.status_code != 200:
            return f"Error: {response.text}"
        return response.json().get("response", "Error: No response from model")
    except Exception as e:
        return f"Error: {str(e)}"

# 提取摘要函数
def generate_summary(histext):
    prompt = (
        "请根据以下人类与 AI 的对话内容，压缩并提炼出精简的核心信息，限制在 60 字以内。请避免冗长的描述，只保留关键信息：\n\n"
        f"{histext}\n\n"
        "请输出精简版摘要内容："
    )
    return generate_reply(prompt)[:60]

# 仅保留最近 N 轮人机对话
def truncate_to_last_n_rounds(histext, n=7):
    rounds = histext.strip().split("\n")
    dialogue_pairs = []
    temp = []
    for line in rounds:
        temp.append(line)
        if len(temp) == 2:
            dialogue_pairs.append("\n".join(temp))
            temp = []
    return "\n".join(dialogue_pairs[-n:]) + "\n"

@r.route("/apichat", methods=['POST'])
def apichat():
    data = request.get_json()
    user_id = data.get("user_id")
    api_key = data.get("api_key")
    message = data.get("message")

    if not user_id or not api_key or not message:
        return jsonify({
            "status": "error",
            "code": 400,
            "message": "缺少必要参数",
            "result": None
        }), 400

    try:
        conn = get_db_connection()
        cursor = conn.cursor(dictionary=True)

        # 验证 APIKEY
        cursor.execute(
            "SELECT session_id FROM user_apikeys WHERE user_id = %s AND api_key = %s",
            (user_id, api_key)
        )
        row = cursor.fetchone()

        if row is None:
            return jsonify({
                "status": "error",
                "code": 403,
                "message": "APIKEY 无效或不匹配",
                "result": None
            }), 403

        session_id = row["session_id"]

        # 获取历史对话
        cursor.execute(
            "SELECT histext FROM user_chat WHERE user_id = %s AND session_id = %s",
            (user_id, str(session_id))
        )
        row = cursor.fetchone()
        histext = row["histext"] if row and row["histext"] else ""

        if len(histext) >= 300:
            rounds = histext.strip().split("\n")
            round_count = len([line for line in rounds if line.startswith("Human:")])
            if round_count <= 8:
                histext = generate_summary(histext)
            else:
                histext = truncate_to_last_n_rounds(histext, 7)

        # 构建 prompt 并生成回复
        prompt = (
            "This is a conversation between a helpful AI and a human. The AI responds directly and concisely to each input. "
            "If unsure, the AI will acknowledge it and offer reasonable suggestions. No explanations or extra commentary.\n\n"
            f"Current conversation:\n{histext}Human: {message}\nAI:"
        )

        reply = generate_reply(prompt)
        new_histext = f"{histext}Human: {message}\nAI: {reply}\n"

        # 更新 user_chat 表
        if row is None:
            cursor.execute(
                "INSERT INTO user_chat (user_id, session_id, histext) VALUES (%s, %s, %s)",
                (user_id, str(session_id), new_histext)
            )
        else:
            cursor.execute(
                "UPDATE user_chat SET histext=%s WHERE user_id=%s AND session_id=%s",
                (new_histext, user_id, session_id)
            )

        # 新增或更新 user_history 表
        cursor.execute(
            "SELECT * FROM user_history WHERE user_id = %s AND session_id = %s",
            (user_id, str(session_id))
        )
        history_row = cursor.fetchone()
        if history_row is None:
            summary = generate_summary(new_histext)
            cursor.execute(
                "INSERT INTO user_history (user_id, session_id, hismain, histext) VALUES (%s, %s, %s, %s)",
                (user_id, str(session_id), summary, new_histext)
            )
        else:
            cursor.execute(
                "UPDATE user_history SET histext = %s WHERE user_id = %s AND session_id = %s",
                (new_histext, user_id, str(session_id))
            )

        conn.commit()
        cursor.close()
        conn.close()

        return jsonify({
            "status": "success",
            "code": 200,
            "message": "交流成功",
            "result": {
                "reply": reply,
                "session_id": session_id
            }
        })

    except Exception as e:
        return jsonify({
            "status": "error",
            "code": 500,
            "message": f"服务器内部错误: {str(e)}",
            "result": None
        }), 500
