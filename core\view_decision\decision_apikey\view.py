#!/usr/bin/env python
# encoding:utf-8
from . import *
from flask import request, jsonify
import uuid
import mysql.connector

# 数据库连接配置
db_config = {
    'host': 'localhost',
    'user': 'root',
    'password': 'root',
    'database': 'w5_db'
}

def get_db_connection():
    return mysql.connector.connect(**db_config)

@r.route("/apikey", methods=['POST'])
def generate_apikey():
    data = request.get_json()
    user_id = data.get("user_id")

    if not user_id:
        return jsonify({
            "status": "error",
            "code": 400,
            "message": "缺少 user_id 字段",
            "result": None
        }), 400

    connection = get_db_connection()
    cursor = connection.cursor(dictionary=True)

    try:
        # 查询当前用户已生成的 APIKEY 数量
        count_query = "SELECT COUNT(*) AS count FROM user_apikeys WHERE user_id = %s"
        cursor.execute(count_query, (user_id,))
        result = cursor.fetchone()
        apikey_count = result['count']

        if apikey_count >= 5:
            return jsonify({
                "status": "error",
                "code": 403,
                "message": "APIKEY 已达到最大限制（5 个）",
                "result": None
            }), 403

        # 生成 API Key
        apikey = (
            str(uuid.uuid4().hex[:4]) + '-' +
            str(uuid.uuid4().hex[4:8]) + '-' +
            str(uuid.uuid4().hex[8:12]) + '-' +
            str(uuid.uuid4().hex[12:16])
        )

        # 生成 session_id
        session_id = 10001 + apikey_count  # 注意保留为 int 类型

        # 插入到 user_apikeys 表：包括 user_id, api_key, session_id
        insert_key_query = """
            INSERT INTO user_apikeys (user_id, api_key, session_id)
            VALUES (%s, %s, %s)
        """
        cursor.execute(insert_key_query, (user_id, apikey, session_id))

        # 插入到 user_chat 表：包括 user_id, session_id, histext=NULL
        insert_chat_query = """
            INSERT INTO user_chat (user_id, session_id, histext)
            VALUES (%s, %s, %s)
        """
        cursor.execute(insert_chat_query, (user_id, str(session_id), None))

        # 提交事务
        connection.commit()

        return jsonify({
            "status": "success",
            "code": 200,
            "message": "APIKEY 生成成功",
            "result": {
                "apikey": apikey
            }
        })

    except Exception as e:
        connection.rollback()
        return jsonify({
            "status": "error",
            "code": 500,
            "message": f"服务器内部错误: {str(e)}",
            "result": None
        }), 500

    finally:
        cursor.close()
        connection.close()
