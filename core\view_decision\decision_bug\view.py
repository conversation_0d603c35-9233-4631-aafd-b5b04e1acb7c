#!/usr/bin/env python
# encoding:utf-8
from . import *
from flask import request, jsonify
import mysql.connector
import uuid

# 数据库连接配置
def get_db_connection():
    connection = mysql.connector.connect(
        host="localhost",
        user="root",
        password="root",
        database="w5_db"
    )
    return connection

@r.route("/bug-report", methods=['POST'])
def submit_bug_report():
    # 处理 form-data 格式的数据
    user_id = request.form.get("user_id")
    description = request.form.get("description")
    file = request.files.get("file")  # 获取上传的文件

    # 检查字段完整性
    if not user_id or not description:
        return jsonify({
            "status": "error",
            "code": 400,
            "message": "缺少必填字段 user_id 或 description",
            "result": None
        }), 400

    # 读取图片内容（如果有）
    image_data = None
    if file:
        image_data = file.read()  # 读取为二进制

    # 连接数据库并插入 BUG 数据
    connection = get_db_connection()
    cursor = connection.cursor()

    # 插入数据（包括 user_id, description 和 bug_image）
    query = """
        INSERT INTO user_bug (user_id, bug_description, bug_image)
        VALUES (%s, %s, %s)
    """
    cursor.execute(query, (user_id, description, image_data))
    connection.commit()
    inserted_bug_id = cursor.lastrowid

    cursor.close()
    connection.close()

    # 返回响应
    return jsonify({
        "status": "success",
        "code": 200,
        "message": "BUG 已提交",
        "result": {
            "bug_id": inserted_bug_id
        }
    })
