#!/usr/bin/env python
# encoding:utf-8
from . import *
from flask import request, jsonify
import requests
import mysql.connector
import re

OLLAMA_API_URL = "http://127.0.0.1:11435/api/generate"

# 🧠 获取数据库连接
def get_db_connection():
    return mysql.connector.connect(
        host="localhost",
        user="root",
        password="root",
        database="w5_db"
    )

# 🧠 构造符合 LLaMA3 模板的 Prompt
def build_chat_prompt(histext, message):
    BEGIN = "<|begin_of_text|>"
    EOH = "<|end_header_id|>"
    S_HEADER = "<|start_header_id|>"
    EOT = "<|eot_id|>"

    prompt = f"{BEGIN}{S_HEADER}system{EOH}\n你是一个经验丰富的中文网络安全专家，擅长安全分析、溯源、渗透测试与防护建议。{EOT}"

    if histext.strip():
        rounds = histext.strip().split("\n")
        for line in rounds:
            if line.startswith("Human:"):
                prompt += f"{S_HEADER}user{EOH}\n{line[len('Human: '):]}{EOT}"
            elif line.startswith("AI:"):
                prompt += f"{S_HEADER}assistant{EOH}\n{line[len('AI: '):]}{EOT}"

    prompt += f"{S_HEADER}user{EOH}\n{message}{EOT}"
    prompt += f"{S_HEADER}assistant{EOH}\n"
    return prompt

# 🧠 向模型发送请求
def generate_reply(prompt):
    try:
        response = requests.post(OLLAMA_API_URL, json={
            "model": "llama418:latest",
            "prompt": prompt,
            "stream": False,
            "options": {
                "temperature": 0.8,
                "top_p": 0.7,
                "num_predict": 600
            }
        })
        if response.status_code != 200:
            return f"Error: {response.text}"
        return response.json().get("response", "Error: No response from model")
    except Exception as e:
        return f"Error: {str(e)}"

# 🧹 清洗生成内容
def clean_reply(reply, prompt):
    reply = re.sub(re.escape(prompt), "", reply)
    reply = re.split(r'\n*Human:.*', reply)[0]
    reply = re.split(r'\n*AI:.*', reply)[0]
    return reply.strip()

# 📚 生成摘要（用于轮数少但太长）
def generate_summary(histext):
    summary_prompt = (
        "请根据以下人类与 AI 的对话内容，压缩并提炼出精简的核心信息，限制在 600 字以内。请避免冗长的描述，只保留关键信息：\n\n"
        f"{histext}\n\n请输出精简版摘要内容："
    )
    return generate_reply(summary_prompt)[:600]

# ✂️ 截取最近 n 轮完整对话（每轮 2 句）
def truncate_to_last_n_rounds(histext, n=7):
    rounds = histext.strip().split("\n")
    dialogue_pairs = []
    temp = []
    for line in rounds:
        temp.append(line)
        if len(temp) == 2:
            dialogue_pairs.append("\n".join(temp))
            temp = []
    return "\n".join(dialogue_pairs[-n:]) + "\n"

# 🔌 /chat 接口
@r.route("/chat", methods=['POST'])
def chat():
    data = request.get_json()
    user_id = data.get("user_id")
    session_id = data.get("session_id")
    message = data.get("message")

    if not user_id or not session_id or not message:
        return jsonify({
            "status": "error",
            "code": 400,
            "message": "Missing user_id, session_id or message",
            "result": None
        }), 400

    try:
        conn = get_db_connection()
        cursor = conn.cursor(dictionary=True)

        cursor.execute("SELECT histext FROM user_chat WHERE user_id=%s AND session_id=%s", (user_id, session_id))
        row = cursor.fetchone()
        if row is None:
            histext = ""
        else:
            histext = row["histext"] or ""
            if len(histext) >= 5000:
                rounds = histext.strip().split("\n")
                round_count = len([line for line in rounds if line.startswith("Human:")])
                if round_count <= 8:
                    histext = generate_summary(histext)
                else:
                    histext = truncate_to_last_n_rounds(histext, 7)

        # ✅ 使用模板构建 prompt
        prompt = build_chat_prompt(histext, message)
        raw_reply = generate_reply(prompt)

        if "Error" in raw_reply:
            return jsonify({
                "status": "error",
                "code": 500,
                "message": raw_reply
            }), 500

        reply = clean_reply(raw_reply, prompt)
        new_histext = f"{histext}Human: {message}\nAI: {reply}\n"

        if row is None:
            cursor.execute(
                "INSERT INTO user_chat (user_id, session_id, histext) VALUES (%s, %s, %s)",
                (user_id, session_id, new_histext)
            )
        else:
            cursor.execute(
                "UPDATE user_chat SET histext=%s WHERE user_id=%s AND session_id=%s",
                (new_histext, user_id, session_id)
            )

        # 🔄 同步更新 user_history
        cursor.execute("SELECT histext FROM user_history WHERE user_id=%s AND session_id=%s", (user_id, session_id))
        his_row = cursor.fetchone()
        if his_row is None:
            cursor.execute(
                "INSERT INTO user_history (user_id, session_id, hismain, histext) VALUES (%s, %s, %s, %s)",
                (user_id, session_id, message, f"Human: {message}\nAI: {reply}\n")
            )
        else:
            updated_histext = his_row["histext"] + f"Human: {message}\nAI: {reply}\n"
            cursor.execute(
                "UPDATE user_history SET histext=%s WHERE user_id=%s AND session_id=%s",
                (updated_histext, user_id, session_id)
            )

        conn.commit()
        cursor.close()
        conn.close()

        return jsonify({
            "status": "success",
            "code": 200,
            "message": "Chat processed successfully",
            "result": {
                "reply": reply,
                "session_id": session_id
            }
        })

    except Exception as e:
        return jsonify({
            "status": "error",
            "code": 500,
            "message": f"Server error: {str(e)}"
        }), 500
