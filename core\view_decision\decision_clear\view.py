#!/usr/bin/env python
# encoding:utf-8
from . import *
from flask import request, jsonify
import mysql.connector

# 数据库连接配置
def get_db_connection():
    connection = mysql.connector.connect(
        host="localhost",
        user="root",          # MySQL 用户名
        password="root",  # MySQL 密码
        database="w5_db"      # 数据库名称
    )
    return connection

@r.route("/memory/<user_id>", methods=['DELETE'])
def delete_memory(user_id):
    try:
        # 获取数据库连接
        connection = get_db_connection()
        cursor = connection.cursor()

        # 删除 user_chat 表中所有属于该 user_id 的记录
        delete_query = "DELETE FROM user_chat WHERE user_id = %s"
        cursor.execute(delete_query, (user_id,))

        # 提交更改
        connection.commit()

        # 检查删除是否成功
        if cursor.rowcount > 0:
            # 如果删除了记录
            cursor.close()
            connection.close()

            return jsonify({
                "status": "success",
                "code": 200,
                "message": "所有记忆已清除",
                "result": {}
            })
        else:
            # 如果没有找到该 user_id 的任何记录
            cursor.close()
            connection.close()

            return jsonify({
                "status": "error",
                "code": 404,
                "message": "用户记忆不存在",
                "result": None
            }), 404

    except Exception as e:
        # 捕获异常并返回错误信息
        return jsonify({
            "status": "error",
            "code": 500,
            "message": f"服务器错误: {str(e)}",
            "result": None
        }), 500
