#!/usr/bin/env python
# encoding:utf-8
from . import *
from flask import request, jsonify
import mysql.connector

# 数据库连接配置
def get_db_connection():
    connection = mysql.connector.connect(
        host="localhost",
        user="root",  # 数据库用户名
        password="root",  # 数据库密码
        database="w5_db"  # 数据库名称
    )
    return connection

@r.route("/create", methods=['POST'])
def create_session():
    # 获取请求的JSON数据
    data = request.get_json()
    
    user_id = data.get("user_id")

    if not user_id:
        return jsonify({
            "status": "error",
            "code": 400,
            "message": "Missing required parameters",
            "result": None
        }), 400
    
    # 获取数据库连接
    connection = get_db_connection()
    cursor = connection.cursor()

    try:
        # 查询当前用户已有的最大 session_id
        cursor.execute("SELECT MAX(session_id) FROM user_chat WHERE user_id = %s", (user_id,))
        result = cursor.fetchone()

        # 处理查询结果：如果没有找到记录，则返回 None，应该设置 session_id 为 1
        if result[0] is None:
            session_id = 1
        else:
            # 强制将查询的结果转换为整数，并计算下一个 session_id
            session_id = int(result[0]) + 1  # 将 result[0] 强制转换为整数再进行加法

        # 插入新会话记录到 user_chat 表
        query = "INSERT INTO user_chat (user_id, session_id) VALUES (%s, %s)"
        cursor.execute(query, (user_id, session_id))
        connection.commit()

        # 构建响应数据
        response_data = {
            "status": "success",
            "code": 200,
            "message": "Session created successfully",
            "result": {
                "session_id": session_id
            }
        }
    
    except mysql.connector.Error as err:
        connection.rollback()
        return jsonify({
            "status": "error",
            "code": 500,
            "message": "Database error: {err}"
        }), 500
    
    finally:
        cursor.close()
        connection.close()

    # 返回响应
    return jsonify(response_data)
