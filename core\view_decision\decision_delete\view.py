#!/usr/bin/env python
# encoding:utf-8
from . import *
from flask import request, jsonify
import mysql.connector

# 数据库连接配置
def get_db_connection():
    connection = mysql.connector.connect(
        host="localhost",
        user="root",          # MySQL 用户名
        password="root",  # MySQL 密码
        database="w5_db"      # 数据库名称
    )
    return connection

@r.route("/delete", methods=['DELETE'])
def delete_session():
    try:
        # 从请求中获取 user_id 和 session_id
        data = request.get_json()
        user_id = data.get("user_id")
        session_id = data.get("session_id")

        # 校验请求中是否包含必要的字段
        if not user_id or not session_id:
            return jsonify({
                "status": "error",
                "code": 400,
                "message": "Missing user_id or session_id",
                "result": None
            }), 400

        # 获取数据库连接
        connection = get_db_connection()
        cursor = connection.cursor()

        # 执行删除操作：删除 user_chat 表中指定 user_id 和 session_id 的记录
        delete_query = "DELETE FROM user_chat WHERE user_id = %s AND session_id = %s"
        cursor.execute(delete_query, (user_id, session_id))

        delete_history_query = "DELETE FROM user_history WHERE user_id = %s AND session_id = %s"
        cursor.execute(delete_history_query, (user_id, session_id))

        # 提交事务
        connection.commit()

        # 检查是否删除了记录
        if cursor.rowcount > 0:
            # 如果删除成功，返回成功响应
            cursor.close()
            connection.close()

            return jsonify({
                "status": "success",
                "code": 200,
                "message": "会话已删除",
                "result": {}
            })
        else:
            # 如果没有找到对应的会话记录
            cursor.close()
            connection.close()

            return jsonify({
                "status": "error",
                "code": 404,
                "message": "指定会话未找到",
                "result": None
            }), 404

    except Exception as e:
        # 捕获异常并返回错误信息
        return jsonify({
            "status": "error",
            "code": 500,
            "message": f"服务器错误: {str(e)}"
        }), 500
