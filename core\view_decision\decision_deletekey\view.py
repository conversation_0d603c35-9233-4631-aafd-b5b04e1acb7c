#!/usr/bin/env python
# encoding:utf-8
from . import *
from flask import request, jsonify
import mysql.connector

# 数据库连接配置
def get_db_connection():
    connection = mysql.connector.connect(
        host="localhost",
        user="root",          # MySQL 用户名
        password="root",  # MySQL 密码
        database="w5_db"      # 数据库名称
    )
    return connection

@r.route("/deletekey/<user_id>", methods=['DELETE'])
def delete_user_apikeys(user_id):
    # 检查 user_id 类型是否合法
    try:
        user_id = int(user_id)
    except ValueError:
        return jsonify({
            "status": "error",
            "code": 400,
            "message": "无效的 user_id 类型",
            "result": None
        }), 400

    try:
        # 建立数据库连接
        connection = get_db_connection()
        cursor = connection.cursor()

        # 检查是否有可删除的 API Key
        check_query = "SELECT COUNT(*) FROM user_apikeys WHERE user_id = %s"
        cursor.execute(check_query, (user_id,))
        count = cursor.fetchone()[0]

        if count == 0:
            return jsonify({
                "status": "error",
                "code": 404,
                "message": "该用户没有可删除的 API Key",
                "result": None
            }), 404

        # 删除 user_apikeys 中的数据
        delete_apikey_query = "DELETE FROM user_apikeys WHERE user_id = %s"
        cursor.execute(delete_apikey_query, (user_id,))

        # 删除 user_chat 中 session_id >= 10000 的记录
        delete_chat_query = "DELETE FROM user_chat WHERE user_id = %s AND session_id >= 10000"
        cursor.execute(delete_chat_query, (str(user_id),))  # 注意：user_chat 的 user_id 是 varchar

        connection.commit()

    except mysql.connector.Error as err:
        return jsonify({
            "status": "error",
            "code": 500,
            "message": f"MySQL 错误: {str(err)}",
            "result": None
        }), 500

    finally:
        if 'cursor' in locals(): cursor.close()
        if 'connection' in locals(): connection.close()

    return jsonify({
        "status": "success",
        "code": 200,
        "message": f"成功删除用户 {user_id} 的 {count} 个 API Key 及其对应对话历史",
        "result": {
            "deleted_count": count
        }
    }), 200
