#!/usr/bin/env python
# encoding:utf-8
from . import *
from flask import request, jsonify
import mysql.connector

# MySQL 连接配置（假设已经配置好）
def get_db_connection():
    connection = mysql.connector.connect(
        host="localhost",
        user="root",  # 数据库用户名
        password="root",  # 数据库密码
        database="w5_db"  # 数据库名称
    )
    return connection

@r.route("/hisapikeys/<user_id>", methods=['GET'])
def get_user_apikeys(user_id):
    # 确保 user_id 是整数类型
    try:
        user_id = int(user_id)
    except ValueError:
        return jsonify({
            "status": "error",
            "code": 400,
            "message": "无效的 user_id 类型",
            "result": None
        }), 400

    # 创建数据库连接
    connection = get_db_connection()
    cursor = connection.cursor(dictionary=True)

    # 查询用户历史 API Key
    query = "SELECT api_key FROM user_apikeys WHERE user_id = %s"
    cursor.execute(query, (user_id,))
    
    # 获取查询结果
    apikeys = cursor.fetchall()

    if not apikeys:
        return jsonify({
            "status": "error",
            "code": 404,
            "message": "用户未生成过 API Key",
            "result": []
        }), 404

    # 格式化查询结果
    result = []
    for apikey in apikeys:
        result.append({
            "apikey": apikey["api_key"]
        })

    # 关闭连接
    cursor.close()
    connection.close()

    # 返回用户历史生成的 API Key 列表
    return jsonify({
        "status": "success",
        "code": 200,
        "message": "",
        "result": result
    })
