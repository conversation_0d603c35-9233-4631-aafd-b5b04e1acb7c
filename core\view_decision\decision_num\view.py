#!/usr/bin/env python
# encoding:utf-8
from . import *
from flask import request, jsonify
import mysql.connector

# 数据库连接配置
def get_db_connection():
    connection = mysql.connector.connect(
        host="localhost",
        user="root",  # 数据库用户名
        password="root",  # 数据库密码
        database="w5_db"  # 数据库名称
    )
    return connection

@r.route("/chat_num/<user_id>", methods=['GET'])
def chat_num(user_id):
    # 获取数据库连接
    connection = get_db_connection()
    cursor = connection.cursor()

    try:
        # 查询指定 user_id 的所有对话数量
        cursor.execute("SELECT COUNT(*) FROM user_chat WHERE user_id = %s", (user_id,))
        result = cursor.fetchone()

        # 如果没有找到相关的对话记录，则返回 0
        chat_count = result[0] if result else 0

        # 构建响应数据
        response_data = {
            "code": 200,
            "data": {
                "chat_count": chat_count
            },
            "msg": "模型会话统计成功",
        }

    except mysql.connector.Error as err:
        return jsonify({
            "status": "error",
            "code": 500,
            "message": f"Database error: {err}",
            "result": None
        }), 500
    
    finally:
        cursor.close()
        connection.close()

    # 返回响应
    return jsonify(response_data)
