#!/usr/bin/env python
# encoding:utf-8
from . import *
from flask import request, jsonify
import mysql.connector

# 获取数据库连接
def get_db_connection():
    connection = mysql.connector.connect(
        host="localhost",
        user="root",
        password="root",
        database="w5_db"
    )
    return connection

# 路由：查询指定用户某个会话的对话历史
@r.route("/userhis", methods=['POST'])  # 改为 POST
def get_user_history_detail():
    data = request.get_json()
    user_id = data.get("user_id")
    session_id = data.get("session_id")
    # 参数校验
    if not user_id or not session_id:
        return jsonify({
            "status": "error",
            "code": 400,
            "message": "缺少 user_id 或 session_id 参数",
            "result": None
        }), 400

    try:
        conn = get_db_connection()
        cursor = conn.cursor(dictionary=True)

        # 查询对应记录
        query = "SELECT histext FROM user_history WHERE user_id = %s AND session_id = %s"
        cursor.execute(query, (user_id, session_id))
        row = cursor.fetchone()

        if not row:
            return jsonify({
                "status": "error",
                "code": 404,
                "message": "未找到对应的对话历史记录",
                "result": None
            }), 404

        return jsonify({
            "status": "success",
            "code": 200,
            "message": "查询成功",
            "result": {
                "user_id": user_id,
                "session_id": session_id,
                "histext": row["histext"]
            }
        })

    except Exception as e:
        return jsonify({
            "status": "error",
            "code": 500,
            "message": f"服务器内部错误: {str(e)}",
            "result": None
        }), 500

    finally:
        if cursor:
            cursor.close()
        if conn:
            conn.close()
