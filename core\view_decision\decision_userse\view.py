#!/usr/bin/env python
# encoding:utf-8
from . import *
from flask import request, jsonify
import mysql.connector

# MySQL 连接配置（假设已经配置好）
def get_db_connection():
    connection = mysql.connector.connect(
        host="localhost",
        user="root",  # 数据库用户名
        password="root",  # 数据库密码
        database="w5_db"  # 数据库名称
    )
    return connection

@r.route("/userse/<user_id>", methods=['GET'])
def get_user_sessions(user_id):
    # 确保 user_id 是整数类型
    try:
        user_id = int(user_id)
    except ValueError:
        return jsonify({
            "status": "error",
            "code": 400,
            "message": "无效的 user_id 类型",
            "result": None
        }), 400

    # 建立数据库连接
    connection = get_db_connection()
    cursor = connection.cursor(dictionary=True)

    # 查询 user_history 中该用户的所有会话记录
    query = "SELECT session_id, hismain FROM user_history WHERE user_id = %s"
    cursor.execute(query, (user_id,))

    sessions = cursor.fetchall()

    if not sessions:
        return jsonify({
            "status": "error",
            "code": 404,
            "message": "该用户暂无会话记录",
            "result": []
        }), 404

    # 格式化返回结果
    result = []
    for session in sessions:
        result.append({
            "session_id": session["session_id"],
            "hismain": session["hismain"]
        })

    # 关闭连接
    cursor.close()
    connection.close()

    # 返回成功响应
    return jsonify({
        "status": "success",
        "code": 200,
        "message": "用户会话记录获取成功",
        "result": result
    })
