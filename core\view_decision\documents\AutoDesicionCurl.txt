# For api ids_info
curl -X POST http://127.0.0.1:8888/api/v1/decision/event/send -H "Content-Type: application/json" -d '{
  "event_id": "env-123",
  "event_type": "Error",
  "severity": "High",
  "detected_at": "2025-03-18",
  "details": "示例错误详情"
}'

"status": 0/1/2(分别表示没处理，已经生成剧本，已经执行决策)

# For api update_status
curl --location --request PATCH 'http://127.0.0.1:8888/api/v1/decision/event/update_status' \
--header 'Content-Type: application/json' \
--data-raw '{
    "event_id": "env-123",
    "new_status": 2
}'

# For get the solution (response_suggestions)
curl --location --request GET 'http://127.0.0.1:8888/api/v1/decision/response_suggestions?event_name=DDOS&event_id=env-123'



