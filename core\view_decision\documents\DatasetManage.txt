# API for Dataset manage API data_getall
curl -X GET "http://localhost:8888/api/v1/decision/datasets"

## 2 API for get one dataseset data_getone
curl -X GET http://localhost:8888/api/v1/decision/datasets/4


## 3 API创建单条数据集 data_create
### 成功测试
curl -X POST http://localhost:8888/api/v1/decision/datasets/create \
  -F "name=Demo Dataset" \
  -F "size=1024" \
  -F "num_samples=500" \
  -F "format=TXT" \
  -F "storage_path=/mnt/soar/data/" \
  -F "owner=vin" \
  -F "description=SOAR demo dataset" \
  -F "tags=soar,test" \
  -F "version=v1.0.1" \
  -F "file=@/home/<USER>/soar/text.txt"

## 4 删除数据集操作 data_delete
### demo
curl -X POST http://localhost:8888/api/v1/decision/datasets/delete \
-H "Content-Type: application/json" \
-d '{"id": 1}'


## 5 文件上传
### 正常上传
curl -X POST http://localhost:8888/api/v1/decision/datasets/upload \
    -F "uploader=test_user" \
    -F "dataset_id=123" \
    -F "file=@/path/to/empty_file.csv" 


## 6 获得上传记录 data_uprecords
curl -X GET "http://localhost:8888/api/v1/decision/datasets/uprecords?dataset_id=1"
curl -X GET "http://localhost:8888/api/v1/decision/datasets/uprecords?uploader=Alice"


## 7 获得下载链接 data_geturls
curl -X GET "http://localhost:8888/api/v1/decision/datasets/download?dataset_id=2"

curl -O -J "http://localhost:8888/api/v1/decision/datasets/download/2"



## 9 获得操作日志 data_logs
curl -X GET "http://localhost:8888/api/v1/decision/datasets/logs?dataset_id=1&log_type=update&limit=10"


## 10 更新状态 data_chstatus
curl -X POST "http://localhost:8888/api/v1/decision/datasets/update/status" \
     -H "Content-Type: application/json" \
     -d '{
           "dataset_id": 1,
           "new_status": "active"
         }'

## 11 文件预览 data_preview
curl -X GET "http://localhost:8888/api/v1/decision/datasets/preview?file_path=/data/ai_dataset/sample.csv&limit=5"



