<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <title>Neo4j Visualization with Neovis</title>
  <script src="https://cdn.jsdelivr.net/npm/neovis.js@2.0.2"></script>
  <style>
    html, body {
      margin: 0;
      padding: 0;
      height: 100%;
    }
    #viz {
      width: 100%;
      height: 90vh;
    }
    #export {
      margin: 10px;
    }
  </style>
</head>
<body>
  <button id="export">导出 PNG</button>
  <div id="viz"></div>

  <script>
    // 初始化配置
    const config = {
      containerId: "viz",
      neo4j: {
        serverUrl: "bolt://localhost:7687",
        serverUser: "neo4j",
        serverPassword: "cyw123857496"
      },
      labels: {
        // 可根据需要自定义标签样式
        Person: {
          caption: "name",
          size: "pagerank",
          community: "community"
        }
      },
      relationships: {
        "*": {
          thickness: "weight",
          caption: false
        }
      },
      initialCypher: "MATCH (n)-[r]->(m) RETURN n, r, m"
    };

    // 创建并渲染图
    const viz = new NeoVis.default(config);
    viz.render();

    // 导出为PNG图片
    document.getElementById("export").onclick = function () {
      const canvas = document.querySelector("canvas");
      const link = document.createElement("a");
      link.download = "graph.png";
      link.href = canvas.toDataURL("image/png");
      link.click();
    };
  </script>
</body>
</html>
