curl -X POST http://127.0.0.1:8888/api/v1/decision/hoverball/GraphDecision \
     -H "Content-Type: application/json" \
     -d '{
           "event_id": "E1002",
           "event_name": "DDOS 攻击",
           "description": "大规模分布式拒绝服务攻击",
           "limit": 1,
           "format_solution": 0
         }'

curl -X POST http://127.0.0.1:8888/api/v1/decision/hoverball/DPDecision \
     -H "Content-Type: application/json" \
     -d '{
           "event_name": "DNS毒化攻击",
           "event_description": "Detected a DDoS attack originating from IP ************, targeting multiple destinations with a horizontal port scan on port 23/TCP.",
           "decision_count": 1,
           "complexity_level": 1,
           "required_steps": [12, 32, 8],
           "format_output": 0,
           "optimize_solution": 0,
           "deep_reasoning": 0
         }'

# 📘 /hoverball/SmartDecision 接口参数说明文档

该接口用于调用 DeepSeek 大模型，实现基于攻击事件的智能决策生成，支持丰富的可控参数定制。

---

## 🧾 参数说明表格

| 参数名               | 类型         | 是否必填 | 默认值 | 说明 |
|---------------------|--------------|----------|--------|------|
| `event_name`         | `string`     | ✅       | 无     | 安全事件名称，例如：“DNS毒化攻击”。 |
| `event_description`  | `string`     | ✅       | 无     | 对事件的简要描述，例如：“检测到来自************的DDoS攻击”。 |
| `decision_count`     | `int`        | ❌       | `1`    | 返回的策略条数，支持多策略对比，建议范围：1-3。 |
| `complexity_level`   | `int`        | ❌       | `1`    | 决策链复杂度控制：<br>1 = 简洁方案，<br>2 = 适中方案，<br>3 = 复杂完整方案。 |
| `required_steps`     | `list[int]`  | ❌       | `[]`   | 指定必须包含的程序行为行号，如 `[12, 32, 8]`。 |
| `format_output`      | `int`        | ❌       | `1`    | 输出格式控制：<br>0 = 返回标准链格式，<br>1 = 转换为自然语言说明。 |
| `optimize_solution`  | `int`        | ❌       | `1`    | 是否推荐最优解：<br>1 = 是（默认），<br>0 = 否。 |
| `deep_reasoning`     | `int`        | ❌       | `0`    | 是否启用深度推理：<br>1 = 是，<br>0 = 否（默认）。 |

---

## 📤 示例请求 JSON

```json
{
  "event_name": "DNS毒化攻击",
  "event_description": "Detected a DDoS attack originating from IP ************, targeting multiple destinations with a horizontal port scan on port 23/TCP.",
  "decision_count": 2,
  "complexity_level": 3,
  "required_steps": [12, 32, 8],
  "format_output": 1,
  "optimize_solution": 1,
  "deep_reasoning": 1
}
```

---

## 📡 示例 curl 调用

```bash
curl -X POST http://127.0.0.1:8888/api/v1/decision/hoverball/SmartDecision \
     -H "Content-Type: application/json" \
     -d '{
           "event_name": "DNS毒化攻击",
           "event_description": "Detected a DDoS attack originating from IP ************, targeting multiple destinations with a horizontal port scan on port 23/TCP.",
           "decision_count": 2,
           "complexity_level": 3,
           "required_steps": [12, 32, 8],
           "format_output": 1,
           "optimize_solution": 1,
           "deep_reasoning": 1
         }'
```

---

如需返回标准链格式，只需将 `format_output` 改为 `0`。


