# 用例说明：
# 该 cURL 命令用于测试 /alert/decision 接口，传入 alert_id=alert-002 及所有参数的默认值。
# 期望行为：接口将从 w5_alert 表获取 alert-002 的事件信息，调用 AI 决策引擎分别生成自然语言和结构化决策，
# 并自动完成决策标记、分析记录及表关联等操作，最终返回所有有效数据。
curl -X POST "http://127.0.0.1:8888/api/v1/decision/alert/decision" \
  -H "Content-Type: application/json" \
  -d '{
    "alert_id": "alert-002",
    "complexity_level": 1,
    "decision_count": 1,
    "required_steps": [],
    "optimize_solution": 0,
    "deep_reasoning": 0
}'


# 用例说明：
# 测试 /alert/parseaction 接口，传入 analyse_id（如 alert-001），
# 期望自动解析、补全参数、生成剧本并导入，返回workflow uuid
curl -X POST "http://127.0.0.1:8888/api/v1/decision/alert/parseaction" \
  -H "Content-Type: application/json" \
  -d '{"analyse_id": "alert-001", "token": "W5_TOKEN_254F524357AF306050D9A193A6DEE1D0"}'

