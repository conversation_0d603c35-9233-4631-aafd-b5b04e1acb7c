API路径和名称的设计：  
1. **指定数据库中事件的增、删、改、查**  
   - **新增事件**  ✅
     - **路径：** `/kgraph/insert`  
     - **名称：** `kgraph_insert`  
提供id
curl -X POST http://127.0.0.1:8888/api/v1/decision/kgraph/insert \
     -H "Content-Type: application/json" \
     -d '{
           "event_id": "12345",
           "event_name": "服务器入侵检测",
           "description": "检测到异常登录行为",
           "harm_name": "远程控制",
           "prevention_measures": "加强登录认证"
         }'
不提供id
curl -X POST http://127.0.0.1:8888/api/v1/decision/kgraph/insert \
     -H "Content-Type: application/json" \
     -d '{
           "event_name": "SQL 注入攻击",
           "description": "检测到恶意 SQL 语句",
           "harm_name": "数据库被篡改"
         }'
缺失必要
curl -X POST http://127.0.0.1:8888/api/v1/decision/kgraph/insert \
     -H "Content-Type: application/json" \
     -d '{
           "description": "服务器异常访问",
           "harm_name": "权限提升"
         }'
   - **删除事件**  
     - **路径：** `/kgraph/delete`  
     - **名称：** `kgraph_delete`  
curl -X POST "http://127.0.0.1:8888/api/v1/decision/kgraph/delete" \
     -H "Content-Type: application/json" \
     -d '{"event_id": "12345"}'
curl -X POST "http://127.0.0.1:8888/api/v1/decision/kgraph/delete" \
     -H "Content-Type: application/json" \
     -d '{"event_id": "not-exist-id"}'
curl -X POST "http://127.0.0.1:8888/api/v1/decision/kgraph/delete" \
     -H "Content-Type: application/json" \
     -d '{}'
curl -X POST "http://127.0.0.1:8888/api/v1/decision/kgraph/delete" \
     -H "Content-Type: application/json" \
     -d ''
   - **更新事件**  
     - **路径：** `/kgraph/update`  
     - **名称：** `kgraph_update`  
curl -X POST "http://127.0.0.1:8888/api/v1/decision/kgraph/update" \
     -H "Content-Type: application/json" \
     -d '{
            "event_id": "12345",
            "event_name": "Updated Event Name",
            "description": "Updated description"
        }'
   - **查询事件**  
     - **路径：** `/kgraph/get`  
     - **名称：** `kgraph_get`  
curl -X GET "http://127.0.0.1:8888/api/v1/decision/kgraph/get?event_name=SQL&page=1&page_size=5"
curl -X GET "http://127.0.0.1:8888/api/v1/decision/kgraph/get?event_id=123e4567-e89b-12d3-a456-426614174000&page=1&page_size=5"
curl -X GET "http://127.0.0.1:8888/api/v1/decision/kgraph/get?page=1&page_size=5"
2. **导出 MySQL 中所有数据为一个 JSON 文件**  
   - **路径：** `/kgraph/export`  
   - **名称：** `kgraph_export`  
curl -X GET "http://127.0.0.1:8888/api/v1/decision/kgraph/export" -o decision_info_export.json

3. **自动化重新构建知识图谱** √
   - **路径：** `/kgraph/rebuild`  
   - **名称：** `kgraph_rebuild`  
 curl http://127.0.0.1:8888/api/v1/decision/kgraph/rebuild

4. **返回知识图谱上次更新时间** 
   - **路径：** `/kgraph/getUptime`  
   - **名称：** `kgraph_getUptime`
curl -X GET http://127.0.0.1:8888/api/v1/decision/kgraph/getUptime

5. **图谱可视化显示**  直接保存到当前API路径下了，后续可以修改考虑其它路径
   - **路径：** `/kgraph/visualization`  
   - **名称：** `kgraph_visualization`  
curl -X GET http://127.0.0.1:8888/api/v1/decision/kgraph/visualization --output visualization.png
推荐前端渲染实现(示例，示例图在文件夹内)，基于 neovis.js 的前端页面，调用API获取数据并展示/导出图，后端目前没有直接手段能够去渲染出高质量可视化图：
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <title>Neo4j Visualization with Neovis</title>
  <script src="https://cdn.jsdelivr.net/npm/neovis.js@2.0.2"></script>
  <style>
    html, body {
      margin: 0;
      padding: 0;
      height: 100%;
    }
    #viz {
      width: 100%;
      height: 90vh;
    }
    #export {
      margin: 10px;
    }
  </style>
</head>
<body>
  <button id="export">导出 PNG</button>
  <div id="viz"></div>

  <script>
    // 初始化配置
    const config = {
      containerId: "viz",
      neo4j: {
        serverUrl: "bolt://localhost:7687",
        serverUser: "neo4j",
        serverPassword: "cyw123857496"
      },
      labels: {
        // 可根据需要自定义标签样式
        Person: {
          caption: "name",
          size: "pagerank",
          community: "community"
        }
      },
      relationships: {
        "*": {
          thickness: "weight",
          caption: false
        }
      },
      initialCypher: "MATCH (n)-[r]->(m) RETURN n, r, m"
    };

    // 创建并渲染图
    const viz = new NeoVis.default(config);
    viz.render();

    // 导出为PNG图片
    document.getElementById("export").onclick = function () {
      const canvas = document.querySelector("canvas");
      const link = document.createElement("a");
      link.download = "graph.png";
      link.href = canvas.toDataURL("image/png");
      link.click();
    };
  </script>
</body>
</html>
