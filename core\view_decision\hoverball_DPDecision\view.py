import requests 
from flask import request, jsonify
from . import *

@r.route("/hoverball/DPDecision", methods=['POST'])
def post_smart_decision():
    try:
        data = request.json or {}

        # 提取参数
        event_name = data.get("event_name")
        event_description = data.get("event_description")
        decision_count = int(data.get("decision_count", 1))
        complexity_level = int(data.get("complexity_level", 1))
        required_steps = data.get("required_steps", [])  # 例如 [1, 5, 12]
        format_output = int(data.get("format_output", 1))  # 0 = 格式化，1 = 自然语言
        optimize_solution = int(data.get("optimize_solution", 1))  # 1 = 推荐最优
        deep_reasoning = int(data.get("deep_reasoning", 0))  # 1 = 开启推理

        if not event_name or not event_description:
            return jsonify({"code": 400, "msg": "参数 event_name 和 event_description 为必填"}), 400

        # 静态嵌入清单内容
        action_list = """1 bankcard 查询信息 query
2 base64 加密 encryption
3 base64 解密 decrypt
4 clickhouse 查询 query
5 dingding 钉钉通知 send
6 email 邮件发送 send
7 es 查询数据 scan
8 feishu 飞书通知 send
9 h3c 添加黑名单 add_blacklist
13 helloworld HelloWorld hello_world
14 honming 查询信息 query
15 icp 查询信息 query
16 ip IP查询 ip
17 linux 执行命令 execute
18 md5 加密 encryption
19 mysql 查询 query
20 mysql 增删改 update
21 nmap 端口扫描 scan
22 otx ioc判断 is_ioc
23 phone 归属地查询 query
24 qq 查询QQ信息 query
25 redis GET get
26 redis SET set
27 redis DEL delete
28 redis 清空数据-单个DB flushdb
29 redis 清空数据-全部DB flushall
30 serverjiang 微信通知 send
31 splunk 查询数据 scan
32 suricata 日志处理 process_logs
33 threatbook IP 分析 ip_query
34 threatbook IP 信誉 ip_reputation
35 threatbook 域名分析 domain_query
36 url 生成 make
37 whois 域名信息查询 query
38 windows 执行命令 execute
39 zhfc 中文分词 make"""

        # 构造提示词
        base_prompt = f"目前遇到的攻击事件名称：{event_name}。事件描述：{event_description}。\n"
        base_prompt += "请从下面可选程序行为清单中，组合你认为的最好的应对策略。\n"
        if required_steps:
            base_prompt += f"请务必包含以下程序行为步骤（按编号），若括号内为空则没有必须步骤要求，注意你是一名安全专家，决策中不应该只有我要求的行为，你应该自行决断: {', '.join(map(str, required_steps))}。\n"
        if complexity_level == 1:
            base_prompt += "请尽量给出完善的决策链，确保决策方案有效性。\n"
        elif complexity_level >= 3:
            base_prompt += "深度思考给出你认为的最好的决策链，长度适中，不要只有两三项\n"
        if decision_count > 1:
            base_prompt += f"请提供 {decision_count} 套不同的完整决策方案，每套方案之间用换行分隔，命名为方案一、方案二等，便于阅读，只有一套的话则不需要，返回且仅返回一条决策链的信息\n"
        if optimize_solution:
            base_prompt += "请综合考虑最佳应对效果，再给出了全部决策后加上推荐最优解。\n"
        if deep_reasoning:
            base_prompt += "请结合深度推理分析思考应对链条的合理性。\n"
        if format_output == 0:
            base_prompt += "决策应对方案请严格使用格式返回：事件名称->开始->程序 行为1->程序 行为2->...->结束。其中程序直接用清单中程序名，行为直接用清单中所选程序对应中文名，返回且仅返回决策链。\n"
        else:
            base_prompt += "请将决策方案按照下面自然语言方式返回，如：1. 事件是：xx；2. 开始；3. 调用程序xx 执行xx...\n"

        base_prompt += "\n程序清单格式如下，第一列行号，第二列程序名，第三列程序可选行为，第四列程序英文名（请不要使用）。清单具体内容如下：\n"
        base_prompt += action_list

        # 调用 DeepSeek API
        response = call_deepseek_model(base_prompt)

        return jsonify({"code": 200, "msg": "智能决策生成成功", "data": response})

    except Exception as e:
        return jsonify({"code": 500, "msg": f"服务器错误: {str(e)}"}), 500


def call_deepseek_model(user_prompt):
    system_prompt = "你是一位网络安全事件决策专家，能够根据我提供的安全事件和可选的程序与程序行为，给出且只给出格式化后的应对策略"

    url = "https://api.deepseek.com/chat/completions"
    headers = {
        "Authorization": dp_apikey,
        "Content-Type": "application/json"
    }

    data = {
        "model": "deepseek-chat",
        "messages": [
            {"role": "system", "content": system_prompt},
            {"role": "user", "content": user_prompt}
        ],
        "stream": False
    }

    response = requests.post(url, headers=headers, json=data)
    response.raise_for_status()
    return response.json()["choices"][0]["message"]["content"]
