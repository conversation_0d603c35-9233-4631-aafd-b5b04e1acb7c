#!/usr/bin/env python 
# encoding:utf-8
import pymysql
from flask import request, jsonify
from difflib import SequenceMatcher
from . import *

DB_CONFIG = {
    "host": ServerHost,
    "user": MysqlUSER,
    "password": MysqlPWD,
    "database": MysqlBase,
    "charset": SQLCharset,
    "cursorclass": pymysql.cursors.DictCursor
}


def get_db_connection():
    return pymysql.connect(**DB_CONFIG)

def similar(a, b):
    return SequenceMatcher(None, a or "", b or "").ratio()

def process_result(record, format_solution):
    config = record.get("configuration_solution", "").strip()
    event_name = record.get("event_name", "")

    # 判断 format_solution 为 1 时才进行格式化
    if format_solution == 1 and '->' in config and '开始' in config and '结束' in config:
        parts = config.split("->")
        result_lines = []
        for idx, part in enumerate(parts):
            if idx == 0:
                result_lines.append(f"{idx + 1}. 事件是：{part}")
            elif part == "开始":
                result_lines.append(f"{idx + 1}. 开始处理流程")
            elif part == "结束":
                result_lines.append(f"{idx + 1}. 结束")
            else:
                # 拆分出程序和动作（如：firewalld 阻止IP）
                if " " in part:
                    prog, action = part.split(" ", 1)
                    result_lines.append(f"{idx + 1}. 调用 {prog} 执行 {action}")
                else:
                    result_lines.append(f"{idx + 1}. 执行步骤：{part}")
        return {
            "event_name": event_name,
            "configuration_solution": "\n".join(result_lines)
        }
    else:
        # 如果 format_solution 为 0 或者 configuration_solution 不符合格式化条件，直接返回原始内容
        return {
            "event_name": event_name,
            "configuration_solution": config
        }

@r.route("/hoverball/GraphDecision", methods=['POST'])
def post_graph_decision():
    connection = None
    try:
        data = request.json or {}
        event_id = data.get("event_id")
        event_name = data.get("event_name")
        description = data.get("description")
        limit = min(max(int(data.get("limit", 5)), 1), 10)  # 限制返回数量在1到10之间，默认5
        format_solution = int(data.get("format_solution", 0))  # 是否格式化 configuration_solution，默认不格式化

        if not event_id and not event_name and not description:
            return jsonify({"code": 400, "msg": "请至少提供 event_id、event_name 或 description 参数"}), 400

        connection = get_db_connection()
        with connection.cursor() as cursor:

            # 1. event_id 精确匹配
            if event_id:
                cursor.execute("SELECT configuration_solution, event_name FROM decision_info WHERE event_id = %s", (event_id,))
                result = cursor.fetchone()
                if result:
                    return jsonify({"code": 200, "msg": "通过 event_id 精确匹配成功", "data": [process_result(result, format_solution)]})

            # 2. event_name 精确匹配
            if event_name:
                cursor.execute("SELECT configuration_solution, event_name FROM decision_info WHERE event_name = %s", (event_name,))
                result = cursor.fetchone()
                if result:
                    return jsonify({"code": 200, "msg": "通过 event_name 精确匹配成功", "data": [process_result(result, format_solution)]})

            # 3. event_name + description 语义匹配
            cursor.execute("SELECT configuration_solution, event_name, description FROM decision_info")
            all_records = cursor.fetchall()

            scored_results = []
            for record in all_records:
                score = 0
                if event_name:
                    score += similar(record.get("event_name"), event_name)
                if description:
                    score += similar(record.get("description"), description)
                scored_results.append((score, record))

            scored_results.sort(reverse=True, key=lambda x: x[0])
            top_matches = [r for s, r in scored_results if s >= 0.5][:limit]

            if not top_matches and scored_results:
                top_matches = [scored_results[0][1]]

            if top_matches:
                return jsonify({
                    "code": 200,
                    "msg": "通过语义匹配找到最相近结果",
                    "data": [process_result(r, format_solution) for r in top_matches]
                })

            return jsonify({"code": 404, "msg": "未找到匹配结果"}), 404

    except pymysql.MySQLError as e:
        return jsonify({"code": 500, "msg": f"数据库错误: {str(e)}"}), 500

    finally:
        if connection:
            connection.close()
