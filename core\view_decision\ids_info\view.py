#!/usr/bin/env python
# encoding:utf-8

from flask import request, jsonify
import pymysql
from . import *
from datetime import datetime

@r.route("/event/send", methods=['GET', 'POST'])
def send_event():
    if request.method == 'POST':
        if not request.is_json:
            return jsonify({"code": 400, "msg": "请求必须是 JSON 格式", "data": None})

        data = request.get_json()
        required_fields = ['event_id', 'event_type', 'severity', 'detected_at', 'details']
        for field in required_fields:
            if field not in data:
                return jsonify({"code": 400, "msg": f"缺少必要字段: {field}", "data": None})

        event_id = data['event_id']
        event_type = data['event_type']
        severity = data['severity']
        detected_at = data['detected_at']
        details = data['details']
        status = 0  # 默认未处理

        connection = None
        try:
            connection = pymysql.connect(
                host='localhost',
                user='root',
                password='root',
                database='w5_db',
                charset='utf8mb4',
                cursorclass=pymysql.cursors.DictCursor
            )

            with connection.cursor() as cursor:
                # 检查表是否存在
                cursor.execute("""
                    SELECT COUNT(*)
                    FROM information_schema.tables 
                    WHERE table_schema = 'w5_db' 
                    AND table_name = 'decision_idsinfo';
                """)
                result = cursor.fetchone()

                if result['COUNT(*)'] == 0:
                    create_table_sql = """
                    CREATE TABLE decision_idsinfo (
                        id INT AUTO_INCREMENT PRIMARY KEY,
                        event_id VARCHAR(255) NOT NULL,
                        event_type VARCHAR(255) NOT NULL,
                        severity VARCHAR(255) NOT NULL,
                        detected_at DATETIME NOT NULL,
                        details TEXT NOT NULL,
                        status INT NOT NULL DEFAULT 0
                    ) CHARACTER SET utf8mb4;
                    """
                    cursor.execute(create_table_sql)

                insert_sql = """
                INSERT INTO decision_idsinfo (event_id, event_type, severity, detected_at, details, status)
                VALUES (%s, %s, %s, %s, %s, %s);
                """
                cursor.execute(insert_sql, (event_id, event_type, severity, detected_at, details, status))
                connection.commit()

            return jsonify({"code": 200, "msg": "事件信息发送并存储成功", "data": None})

        except pymysql.MySQLError as e:
            return jsonify({"code": 500, "msg": f"数据库错误: {str(e)}", "data": None})

        finally:
            if connection:
                try:
                    connection.close()
                except Exception:
                    pass

    else:
        return jsonify({"code": 405, "msg": "仅支持 POST 请求", "data": None})
