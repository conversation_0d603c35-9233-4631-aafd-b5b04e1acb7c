#!/usr/bin/env python
# encoding:utf-8
import pymysql
from flask import request, jsonify
from . import *

# 数据库连接配置
DB_CONFIG = {
    "host": ServerHost,
    "user": MysqlUSER,
    "password": MysqlPWD,
    "database": MysqlBase,
    "charset": SQLCharset,
    "cursorclass": pymysql.cursors.DictCursor
}


def get_db_connection():
    """获取数据库连接"""
    return pymysql.connect(**DB_CONFIG)

@r.route("/kgraph/delete", methods=['POST'])
def delete_kgraph():
    connection = None
    try:
        # 解析请求数据
        data = request.get_json()
        if not data:
            return jsonify({"code": 400, "msg": "请求体不能为空", "data": None})

        event_id = data.get("event_id")
        if not event_id:
            return jsonify({"code": 400, "msg": "缺少必填字段: event_id", "data": None})

        # 连接数据库
        connection = get_db_connection()
        with connection.cursor() as cursor:
            # 检查 event_id 是否存在
            check_query = "SELECT event_id FROM decision_info WHERE event_id = %s"
            cursor.execute(check_query, (event_id,))
            result = cursor.fetchone()

            if not result:
                return jsonify({"code": 404, "msg": "event_id 不存在，无法删除", "data": None})

            # 执行删除操作
            delete_query = "DELETE FROM decision_info WHERE event_id = %s"
            cursor.execute(delete_query, (event_id,))
            connection.commit()

        return jsonify({"code": 200, "msg": f"event_id {event_id} 删除成功", "data": None})

    except pymysql.MySQLError as e:
        return jsonify({"code": 500, "msg": f"数据库错误: {e}", "data": None})

    finally:
        try:
            if connection:
                connection.close()
        except NameError:
            pass
