#!/usr/bin/env python
# encoding:utf-8
import pymysql
import json
import os
from flask import request, jsonify, send_file
from io import BytesIO
from . import *

# 数据库连接配置
DB_CONFIG = {
    "host": ServerHost,
    "user": MysqlUSER,
    "password": MysqlPWD,
    "database": MysqlBase,
    "charset": SQLCharset,
    "cursorclass": pymysql.cursors.DictCursor
}


def get_db_connection():
    """获取数据库连接"""
    return pymysql.connect(**DB_CONFIG)

@r.route("/kgraph/export", methods=['GET'])
def export_kgraph():
    connection = None
    try:
        # 连接数据库
        connection = get_db_connection()
        with connection.cursor() as cursor:
            # 执行查询，获取所有数据
            cursor.execute("SELECT * FROM decision_info")
            result = cursor.fetchall()

            # 转换数据为 JSON 格式
            json_data = json.dumps(result, ensure_ascii=False, indent=4)

            # 将数据写入内存中的临时文件
            file = BytesIO()
            file.write(json_data.encode('utf-8'))
            file.seek(0)

            # 返回文件作为响应下载
            return send_file(file, as_attachment=True, attachment_filename="decision_info_export.json", mimetype='application/json')

    except pymysql.MySQLError as e:
        return jsonify({"code": 500, "msg": f"数据库错误: {e}"}), 500
    except Exception as e:
        return jsonify({"code": 500, "msg": f"导出文件错误: {e}"}), 500
    finally:
        try:
            if connection:
                connection.close()
        except NameError:
            pass
