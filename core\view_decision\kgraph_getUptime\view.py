#!/usr/bin/env python
# encoding:utf-8
import os
from flask import jsonify
from . import *

@r.route("/kgraph/getUptime", methods=['GET'])
def get_uptime():
    file_path = os.path.join(os.path.dirname(__file__), "..", "kgraph_rebuild", "updatetime.txt")
    file_path = os.path.abspath(file_path)

    print(f"调试信息：文件路径为: {file_path}")

    if not os.path.exists(file_path):
        return jsonify({"code": 404, "msg": "暂无记录，尚未重建图谱", "data": None})

    try:
        with open(file_path, "r", encoding="utf-8") as f:
            lines = f.readlines()
            if not lines:
                return jsonify({"code": 204, "msg": "文件为空，暂无记录", "data": None})
            last_time = lines[-1].strip()
            return jsonify({"code": 200, "msg": "获取成功", "data": last_time})
    except Exception as e:
        return jsonify({"code": 500, "msg": f"读取文件失败: {str(e)}", "data": None})
