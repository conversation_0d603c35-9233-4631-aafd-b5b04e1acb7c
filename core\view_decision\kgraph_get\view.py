#!/usr/bin/env python
# encoding:utf-8
import pymysql
from flask import request, jsonify
from . import *

DB_CONFIG = {
    "host": ServerHost,
    "user": MysqlUSER,
    "password": MysqlPWD,
    "database": <PERSON>sqlBase,
    "charset": SQLCharset,
    "cursorclass": pymysql.cursors.DictCursor
}


def get_db_connection():
    return pymysql.connect(**DB_CONFIG)

@r.route("/kgraph/get", methods=['GET'])
def get_kgraph():
    connection = None
    try:
        event_id = request.args.get('event_id', default=None)
        event_name = request.args.get('event_name', default=None)
        page = int(request.args.get('page', default=1))
        page_size = int(request.args.get('page_size', default=10))

        if page <= 0 or page_size <= 0:
            return jsonify({"code": 400, "msg": "无效的分页参数", "data": None})

        connection = get_db_connection()
        with connection.cursor() as cursor:
            query = "SELECT * FROM decision_info WHERE 1=1"
            params = []

            if event_id:
                query += " AND event_id LIKE %s"
                params.append(f"%{event_id}%")
            if event_name:
                query += " AND event_name LIKE %s"
                params.append(f"%{event_name}%")

            query += " LIMIT %s OFFSET %s"
            params.append(page_size)
            params.append((page - 1) * page_size)
            cursor.execute(query, tuple(params))
            result = cursor.fetchall()

            count_query = "SELECT COUNT(*) FROM decision_info WHERE 1=1"
            count_params = []
            if event_id:
                count_query += " AND event_id LIKE %s"
                count_params.append(f"%{event_id}%")
            if event_name:
                count_query += " AND event_name LIKE %s"
                count_params.append(f"%{event_name}%")
            cursor.execute(count_query, tuple(count_params))
            total_count = cursor.fetchone()['COUNT(*)']

            response_data = {
                "total_count": total_count,
                "page": page,
                "page_size": page_size,
                "data": result
            }

        return jsonify({"code": 200, "msg": "查询成功", "data": response_data})

    except pymysql.MySQLError as e:
        return jsonify({"code": 500, "msg": f"数据库错误: {e}", "data": None})

    finally:
        try:
            if connection:
                connection.close()
        except NameError:
            pass
