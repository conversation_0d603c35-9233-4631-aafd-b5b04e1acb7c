#!/usr/bin/env python
# encoding:utf-8
import pymysql
import uuid
from flask import request, jsonify
from . import *

# 数据库连接配置
DB_CONFIG = {
    "host": ServerHost,
    "user": MysqlUSER,
    "password": MysqlPWD,
    "database": MysqlBase,
    "charset": SQLCharset,
    "cursorclass": pymysql.cursors.DictCursor
}


def get_db_connection():
    """获取数据库连接"""
    return pymysql.connect(**DB_CONFIG)

@r.route("/kgraph/insert", methods=['POST'])
def insert_kgraph():
    connection = None
    try:
        # 解析请求数据
        data = request.get_json()
        if not data:
            return jsonify({"code": 400, "msg": "请求体不能为空", "data": None})

        # 必填字段校验
        required_fields = ["event_name", "description", "harm_name"]
        for field in required_fields:
            if field not in data or not data[field]:
                return jsonify({"code": 400, "msg": f"缺少必填字段: {field}", "data": None})

        # 生成 event_id（如果未提供）
        event_id = data.get("event_id", str(uuid.uuid4()))

        # 允许插入的字段
        allowed_fields = [
            "event_id", "event_name", "maintenance_method", "check_item",
            "device_software_id", "vendor_name", "harm_name", "description",
            "prevention_measures", "attack_cause", "defective_device_software",
            "configuration_solution"
        ]

        # 过滤数据
        insert_data = {key: data.get(key, None) for key in allowed_fields}
        insert_data["event_id"] = event_id

        # 执行插入
        connection = get_db_connection()
        with connection.cursor() as cursor:
            insert_query = """
                INSERT INTO decision_info (
                    event_id, event_name, maintenance_method, check_item, device_software_id,
                    vendor_name, harm_name, description, prevention_measures, attack_cause,
                    defective_device_software, configuration_solution
                ) VALUES (
                    %(event_id)s, %(event_name)s, %(maintenance_method)s, %(check_item)s, %(device_software_id)s,
                    %(vendor_name)s, %(harm_name)s, %(description)s, %(prevention_measures)s, %(attack_cause)s,
                    %(defective_device_software)s, %(configuration_solution)s
                )
                ON DUPLICATE KEY UPDATE
                    event_name = VALUES(event_name),
                    maintenance_method = VALUES(maintenance_method),
                    check_item = VALUES(check_item),
                    device_software_id = VALUES(device_software_id),
                    vendor_name = VALUES(vendor_name),
                    harm_name = VALUES(harm_name),
                    description = VALUES(description),
                    prevention_measures = VALUES(prevention_measures),
                    attack_cause = VALUES(attack_cause),
                    defective_device_software = VALUES(defective_device_software),
                    configuration_solution = VALUES(configuration_solution)
            """
            cursor.execute(insert_query, insert_data)
            connection.commit()

        return jsonify({"code": 200, "msg": "数据插入成功", "data": {"event_id": event_id}})

    except pymysql.MySQLError as e:
        return jsonify({"code": 500, "msg": f"数据库错误: {e}", "data": None})

    finally:
        try:
            if connection:
                connection.close()
        except NameError:
            pass
