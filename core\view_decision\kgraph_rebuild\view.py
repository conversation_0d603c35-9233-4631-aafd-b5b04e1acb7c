#!/usr/bin/env python
# encoding:utf-8
import os
from datetime import datetime
import pymysql
from flask import request, jsonify
from py2neo import Graph, Node, Relationship
from . import *

# 数据库连接配置
DB_CONFIG = {
    "host": ServerHost,
    "user": MysqlUSER,
    "password": MysqlPWD,
    "database": MysqlBase,
    "charset": SQLCharset,
    "cursorclass": pymysql.cursors.DictCursor
}


# Neo4j 数据库配置
NEO4J_CONFIG = {
    "url": Neo4jUrl,
    "auth": (Neo4jUSER, Neo4jPWD)
}

def get_db_connection():
    """获取数据库连接"""
    return pymysql.connect(**DB_CONFIG)

# 创建并连接到 Neo4j 数据库
def create_graph_connection():
    return Graph(NEO4J_CONFIG["url"], auth=NEO4J_CONFIG["auth"])

# 从数据库读取数据，返回统一格式
def read_data_from_db():
    connection = None
    try:
        connection = get_db_connection()
        with connection.cursor() as cursor:
            query = "SELECT * FROM decision_info"
            cursor.execute(query)
            results = cursor.fetchall()
            return {"code": 200, "msg": "查询成功", "data": results}
    except pymysql.MySQLError as e:
        return {"code": 500, "msg": f"数据库错误: {e}", "data": None}
    finally:
        if connection:
            connection.close()

# 创建节点
def create_node(g, label, nodes, properties, color):
    for node_name in nodes:
        node_properties = properties.get(node_name, {})
        node = Node(label, name=node_name, color=color, **node_properties)
        g.create(node)

# 创建关系
def create_relationship(g, start_node, end_node, edges, rel_type):
    for edge in edges:
        p, q = edge
        query = f"""
        MATCH (a:{start_node}),(b:{end_node})
        WHERE a.name='{p}' AND b.name='{q}'
        CREATE (a)-[r:{rel_type}]->(b)
        """
        g.run(query)

# 清空图谱
def clear_graph(g):
    g.run("MATCH (n) DETACH DELETE n")

# 记录更新时间
def write_update_time():
    now = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    file_path = os.path.join(os.path.dirname(__file__), "updatetime.txt")
    with open(file_path, "a", encoding="utf-8") as f:
        f.write(f"图谱重建时间: {now}\n")

# 记录关系数量
def write_relation_count(event_method_rels, event_device_rels, vendor_device_rels, event_check_rels, event_harm_rels):
    relation_counts = {
        'SecurityEvent-Uses-MaintenanceMethod': len(event_method_rels),
        'SecurityEvent-Affects-DeviceSoftware': len(event_device_rels),
        'Vendor-Provides-DeviceSoftware': len(vendor_device_rels),
        'SecurityEvent-Requires-CheckItem': len(event_check_rels),
        'SecurityEvent-Causes-Harm': len(event_harm_rels),
    }
    file_path = os.path.join(os.path.dirname(__file__), "realityCount.txt")
    with open(file_path, "w", encoding="utf-8") as f:
        for relation, count in relation_counts.items():
            f.write(f"{relation} 数量: {count}\n")

# 处理图谱重建，返回统一格式
def rebuild_kgraph():
    # 获取数据库数据
    result = read_data_from_db()
    if result["code"] != 200:
        return jsonify(result)

    data = result["data"]
    if not data:
        return jsonify({"code": 204, "msg": "没有数据可处理", "data": None})

    # 初始化 Neo4j 连接
    g = create_graph_connection()

    # 清空现有图谱
    clear_graph(g)

    # 初始化集合和关系容器
    events = set()
    maintenance_methods = set()
    check_items = set()
    device_software_ids = set()
    vendor_names = set()
    harm_names = set()

    event_method_rels = []
    event_device_rels = []
    vendor_device_rels = []
    event_check_rels = []
    event_harm_rels = []

    node_properties = {}

    # 遍历数据构造节点和关系
    for item in data:
        event_name = item['event_name']
        events.add(event_name)

        node_properties[event_name] = {
            'description': item.get('description', ''),
            'preventive_measures': item.get('prevention_measures', ''),
            'attack_reason': item.get('attack_cause', ''),
            'defective_software': item.get('defective_device_software', ''),
            'detailed_solution': item.get('configuration_solution', ''),
        }

        if item.get('maintenance_method'):
            methods = [m.strip() for m in item['maintenance_method'].split(',')]
            maintenance_methods.update(methods)
            event_method_rels.extend([(event_name, m) for m in methods])

        if item.get('check_item'):
            checks = [c.strip() for c in item['check_item'].split(',')]
            check_items.update(checks)
            event_check_rels.extend([(event_name, c) for c in checks])

        software_ids = []
        if item.get('device_software_id'):
            software_ids = [d.strip() for d in item['device_software_id'].split(',')]
            device_software_ids.update(software_ids)
            event_device_rels.extend([(event_name, d) for d in software_ids])

        if item.get('vendor_name'):
            vendors = [v.strip() for v in item['vendor_name'].split(',')]
            vendor_names.update(vendors)
            vendor_device_rels.extend([(v, s) for v in vendors for s in software_ids])

        if item.get('harm_name'):
            harms = [h.strip() for h in item['harm_name'].split(',')]
            harm_names.update(harms)
            event_harm_rels.extend([(event_name, h) for h in harms])

    # 创建节点
    create_node(g, 'SecurityEvent', events, node_properties, 'red')
    create_node(g, 'MaintenanceMethod', maintenance_methods, {}, 'blue')
    create_node(g, 'CheckItem', check_items, {}, 'green')
    create_node(g, 'DeviceSoftware', device_software_ids, {}, 'yellow')
    create_node(g, 'Vendor', vendor_names, {}, 'purple')
    create_node(g, 'Harm', harm_names, {}, 'orange')

    # 创建关系
    create_relationship(g, 'SecurityEvent', 'MaintenanceMethod', event_method_rels, 'USES')
    create_relationship(g, 'SecurityEvent', 'DeviceSoftware', event_device_rels, 'AFFECTS')
    create_relationship(g, 'Vendor', 'DeviceSoftware', vendor_device_rels, 'PROVIDES')
    create_relationship(g, 'SecurityEvent', 'CheckItem', event_check_rels, 'REQUIRES')
    create_relationship(g, 'SecurityEvent', 'Harm', event_harm_rels, 'CAUSES')

    # 写入更新时间和关系数量
    write_update_time()
    write_relation_count(event_method_rels, event_device_rels, vendor_device_rels, event_check_rels, event_harm_rels)

    return jsonify({"code": 200, "msg": "图谱重建成功", "data": None})

# Flask 路由
@r.route("/kgraph/rebuild", methods=['GET'])
def rebuild():
    return rebuild_kgraph()
