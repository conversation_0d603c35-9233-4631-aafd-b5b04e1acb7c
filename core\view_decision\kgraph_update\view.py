#!/usr/bin/env python
# encoding:utf-8
import pymysql
from flask import request, jsonify
from . import *

# 数据库连接配置
DB_CONFIG = {
    "host": ServerHost,
    "user": MysqlUSER,
    "password": MysqlPWD,
    "database": MysqlBase,
    "charset": SQLCharset,
    "cursorclass": pymysql.cursors.DictCursor
}


def get_db_connection():
    """获取数据库连接"""
    return pymysql.connect(**DB_CONFIG)

@r.route("/kgraph/update", methods=['POST'])
def update_kgraph():
    connection = None
    try:
        data = request.get_json()
        if not data:
            return jsonify({"code": 400, "msg": "请求体不能为空", "data": None})

        event_id = data.get("event_id")
        if not event_id:
            return jsonify({"code": 400, "msg": "缺少必填字段: event_id", "data": None})

        connection = get_db_connection()
        with connection.cursor() as cursor:
            check_query = "SELECT event_id FROM decision_info WHERE event_id = %s"
            cursor.execute(check_query, (event_id,))
            result = cursor.fetchone()

            if not result:
                return jsonify({"code": 404, "msg": "event_id 不存在，无法更新", "data": None})

            allowed_fields = [
                "event_name", "maintenance_method", "check_item", "device_software_id",
                "vendor_name", "harm_name", "description", "prevention_measures",
                "attack_cause", "defective_device_software", "configuration_solution"
            ]
            update_data = {key: data.get(key) for key in allowed_fields if key in data}

            if not update_data:
                return jsonify({"code": 400, "msg": "没有提供需要更新的字段", "data": None})

            update_query = "UPDATE decision_info SET "
            update_values = []
            for field in update_data:
                update_query += f"{field} = %s, "
                update_values.append(update_data[field])
            update_query = update_query.rstrip(", ")
            update_query += " WHERE event_id = %s"
            update_values.append(event_id)

            cursor.execute(update_query, tuple(update_values))
            connection.commit()

        return jsonify({"code": 200, "msg": f"event_id {event_id} 更新成功", "data": None})

    except pymysql.MySQLError as e:
        return jsonify({"code": 500, "msg": f"数据库错误: {e}", "data": None})

    finally:
        if connection:
            try:
                connection.close()
            except Exception:
                pass
