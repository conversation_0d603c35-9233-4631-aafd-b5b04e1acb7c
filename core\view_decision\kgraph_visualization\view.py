#!/usr/bin/env python
# encoding:utf-8
from . import *
from neo4j import GraphDatabase
import networkx as nx
import matplotlib.pyplot as plt
import matplotlib.patches as mpatches
from io import BytesIO
from flask import Response
import os
import random

uri = Neo4jUrl
username = Neo4jPWD
password = Neo4jUSER

driver = GraphDatabase.driver(uri, auth=(username, password))

def random_color():
    return "#{:06x}".format(random.randint(0, 0xFFFFFF))

def get_node_color_map(labels):
    color_map = {}
    for label in labels:
        color_map[label] = random_color()
    return color_map

@r.route("/kgraph/visualization", methods=['GET', 'POST'])
def kgraph_visualization():
    with driver.session() as session:
        result = session.run("MATCH (n)-[r]->(m) RETURN n, r, m")

        graph = nx.DiGraph()
        labels = {}
        node_types = set()
        edge_labels = {}

        for record in result:
            n = record["n"]
            m = record["m"]
            r = record["r"]

            id_n = str(n.id)
            id_m = str(m.id)

            label_n = list(n.labels)[0] if n.labels else "Node"
            label_m = list(m.labels)[0] if m.labels else "Node"

            node_types.add(label_n)
            node_types.add(label_m)

            labels[id_n] = n.get("name", label_n)
            labels[id_m] = m.get("name", label_m)

            graph.add_node(id_n, label=label_n)
            graph.add_node(id_m, label=label_m)
            graph.add_edge(id_n, id_m)

            edge_labels[(id_n, id_m)] = r.type

        color_map = get_node_color_map(node_types)

        node_colors = [color_map[graph.nodes[n]['label']] for n in graph.nodes()]
        node_labels = labels

        pos = nx.spring_layout(graph, k=0.6, iterations=50)

        plt.figure(figsize=(12, 10))
        nx.draw_networkx_nodes(graph, pos, node_color=node_colors, node_size=1500, alpha=0.9, linewidths=2)
        nx.draw_networkx_labels(graph, pos, labels=node_labels, font_size=12, font_weight='bold')
        nx.draw_networkx_edges(graph, pos, arrowstyle='-|>', arrowsize=20, edge_color='gray', width=2)
        nx.draw_networkx_edge_labels(graph, pos, edge_labels=edge_labels, font_color='green', font_size=10)

        handles = [mpatches.Patch(color=color_map[ntype], label=ntype) for ntype in node_types]
        plt.legend(handles=handles, loc='lower left', fontsize='large')
        plt.axis('off')
        plt.tight_layout()

        # 关键：保存图片到脚本所在目录
        image_path = os.path.join(os.path.dirname(__file__), "kgraph.png")
        plt.savefig(image_path, format='png', dpi=150)
        plt.close()

        # 可选：也返回图片数据（前端显示 or 下载）
        with open(image_path, "rb") as f:
            return Response(f.read(), mimetype='image/png')
