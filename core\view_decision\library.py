import os
import json
import rpyc
import shutil
import socket
import asyncio
import requests
import platform
import threading
from core.model import *
from loguru import logger
from core import (db, redis)
from core.err import *
from core.utils.times import Time
from core.utils.randoms import Random
from core.utils.file import File
from core.utils.zip import Zip
from core.utils.pages import Page
from core.utils.cloud import Cloud
from core.utils.version import Version as VersionUtil
from rpyc.utils.server import ThreadedServer
from flask import (request, current_app, Blueprint)
from werkzeug.utils import secure_filename
from core.auto.core import auto_execute, ManageTimer

# dynamic_field_map 和 static_content_map 可直接导入使用
MysqlPWD = "root"
MysqlUSER = "root"
ServerHost = "localhost"
MysqlBase = "w5_db"
SQLCharset = "utf8mb4"

Neo4jPWD = "neo4j123456"
Neo4jUSER = "neo4j"
Neo4jUrl = "bolt://localhost:7687"

ImportUrl = "http://127.0.0.1:8888/api/v1/soar/post/workflow/import"
w5_tokens = "W5_TOKEN_254F524357AF306050D9A193A6DEE1D0"

dp_apikey = "Bearer sk-ab3bc6575de84794b9919802fea310e4"

dynamic_field_map = {
    "md5": {
        "encryption": {
            "text": "signature"  # w5_alert.signature
            # type字段无合适w5_alert字段，建议静态赋值
        }
    },
    "nmap": {
        "scan": {
            "target": "destination_ip",  # w5_alert.destination_ip
            "ports": "destination_port",  # w5_alert.destination_port
            "protocol": "protocol"        # w5_alert.protocol
        }
    },
    "mysql": {
        "query": {
            "host": "source_ip",
            "port": "source_port"
            # 其它建议静态
        },
        "update": {
            "host": "source_ip",
            "port": "source_port"
        }
    },
    "linux": {
        "execute": {
            "host": "source_ip",
            "port": "source_port"
            # user, passwd, shell建议静态
        }
    },
    "redis": {
        "get": {
            "host": "source_ip",
            "port": "source_port"
            # db, password, key建议静态
        },
        "set": {
            "host": "source_ip",
            "port": "source_port"
        },
        "delete": {
            "host": "source_ip",
            "port": "source_port"
        },
        "flushdb": {
            "host": "source_ip",
            "port": "source_port"
        },
        "flushall": {
            "host": "source_ip",
            "port": "source_port"
        }
    },
    "dingding": {
        "send": {
            "msg": "signature"  # w5_alert.signature
            # access_token建议静态
        }
    },
    "windows": {
        "execute": {
            "host": "source_ip",
            "port": "source_port"
        }
    },
    "intelligent_decision-making": {
        "send_event": {
            "event_id": "alert_id",
            "event_type": "attack_type",
            "severity": "severity",
            "detected_at": "timestamp",
            "details": "signature"
        },
        "get_response_suggestions": {
            "event_id": "alert_id"
        },
        "update_event_status": {
            "event_id": "alert_id"
        }
    },
    "email": {
        "send": {
            "text": "signature",
            "title": "attack_type"
            # 其它建议静态
        }
    },
    "base64": {
        "encryption": {
            "text": "signature"
        },
        "decrypt": {
            "text": "signature"
        }
    },
    "feishu": {
        "send": {
            "msg": "signature"
            # hook_uuid静态
        }
    },
    "fscan": {
        "scan": {
            "target": "destination_ip"
            # ports静态
        }
    },
    "ip": {
        "ip": {
            "ip": "source_ip"
        }
    },
    "h3c": {
        "add_blacklist": {
    	     "ip": "source_ip"
    	 }
    }
}

static_content_map = {
    "md5": {
        "encryption": {
            "type": "md5"
        }
    },
    "nmap": {
        "scan": {
            "ports": "80,443",
            "protocol": "tcp"
        }
    },
    "mysql": {
        "query": {
            "user": "root",
            "passwd": "password",
            "db": "testdb",
            "sql": "SELECT 1"
        },
        "update": {
            "user": "root",
            "passwd": "password",
            "db": "testdb",
            "sql": "UPDATE test SET value=1"
        }
    },
    "linux": {
        "execute": {
            "user": "root",
            "passwd": "password",
            "shell": "ls"
        }
    },
    "redis": {
        "get": {
            "db": 0,
            "password": "",
            "key": "mykey"
        },
        "set": {
            "db": 0,
            "password": "",
            "key": "mykey",
            "value": "myvalue"
        },
        "delete": {
            "db": 0,
            "password": "",
            "key": "mykey"
        },
        "flushdb": {
            "db": 0,
            "password": ""
        },
        "flushall": {
            "password": ""
        }
    },
    "dingding": {
        "send": {
            "access_token": "2e6ac91ee756ff39e74d9105a9aabe8cec59c8d10747a061a02d08562e5411fc"
        }
    },
    "windows": {
        "execute": {
            "user": "Administrator",
            "passwd": "password",
            "cmd": "whoami"
        }
    },
    "intelligent_decision-making": {
        "send_event": {
            "event_id": "evt-001",
            "event_type": "未知事件",
            "severity": "中",
            "detected_at": "2025-06-12 12:00:00",
            "details": "自动生成"
        },
        "get_response_suggestions": {
            "event_id": "evt-001"
        },
        "update_event_status": {
            "event_id": "evt-001",
            "new_status": "已处理"
        }
    },
    "email": {
        "send": {
            "host": "smtp.163.com",
            "port": 465,
            "user": "<EMAIL>",
            "passwd": "UTJAgddhdBDsaU7m",
            "encrypt": "ssl",
            "sender": "<EMAIL>",
            "to": "<EMAIL>",
            "type": "plain"
        }
    },
    "base64": {
        "encryption": {},
        "decrypt": {}
    },
    "feishu": {
        "send": {
            "hook_uuid": "27bfbd32-aea8-4496-8f4c-ac4edc829a2f"
        }
    },
    "fscan": {
        "scan": {
            "ports": "80,443"
        }
    },
    "ip": {
        "ip": {
            "ip": "127.0.0.1"
        }
    },
    "h3c": {
    	"add_blacklist": {
    	    "age": 3600
    	}
    }
}
