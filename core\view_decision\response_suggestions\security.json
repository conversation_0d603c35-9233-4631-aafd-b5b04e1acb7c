[{"event_id": "ea001", "event_name": "Unauthorized Access Attempt", "maintenance_method": "Intrusion Detection System, Firewall Configuration", "check_item": "Access Logs Review, Firewall Rule Review", "device_software_id": "IDS-1.0, FW-2.5", "vendor_name": "Vendor A, Vendor B", "harm_name": "Data Breach, System Compromise", "description": "An unauthorized access attempt was detected on the network.", "prevention_measures": "Implement strong password policies, enable two-factor authentication.", "attack_cause": "Weak user credentials, Lack of network segmentation.", "defective_device_software": "IDS-1.0", "configuration_solution": "1. Increase password length to 12 characters. 2. Enable account lockout after 3 failed attempts."}, {"event_id": "ea002", "event_name": "Malware Infection Detected", "maintenance_method": "Antivirus Update, Network Segmentation", "check_item": "Malware Scan, Network Traffic Analysis", "device_software_id": "AV-3.2, NS-4.0", "vendor_name": "Vendor C", "harm_name": "Data Loss, System Downtime", "description": "A malware infection was detected on a server.", "prevention_measures": "Regular software updates, employee training on phishing attacks.", "attack_cause": "Malicious email attachment opened.", "defective_device_software": "AV-3.2", "configuration_solution": "1. Isolate infected machine from the network. 2. Perform a full malware scan."}, {"event_id": "ea003", "event_name": "Phishing Email Reported", "maintenance_method": "Email Filtering, User Awareness Training", "check_item": "Email Header Analysis, Spam Filter Review", "device_software_id": "EF-1.1, UF-2.0", "vendor_name": "Vendor D, Vendor E", "harm_name": "Credential Theft, Financial Loss", "description": "A user reported a phishing email attempting to steal credentials.", "prevention_measures": "Implement email filtering rules, conduct regular training sessions.", "attack_cause": "User clicked on a malicious link in the email.", "defective_device_software": "EF-1.1", "configuration_solution": "1. Block sender domain in email filter. 2. Notify users to change passwords."}, {"event_id": "ea004", "event_name": "数据泄露", "maintenance_method": "数据加密, 访问控制", "check_item": "访问日志审核, 权限检查", "device_software_id": "DLP-1.0, AC-2.5", "vendor_name": "Vendor F, Vendor G", "harm_name": "敏感信息泄露, 法律责任", "description": "检测到敏感数据被未经授权的用户访问。", "prevention_measures": "实施数据加密，定期更新访问权限。", "attack_cause": "内部人员失误，安全策略不完善。", "defective_device_software": "DLP-1.0", "configuration_solution": "1. 加强数据访问审核。 2. 定期评估数据保护措施。"}, {"event_id": "ea005", "event_name": "拒绝服务攻击", "maintenance_method": "流量监控, 网络隔离", "check_item": "流量分析, 服务器健康检查", "device_software_id": "DDoS-2.1, FW-3.0", "vendor_name": "<PERSON><PERSON><PERSON>", "harm_name": "系统瘫痪, 服务中断", "description": "发现针对网站的拒绝服务攻击，导致服务不可用。", "prevention_measures": "部署流量清洗服务，优化服务器配置。", "attack_cause": "攻击者利用网络漏洞发起攻击。", "defective_device_software": "FW-3.0", "configuration_solution": "1. 增加带宽，提升服务抗压能力。 2. 启用防火墙规则。"}, {"event_id": "ea006", "event_name": "恶意软件感染", "maintenance_method": "实时监控, 定期扫描", "check_item": "病毒库更新, 安全补丁检查", "device_software_id": "AV-4.0, NS-2.2", "vendor_name": "Vendor I", "harm_name": "数据损失, 系统崩溃", "description": "在员工的计算机上发现了恶意软件感染。", "prevention_measures": "定期进行安全培训，确保软件及时更新。", "attack_cause": "用户打开了带病毒的文件。", "defective_device_software": "AV-4.0", "configuration_solution": "1. 隔离受感染的设备。 2. 执行全面的病毒扫描。"}, {"event_id": "ea007", "event_name": "网络钓鱼攻击", "maintenance_method": "用户教育, 邮件过滤", "check_item": "邮件来源验证, 钓鱼邮件报告", "device_software_id": "EF-2.0, UF-1.5", "vendor_name": "<PERSON>endor J, Vendor K", "harm_name": "账号被盗, 财务损失", "description": "用户报告收到钓鱼邮件，试图盗取其凭证。", "prevention_measures": "实施强制邮件过滤规则，进行定期的安全培训。", "attack_cause": "用户点击了邮件中的恶意链接。", "defective_device_software": "EF-2.0", "configuration_solution": "1. 在邮件过滤器中屏蔽可疑发件人。 2. 通知用户更改密码。"}, {"event_id": "ea008", "event_name": "VPN配置异常", "maintenance_method": "VPN设置检查, 防火墙规则配置", "check_item": "VPN连接日志检查, 防火墙端口开放检查", "device_software_id": "VPN-1.0, FW-2.5", "vendor_name": "Vendor A, Vendor B", "harm_name": "数据泄露, 网络不稳定", "description": "检测到VPN配置异常，部分用户未能成功连接。", "prevention_measures": "确保VPN服务器配置准确，限制未授权用户的访问。", "attack_cause": "配置错误导致VPN连接不稳定，且开放端口缺乏限制。", "defective_device_software": "VPN-1.0", "configuration_solution": "1. 确保VPN使用强加密协议（如AES-256）。 2. 在防火墙中只开放VPN所需的端口，屏蔽其他非必要端口。"}, {"event_id": "ea009", "event_name": "跨站点脚本攻击", "maintenance_method": "应用防火墙设置, 输入过滤配置", "check_item": "应用日志检查, 输入验证策略检查", "device_software_id": "WAF-3.0, FW-2.5", "vendor_name": "Vendor C, Vendor B", "harm_name": "数据篡改, 隐私泄露", "description": "检测到在网站上进行的跨站脚本攻击企图。", "prevention_measures": "设置Web应用防火墙，执行严格的输入验证。", "attack_cause": "用户提交的输入未经过滤直接执行。", "defective_device_software": "WAF-3.0", "configuration_solution": "1. 在WAF中启用XSS保护规则。 2. 通过正则表达式检查用户输入，屏蔽可能的恶意脚本。"}, {"event_id": "ea010", "event_name": "VPN访问日志异常", "maintenance_method": "日志监控, 异常行为检测", "check_item": "VPN访问日志分析, 账号活动监控", "device_software_id": "SIEM-1.0, VPN-1.0", "vendor_name": "Vendor D, Vendor A", "harm_name": "未授权访问, 数据泄露", "description": "在VPN访问日志中检测到异常登录活动。", "prevention_measures": "启用多因子身份验证，定期审查访问日志。", "attack_cause": "弱密码导致的未授权访问。", "defective_device_software": "VPN-1.0", "configuration_solution": "1. 设置多因子身份验证以增加访问安全。 2. 定期检查VPN日志以识别异常行为。"}, {"event_id": "ea011", "event_name": "文件共享服务配置错误", "maintenance_method": "权限设置检查, 共享配置优化", "check_item": "共享权限检查, 文件访问日志分析", "device_software_id": "FS-2.1, VPN-1.0", "vendor_name": "<PERSON><PERSON><PERSON> E, Vendor A", "harm_name": "数据泄露, 内部信息泄漏", "description": "发现文件共享服务存在配置错误，导致内部敏感信息被暴露。", "prevention_measures": "严格控制共享文件权限，启用访问日志记录。", "attack_cause": "文件共享权限未严格配置，导致无关用户可访问。", "defective_device_software": "FS-2.1", "configuration_solution": "1. 将文件共享权限设置为仅允许授权用户访问。 2. 使用VPN加密远程访问。"}, {"event_id": "ea012", "event_name": "网络端口扫描检测", "maintenance_method": "流量监控, 防火墙规则优化", "check_item": "流量日志分析, 防火墙端口规则检查", "device_software_id": "IDS-1.0, FW-2.5", "vendor_name": "Vendor A, Vendor B", "harm_name": "潜在入侵, 资源耗尽", "description": "检测到针对网络的端口扫描活动，可能预示着潜在的入侵行为。", "prevention_measures": "限制不必要的端口，设置流量阈值报警。", "attack_cause": "外部攻击者通过端口扫描探测网络漏洞。", "defective_device_software": "IDS-1.0", "configuration_solution": "1. 通过防火墙屏蔽所有非必要端口。 2. 在IDS中设置对异常流量的实时报警。"}, {"event_id": "ea013", "event_name": "DDoS攻击", "maintenance_method": "流量监控, DDoS保护服务", "check_item": "流量分析, 服务器健康检查", "device_software_id": "DDoS-3.0, FW-3.5", "vendor_name": "Vendor X, Vendor Y", "harm_name": "服务停机, 资源耗尽", "description": "检测到分布式拒绝服务（DDoS）攻击，导致网络瘫痪。", "prevention_measures": "部署DDoS保护服务，配置网络设备的流量限制。", "attack_cause": "由僵尸网络设备生成的过量流量攻击目标网络。", "defective_device_software": "FW-3.5", "configuration_solution": "DDOS->开始->suricata 日志处理->firewalld 阻止IP->feishu 飞书通知->结束"}]