import pymysql
import json

# 数据库连接配置
DB_CONFIG = {
    "host": "localhost",
    "user": "root",
    "password": "root",
    "database": "w5_db",
    "charset": "utf8mb4",
    "cursorclass": pymysql.cursors.DictCursor
}

# 删除 decision_info 表中的所有数据
def delete_all_data_from_table(connection):
    with connection.cursor() as cursor:
        delete_query = "DELETE FROM decision_info;"
        cursor.execute(delete_query)
        connection.commit()
        print("decision_info 表中的所有数据已删除。")

# 创建 decision_info 表
def create_table(connection):
    with connection.cursor() as cursor:
        create_table_query = """
        CREATE TABLE IF NOT EXISTS decision_info (
            event_id VARCHAR(255) PRIMARY KEY,
            event_name VARCHAR(255) NOT NULL,
            maintenance_method TEXT,
            check_item TEXT,
            device_software_id VARCHAR(255),
            vendor_name VARCHAR(255),
            harm_name TEXT,
            description TEXT,
            prevention_measures TEXT,
            attack_cause TEXT,
            defective_device_software VARCHAR(255),
            configuration_solution TEXT
        );
        """
        cursor.execute(create_table_query)
        connection.commit()
        print("decision_info 表已创建或已存在。")

# 读取 JSON 文件
def load_security_data(filepath='security.json'):
    with open(filepath, 'r', encoding='utf-8') as file:
        return json.load(file)

# 插入数据到 decision_info 表
def insert_data(connection, data):
    with connection.cursor() as cursor:
        insert_query = """
            INSERT INTO decision_info (
                event_id, event_name, maintenance_method, check_item, device_software_id,
                vendor_name, harm_name, description, prevention_measures, attack_cause,
                defective_device_software, configuration_solution
            ) VALUES (
                %(event_id)s, %(event_name)s, %(maintenance_method)s, %(check_item)s, %(device_software_id)s,
                %(vendor_name)s, %(harm_name)s, %(description)s, %(prevention_measures)s, %(attack_cause)s,
                %(defective_device_software)s, %(configuration_solution)s
            )
            ON DUPLICATE KEY UPDATE
                event_name = VALUES(event_name),
                maintenance_method = VALUES(maintenance_method),
                check_item = VALUES(check_item),
                device_software_id = VALUES(device_software_id),
                vendor_name = VALUES(vendor_name),
                harm_name = VALUES(harm_name),
                description = VALUES(description),
                prevention_measures = VALUES(prevention_measures),
                attack_cause = VALUES(attack_cause),
                defective_device_software = VALUES(defective_device_software),
                configuration_solution = VALUES(configuration_solution)
        """

        for item in data:
            try:
                cursor.execute(insert_query, item)
            except pymysql.MySQLError as e:
                print(f"插入数据时发生错误: {e}")

        connection.commit()
        print("所有数据已插入 decision_info 表。")

def main():
    # 加载 JSON 数据
    security_data = load_security_data()

    # 连接数据库
    try:
        connection = pymysql.connect(**DB_CONFIG)
        print("数据库连接成功！")

        # 删除表中的所有数据
        delete_all_data_from_table(connection)

        # 创建表
        create_table(connection)

        # 插入数据
        insert_data(connection, security_data)

        print("数据导入成功！")

    except pymysql.MySQLError as e:
        print(f"数据库错误: {e}")

    finally:
        if connection:
            connection.close()
            print("数据库连接已关闭。")

if __name__ == "__main__":
    main()
