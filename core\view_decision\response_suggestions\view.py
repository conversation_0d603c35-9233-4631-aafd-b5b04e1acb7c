from flask import request, jsonify
import pymysql
import os
from datetime import datetime
from difflib import SequenceMatcher
from . import *  # 使用 r@route 的形式

DB_CONFIG = {
    "host": ServerHost,
    "user": MysqlUSER,
    "password": MysqlPWD,
    "database": MysqlBase,
    "charset": SQLCharset,
    "cursorclass": pymysql.cursors.DictCursor
}


def similar(a, b):
    return SequenceMatcher(None, a, b).ratio()

def update_decision_counts(success):
    current_date = datetime.now().strftime("%Y-%m-%d")
    file_path = os.path.join(os.path.abspath(os.path.dirname(__file__)), "../screen_countall/decisionCounts.txt")
    try:
        if not os.path.exists(file_path):
            with open(file_path, "w", encoding="utf-8") as f:
                f.write(f"{current_date} {'true' if success else 'false'}\n")
        else:
            with open(file_path, "a", encoding="utf-8") as f:
                f.write(f"{current_date} {'true' if success else 'false'}\n")
    except Exception as e:
        print(f"写入文件失败: {str(e)}")

@r.route("/response_suggestions", methods=['GET'])
def get_response_suggestions():
    event_id = request.args.get("event_id")
    event_name = request.args.get("event_name")
    description = request.args.get("description", None)

    if not event_id or not event_name:
        update_decision_counts(False)
        return jsonify({"code": 400, "msg": "缺少必需的参数: event_id 和 event_name", "data": None})

    connection = None
    try:
        connection = pymysql.connect(**DB_CONFIG)

        with connection.cursor() as cursor:
            cursor.execute("SELECT * FROM decision_info WHERE event_id = %s", (event_id,))
            results = cursor.fetchall()

            if results:
                update_decision_counts(True)
                return jsonify({"code": 200, "msg": "查询成功", "data": results})

            cursor.execute("SELECT * FROM decision_info WHERE event_name = %s", (event_name,))
            results = cursor.fetchall()

            if results:
                update_decision_counts(True)
                return jsonify({"code": 200, "msg": "查询成功", "data": results})

            cursor.execute("SELECT * FROM decision_info")
            all_records = cursor.fetchall()

            similar_results = [record for record in all_records if similar(record["event_name"], event_name) >= 0.6]

            if similar_results:
                update_decision_counts(True)
                return jsonify({"code": 200, "msg": "查询成功", "data": similar_results})

            update_decision_counts(False)
            return jsonify({"code": 404, "msg": "未找到匹配的事件信息", "data": None})

    except pymysql.MySQLError as e:
        update_decision_counts(False)
        return jsonify({"code": 500, "msg": f"数据库错误: {str(e)}", "data": None})

    except Exception as e:
        update_decision_counts(False)
        return jsonify({"code": 500, "msg": f"服务器错误: {str(e)}", "data": None})

    finally:
        if connection:
            try:
                connection.close()
            except Exception:
                pass
