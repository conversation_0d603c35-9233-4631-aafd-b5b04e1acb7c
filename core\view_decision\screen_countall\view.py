from flask import request, jsonify
import os
from datetime import datetime, timedelta
import pymysql
from . import *  # 保留原始结构
from . import r  # 确保 @r.route 可用

# ---- 共用配置与函数 ----

# 数据库连接配置
DB_CONFIG = {
    "host": ServerHost,
    "user": MysqlUSER,
    "password": MysqlPWD,
    "database": MysqlBase,
    "charset": SQLCharset,
    "cursorclass": pymysql.cursors.DictCursor
}


def get_db_connection():
    return pymysql.connect(**DB_CONFIG)

def get_decision_counts_file_path():
    return os.path.join(os.path.abspath(os.path.dirname(__file__)), "decisionCounts.txt")

def get_yesterday_success_count():
    file_path = get_decision_counts_file_path()

    if not os.path.exists(file_path):
        return 0

    current_date = datetime.now()
    yesterday = current_date - timedelta(days=1)
    yesterday_date_str = yesterday.strftime("%Y-%m-%d")

    count = 0
    try:
        with open(file_path, "r", encoding="utf-8") as file:
            lines = file.readlines()
            for line in reversed(lines):
                date, status = line.strip().split(" ")
                if date == yesterday_date_str and status == "true":
                    count += 1
                elif date < yesterday_date_str:
                    break
    except Exception as e:
        print(f"读取文件失败: {str(e)}")
    return count

# ---- 汇总接口 ----

@r.route("/screen/countall", methods=["GET"])
def screen_summary():
    summary = {
        "yesterday_success_count": 0,
        "total_relation_count": 0,
        "decision_db_count": 0
    }

    # 1. 昨日成功数
    try:
        summary["yesterday_success_count"] = get_yesterday_success_count()
    except Exception as e:
        return jsonify({"code": 500, "msg": f"读取 decisionCounts.txt 失败: {str(e)}"}), 500

    # 2. 现实关系数
    try:
        reality_path = os.path.join(os.path.dirname(__file__), "../kgraph_rebuild/realityCount.txt")
        if os.path.exists(reality_path):
            with open(reality_path, "r", encoding="utf-8") as f:
                lines = f.readlines()
                for line in lines:
                    if "数量" in line:
                        count = int(line.split("数量:")[1].strip())
                        summary["total_relation_count"] += count
    except Exception as e:
        return jsonify({"code": 500, "msg": f"读取 realityCount.txt 失败: {str(e)}"}), 500

    # 3. 决策系统记录数（数据库）
    connection = None
    try:
        connection = get_db_connection()
        with connection.cursor() as cursor:
            query = """
                SELECT COUNT(*) AS count
                FROM decision_info
                WHERE configuration_solution IS NOT NULL
                AND configuration_solution != ''
            """
            cursor.execute(query)
            result = cursor.fetchone()
            summary["decision_db_count"] = result["count"]
    except pymysql.MySQLError as e:
        return jsonify({"code": 500, "msg": f"数据库错误: {e}"}), 500
    except Exception as e:
        return jsonify({"code": 500, "msg": f"系统错误: {e}"}), 500
    finally:
        if connection:
            connection.close()

    return jsonify({"code": 200, "msg": "汇总成功", "data": summary}), 200
