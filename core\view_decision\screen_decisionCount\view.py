#!/usr/bin/env python
# encoding:utf-8
import pymysql
from flask import request, jsonify
from . import *

# 数据库连接配置（如已有请忽略）
DB_CONFIG = {
    "host": ServerHost,
    "user": MysqlUSER,
    "password": MysqlPWD,
    "database": MysqlBase,
    "charset": SQLCharset,
    "cursorclass": pymysql.cursors.DictCursor
}

def get_db_connection():
    return pymysql.connect(**DB_CONFIG)

@r.route("/screen/decisionCount", methods=["GET"])
def get_decision_count():
    connection = None
    try:
        connection = get_db_connection()
        with connection.cursor() as cursor:
            query = """
                SELECT COUNT(*) AS count
                FROM decision_info
                WHERE configuration_solution IS NOT NULL
                AND configuration_solution != ''
            """
            cursor.execute(query)
            result = cursor.fetchone()
            return jsonify({"code": 200, "data": result, "msg": "查询成功"})
    except pymysql.MySQLError as e:
        return jsonify({"code": 500, "msg": f"数据库错误: {e}"}), 500
    except Exception as e:
        return jsonify({"code": 500, "msg": f"系统错误: {e}"}), 500
    finally:
        if connection:
            connection.close()
