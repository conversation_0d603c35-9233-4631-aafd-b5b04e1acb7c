from . import *
# 定义 Flask 路由
@r.route("/screen/realityCount", methods=['GET'])
def reality_count():
    file_path = os.path.join(os.path.dirname(__file__), "../kgraph_rebuild/realityCount.txt")

    # 读取 realityCount.txt 文件内容
    try:
        with open(file_path, "r", encoding="utf-8") as f:
            lines = f.readlines()

        # 解析各行数据并计算总关系数
        total_count = 0
        for line in lines:
            if "数量" in line:
                count = int(line.split("数量:")[1].strip())
                total_count += count

        return jsonify({"code": 200, "msg": "读取成功", "total_relation_count": total_count})

    except Exception as e:
        return jsonify({"code": 500, "msg": f"读取文件失败: {e}"})
