from flask import request, jsonify
import os
from datetime import datetime, timedelta  # 需要导入 timedelta
from . import r  # 使用 r@route 的形式

# 更新 decisionCounts.txt 文件路径
def get_decision_counts_file_path():
    # 获取文件的绝对路径
    return os.path.join(os.path.abspath(os.path.dirname(__file__)), "../screen_countall/decisionCounts.txt")

# 读取 decisionCounts.txt 文件并计算前一天成功（true）的行数
def get_yesterday_success_count():
    file_path = get_decision_counts_file_path()

    if not os.path.exists(file_path):
        return 0  # 如果文件不存在，返回 0

    # 获取当前日期和前一天日期
    current_date = datetime.now()
    yesterday = current_date - timedelta(days=1)
    yesterday_date_str = yesterday.strftime("%Y-%m-%d")

    # 读取文件并统计前一天且为true的记录
    count = 0
    try:
        with open(file_path, "r", encoding="utf-8") as file:
            lines = file.readlines()
            
            # 从底向上读取文件内容，查找前一天且为true的记录
            for line in reversed(lines):
                date, status = line.strip().split(" ")
                if date == yesterday_date_str and status == "true":
                    count += 1
                elif date < yesterday_date_str:
                    break  # 如果日期小于前一天，停止读取

    except Exception as e:
        print(f"读取文件失败: {str(e)}")  # 错误处理，实际应用中可以改成日志记录

    return count

@r.route("/screen/yesterdayCount", methods=['GET'])
def get_yesterday_count():
    try:
        count = get_yesterday_success_count()
        return jsonify({"code": 200, "yesterday_success_count": count}), 200
    except Exception as e:
        return jsonify({"code": 0, "message": f"服务器错误: {str(e)}"}), 500
