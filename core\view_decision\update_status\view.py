from flask import request, jsonify
import pymysql
from . import *  # 假设 r 是 Flask 应用的实例或蓝图
from datetime import datetime

@r.route("/event/update_status", methods=['PATCH'])
def update_event_status():
    if not request.is_json:
        return jsonify({"code": 400, "msg": "请求必须是 JSON 格式", "data": None})

    data = request.get_json()
    required_fields = ['event_id', 'new_status']

    for field in required_fields:
        if field not in data:
            return jsonify({"code": 400, "msg": f"缺少必要字段: {field}", "data": None})

    event_id = data['event_id']
    new_status = data['new_status']

    if not isinstance(new_status, int) or new_status < 0:
        return jsonify({"code": 400, "msg": "new_status 必须是非负整数", "data": None})

    connection = None
    try:
        connection = pymysql.connect(
            host='localhost',
            user='root',
            password='root',
            database='w5_db',
            charset='utf8mb4',
            cursorclass=pymysql.cursors.DictCursor
        )

        with connection.cursor() as cursor:
            cursor.execute("SELECT * FROM decision_idsinfo WHERE event_id = %s;", (event_id,))
            event = cursor.fetchone()

            if not event:
                return jsonify({"code": 404, "msg": "未找到指定 event_id 的事件", "data": None})

            cursor.execute("UPDATE decision_idsinfo SET status = %s WHERE event_id = %s;", (new_status, event_id))
            connection.commit()

            return jsonify({"code": 200, "msg": "事件状态更新成功", "data": None})

    except pymysql.MySQLError as e:
        return jsonify({"code": 500, "msg": f"数据库错误: {str(e)}", "data": None})

    finally:
        if connection:
            try:
                connection.close()
            except Exception:
                pass
