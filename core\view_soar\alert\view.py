#!/usr/bin/env python
# encoding:utf-8
from . import *
import requests
import json
from core.model import Al<PERSON>, AlertAnalysis
from datetime import datetime
from flask import request, jsonify
# 引入 Flasgger 的 swag_from 装饰器（也可以直接使用 docstring 编写）
from flasgger import swag_from

# 用于从数据库中调取告警信息返回给前端
# 1. 入侵检测后的报警告警信息数据结构（略）

@r.route("/get/alert/message", methods=['GET'])
def get_alert_message():
    """
    获取告警信息
    ---
    parameters:
      - name: page
        in: query
        type: integer
        required: false
        default: 1
        description: 当前页码
      - name: page_size
        in: query
        type: integer
        required: false
        default: 10
        description: 每页记录数
    responses:
      200:
        description: 返回告警信息数据
        schema:
          type: object
          properties:
            list:
              type: array
              items:
                type: object
                properties:
                  alert_id:
                    type: string
                  timestamp:
                    type: string
                  source_ip:
                    type: string
                  destination_ip:
                    type: string
                  source_port:
                    type: string
                  destination_port:
                    type: string
                  protocol:
                    type: string
                  attack_type:
                    type: string
                  severity:
                    type: string
                  signature:
                    type: string
                  detection_system:
                    type: string
                  correlation_id:
                    type: string
                  status:
                    type: string
                  create_time:
                    type: string
                  update_time:
                    type: string
            pagination:
              type: object
              properties:
                current_page:
                  type: integer
                page_size:
                  type: integer
                total_count:
                  type: integer
                total_pages:
                  type: integer
      500:
        description: 服务器内部错误
    """
    try:
        print("开始获取告警信息")
        # 获取分页参数
        page = int(request.args.get('page', 1))
        page_size = int(request.args.get('page_size', 10))
        print(f"分页参数: page={page}, page_size={page_size}")
        
        # 计算偏移量
        offset = (page - 1) * page_size
        
        # 获取总记录数
        total_count = Alert.count()
        total_pages = (total_count + page_size - 1) // page_size
        
        # 获取告警记录
        alerts = Alert.select().order_by('timestamp', 'desc').offset(offset).limit(page_size).get()
        
        # 格式化告警数据
        alert_list = []
        for alert in alerts:
            try:
                alert_data = {
                    'alert_id': alert.alert_id,
                    'timestamp': alert.timestamp.strftime('%Y-%m-%d %H:%M:%S') if alert.timestamp else None,
                    'source_ip': alert.source_ip,
                    'destination_ip': alert.destination_ip,
                    'source_port': alert.source_port,
                    'destination_port': alert.destination_port,
                    'protocol': alert.protocol,
                    'attack_type': alert.attack_type,
                    'severity': alert.severity,
                    'signature': alert.signature,
                    'detection_system': alert.detection_system,
                    'correlation_id': alert.correlation_id,
                    'status': alert.status,
                    'is_decided': alert.is_decided,
                    'is_processed': alert.is_processed,
                    'create_time': alert.create_time.strftime('%Y-%m-%d %H:%M:%S') if alert.create_time else None,
                    'update_time': alert.update_time.strftime('%Y-%m-%d %H:%M:%S') if alert.update_time else None
                }
                alert_list.append(alert_data)
            except Exception as e:
                continue
        
        # 构建响应数据
        response_data = {
            'list': alert_list,
            'pagination': {
                'current_page': page,
                'page_size': page_size,
                'total_count': total_count,
                'total_pages': total_pages
            }
        }
        
        return Response.re(data=response_data)
        
    except Exception as e:
        return Response.re(ErrMsg(500, f"获取告警信息失败: {str(e)}"))

@r.route("/post/alert/del", methods=['POST'])
def post_alert_del():
    """
    删除告警信息
    ---
    parameters:
      - name: alert_id
        in: jsonData
        type: string
        required: true
        description: 要删除的告警ID
    responses:
      200:
        description: 删除告警信息成功
        schema:
          type: object
          properties:
            code:
              type: integer
            message:
              type: string
            data:
              type: string
      400:
        description: 缺少告警ID或未找到记录
      500:
        description: 删除告警信息失败
    """
    try:
        print("开始删除告警信息")
        
        # 获取要删除的告警ID
        # print(f"请求参数: {request.json}")
        alert_id = request.json.get('alert_id')
        if not alert_id:
            logger.warning("缺少告警ID参数")
            return Response.re(data={
                "code": 400,
                "message": "缺少必需的告警ID参数",
                "data": None
            })
        
        print(f"准备删除告警: alert_id={alert_id}")
        # 检查告警是否存在
        alert = Alert.where('alert_id', alert_id).first()
        if not alert:
            logger.warning(f"未找到告警记录: alert_id={alert_id}")
            return Response.re(data={
                "code": 404,
                "message": "未找到指定的告警信息",
                "data": None
            })
        
        # 删除关联的分析记录
        print(f"删除告警关联的分析记录: alert_id={alert_id}")
        AlertAnalysis.where('alert_id', alert_id).delete()
        
        # 删除告警记录
        print(f"删除告警记录: alert_id={alert_id}")
        alert.delete()
        
        print(f"成功删除告警: alert_id={alert_id}")
        return Response.re(data={
            "code": 200,
            "message": "删除告警信息成功",
            "data": None
        })
    except Exception as e:
        return Response.re(data={
            "code": 500,
            "message": f"删除告警信息失败: {str(e)}",
            "data": None
        })

@r.route("/post/alert/fetch", methods=['POST'])
def post_alert_fetch():
  """
  新增告警记录
  ---
  parameters:
    - name: alert_data
      in: body
      type: string
      required: true
      description: 告警信息数据
  responses:
    200:
      description: 成功新增告警记录
      schema:
        type: object
        properties:
          alert_id:
            type: string
            description: 新增的告警记录ID
    400:
      description: 缺少必需的告警数据参数
    500:
      description: 新增告警记录失败
  """
  try:
    # 该API用于作为webhook供alert系统调用，将告警信息推送到本系统
    # 获取告警信息
    alert_data = request.get_json()
    print(f"接收到告警信息: {alert_data}")
    # 解析告警信息
    alert_id = alert_data.get('alert_id')
    timestamp = alert_data.get('timestamp')
    source_ip = alert_data.get('source_ip')
    destination_ip = alert_data.get('destination_ip')
    source_port = alert_data.get('source_port')
    destination_port = alert_data.get('destination_port')
    protocol = alert_data.get('protocol')
    attack_type = alert_data.get('attack_type')
    severity = alert_data.get('severity')
    signature = alert_data.get('signature')
    detection_system = alert_data.get('detection_system')
    correlation_id = alert_data.get('correlation_id')
    status = alert_data.get('status')
    create_time = datetime.now()
    update_time = datetime.now()
    # 保存告警信息
    alert = Alert.create(
        alert_id=alert_id,
        timestamp=timestamp,
        source_ip=source_ip,
        destination_ip=destination_ip,
        source_port=source_port,
        destination_port=destination_port,
        protocol=protocol,
        attack_type=attack_type,
        severity=severity,
        signature=signature,
        detection_system=detection_system,
        correlation_id=correlation_id,
        status=status,
        create_time=create_time,
        update_time=update_time
    )
    print(f"成功保存告警信息: alert_id={alert_id}")
    return Response.re(data={
        "code": 200,
        "message": "成功保存告警信息",
        "data": None
    })
  except Exception as e:
    return Response.re(data={
        "code": 500,
        "message": f"保存告警信息失败: {str(e)}",
        "data": None
    })
    
