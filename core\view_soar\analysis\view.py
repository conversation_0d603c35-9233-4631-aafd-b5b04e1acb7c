#!/usr/bin/env python
# encoding:utf-8
from . import *
import json
from core.model import Analysis, AlertAnalysis
from datetime import datetime
import uuid

# 示例 API 数据结构说明：
# {
#   "analysis_id": "UUID",
#   "timestamp": "ISO 8601 时间格式",
#   "related_alerts": ["alert_id1", "alert_id2"],
#   "attack_summary": "简要描述攻击行为",
#   "impact_analysis": {
#     "affected_systems": ["IP/主机名"],
#     "potential_risk": "对业务的影响（如数据泄露、服务中断）"
#   },
#   "recommended_actions": [
#     "采取的防御措施",
#     "补丁建议",
#     "阻断攻击的具体步骤"
#   ],
#   "notes": "额外补充说明"
# }

@r.route('/get/analysis/message', methods=['GET'])
def get_analysis_message():
    """
    获取分析信息
    ---
    parameters:
      - name: alert_id
        in: query
        type: string
        required: false
        description: 告警ID，用于查询关联的分析记录，不提供则返回所有分析记录
    responses:
      200:
        description: 成功返回分析信息数据
        schema:
          type: object
          properties:
            list:
              type: array
              items:
                type: object
                properties:
                  analysis_id:
                    type: string
                    description: 分析ID
                  timestamp:
                    type: string
                    description: 分析记录时间，格式为 YYYY-MM-DD HH:MM:SS
                  attack_summary:
                    type: string
                    description: 攻击行为简要描述
                  affected_systems:
                    type: array
                    items:
                      type: string
                    description: 受影响的系统列表
                  potential_risk:
                    type: string
                    description: 可能的风险描述
                  recommended_actions:
                    type: array
                    items:
                      type: string
                    description: 建议的防御措施列表
                  notes:
                    type: string
                    description: 额外补充说明
                  status:
                    type: string
                    description: 分析记录状态
                  create_time:
                    type: string
                    description: 创建时间，格式为 YYYY-MM-DD HH:MM:SS
                  update_time:
                    type: string
                    description: 更新时间，格式为 YYYY-MM-DD HH:MM:SS
      500:
        description: 获取分析信息失败
    """
    try:
        # 获取告警ID参数
        alert_id = request.args.get('alert_id')
        
        if alert_id:
            # 有告警ID，获取关联的分析ID列表
            analysis_ids = AlertAnalysis.select('analysis_id').where(
                'alert_id', alert_id
            ).get()
            
            if not analysis_ids:
                return Response.re(data={
                    'list': []
                })
            
            # 获取分析记录
            analysis_id_list = [record.analysis_id for record in analysis_ids]
            analyses = Analysis.select().where_in(
                'analysis_id', analysis_id_list
            ).order_by('timestamp', 'desc').get()
        else:
            # 没有告警ID，获取所有分析记录
            analyses = Analysis.select().order_by('timestamp', 'desc').get()
        
        # 格式化分析数据
        analysis_list = []
        for analysis in analyses:
            try:
                analysis_data = {
                    'analysis_id': analysis.analysis_id,
                    'timestamp': analysis.timestamp.strftime('%Y-%m-%d %H:%M:%S') if analysis.timestamp else None,
                    'attack_summary': analysis.attack_summary,
                    'affected_systems': analysis.affected_systems,
                    'potential_risk': analysis.potential_risk,
                    'recommended_actions': analysis.recommended_actions,
                    'notes': analysis.notes,
                    'status': analysis.status,
                    'create_time': analysis.create_time.strftime('%Y-%m-%d %H:%M:%S') if analysis.create_time else None,
                    'update_time': analysis.update_time.strftime('%Y-%m-%d %H:%M:%S') if analysis.update_time else None
                }
                analysis_list.append(analysis_data)
            except Exception as e:
                continue
        
        # 构建响应数据
        response_data = {
            'list': analysis_list
        }
        
        return Response.re(data=response_data)
        
    except Exception as e:
        return Response.re(ErrMsg(500, f"获取分析信息失败: {str(e)}"))
      
@r.route('/post/analysis/fetch', methods=['POST'])
def post_analysis_fetch():
  # 首先根据请求数据判断发送的分析信息对应的告警信息是否已经存储在数据库中了
  # 如果已经存储，则直接返回告警信息
  # 如果没有存储，则将告警信息存储到数据库中，并返回告警信息
  try:
      # 获取请求数据
      data = request.get_json()
      if not data:
          return Response.re(ErrMsg(400, "缺少请求数据"))
      
      # 解析请求数据
      analysis_id = data.get('analysis_id')
      timestamp = data.get('timestamp')
      related_alerts = data.get('related_alerts')
      attack_summary = data.get('attack_summary')
      impact_analysis = data.get('impact_analysis')
      recommended_actions = data.get('recommended_actions')
      notes = data.get('notes')
      
      # 查询分析记录
      analysis = Analysis.select().where('analysis_id', analysis_id).first()
      if analysis:
          # 分析记录已存在
          return Response.re(data={
              'analysis_id': analysis.analysis_id,
              'timestamp': analysis.timestamp.strftime('%Y-%m-%d %H:%M:%S') if analysis.timestamp else None,
              'related_alerts': analysis.related_alerts,
              'attack_summary': analysis.attack_summary,
              'impact_analysis': analysis.impact_analysis,
              'recommended_actions': analysis.recommended_actions,
              'notes': analysis.notes
          })
      
      # 创建分析记录
      analysis = Analysis(
          analysis_id=analysis_id,
          timestamp=datetime.strptime(timestamp, '%Y-%m-%d %H:%M:%S') if timestamp else None,
          related_alerts=related_alerts,
          attack_summary=attack_summary,
          impact_analysis=impact_analysis,
          recommended_actions=recommended_actions,
          notes=notes
      )
      analysis.save()
      
      # 返回分析信息
      return Response.re(data={
          'analysis_id': analysis.analysis_id,
          'timestamp': analysis.timestamp.strftime('%Y-%m-%d %H:%M:%S') if analysis.timestamp else None,
          'related_alerts': analysis.related_alerts,
          'attack_summary': analysis.attack_summary,
          'impact_analysis': analysis.impact_analysis,
          'recommended_actions': analysis.recommended_actions,
          'notes': analysis.notes
      })
  except Exception as e:
      return Response.re(ErrMsg(500, f"获取分析信息失败: {str(e)}"))