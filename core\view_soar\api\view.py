#!/usr/bin/env python
# encoding:utf-8
from . import *

@r.route("/webhook", methods=['GET', 'POST'])
def api_webhook():
    """
    Webhook 接口
    ---
    consumes:
      - application/json
    parameters:
      - in: body
        name: body
        required: true
        description: 请求体必须包含 API 密钥、应用 UUID 和数据内容
        schema:
          type: object
          required:
            - key
            - uuid
            - data
          properties:
            key:
              type: string
              description: API 密钥
            uuid:
              type: string
              description: 应用 UUID
            data:
              type: string
              description: 传入的数据
    responses:
      200:
        description: Webhook 处理成功
      400:
        description: 缺少必需参数
      403:
        description: API 密钥错误
      500:
        description: 内部服务器错误
    """
    if request.method == "POST":
        api_key = request.json.get("key", "")
        app_uuid = request.json.get("uuid", "")
        data = request.json.get("data", "")

        if str(api_key).strip() == "" or api_key is None:
            return Response.re(err=ErrWebhookkey)

        if str(app_uuid).strip() == "" or app_uuid is None:
            return Response.re(err=ErrWebhookUUID)

        if str(data).strip() == "" or data is None:
            return Response.re(err=ErrWebhookText)

        key = "api_key"

        if redis.exists(key) == 0:
            setting = Setting.select('value').where("key", "api_key").first()
            value_key = setting.value
            redis.set("api_key", str(value_key))
        else:
            value_key = redis.get(key).decode()

        if str(api_key) != str(value_key):
            return Response.re(err=ErrWebhookKeyNot)

        workflow = Workflow.select('uuid', 'status', "controller_data").where("webhook_app", app_uuid).first()

        if workflow:
            if str(workflow.status) == "1":
                return Response.re(err=ErrWebhookStatus)
            else:
                auto_execute(workflow.uuid, controller_data=workflow.controller_data, data=data, app_uuid=app_uuid)
        else:
            return Response.re(err=ErrWebhookUUIDNot)

        return Response.re()
    # 若为 GET 请求，可按需返回对应信息
    return Response.re(err=ErrMethodNotAllowed)


@r.route("/get/workflow_exec", methods=['GET', 'POST'])
def api_get_workflow_exec():
    """
    获取工作流执行结果接口
    ---
    consumes:
      - application/json
    parameters:
      - in: body
        name: body
        required: true
        description: 请求体必须包含 API 密钥和工作流 UUID
        schema:
          type: object
          required:
            - key
            - uuid
          properties:
            key:
              type: string
              description: API 密钥
            uuid:
              type: string
              description: 工作流的 UUID
    responses:
      200:
        description: 返回工作流执行结果
      400:
        description: 缺少必需参数或 API 密钥错误
      500:
        description: 内部服务器错误
    """
    if request.method == "POST":
        api_key = request.json.get("key", "")
        uuid = request.json.get("uuid", "")

        if str(api_key).strip() == "" or api_key is None:
            return Response.re(err=ErrWebhookkey)

        if str(uuid).strip() == "" or uuid is None:
            return Response.re(err=ErrWebhookUUID)

        key = "api_key"
        if redis.exists(key) == 0:
            setting = Setting.select('value').where("key", "api_key").first()
            value_key = setting.value
            redis.set("api_key", str(value_key))
        else:
            value_key = redis.get(key).decode()

        if str(api_key) != str(value_key):
            return Response.re(err=ErrWebhookKeyNot)

        from core.view_soar.workflow.view import get_workflow_exec
        result = get_workflow_exec(uuid)
        return Response.re(data=result)
    return Response.re(err=ErrMethodNotAllowed)


@r.route("/get/workflow_success_fail", methods=['GET', 'POST'])
def api_get_workflow_sucess_fail():
    """
    获取工作流成功与失败统计接口
    ---
    consumes:
      - application/json
    parameters:
      - in: body
        name: body
        required: true
        description: 请求体必须包含 API 密钥和工作流 UUID
        schema:
          type: object
          required:
            - key
            - uuid
          properties:
            key:
              type: string
              description: API 密钥
            uuid:
              type: string
              description: 工作流的 UUID
    responses:
      200:
        description: 返回成功和失败的统计数据
        schema:
          type: object
          properties:
            success_sum:
              type: integer
            fail_sum:
              type: integer
      400:
        description: 缺少必需参数或 API 密钥错误
      500:
        description: 内部服务器错误
    """
    if request.method == "POST":
        api_key = request.json.get("key", "")
        uuid = request.json.get("uuid", "")

        if str(api_key).strip() == "" or api_key is None:
            return Response.re(err=ErrWebhookkey)

        if str(uuid).strip() == "" or uuid is None:
            return Response.re(err=ErrWebhookUUID)

        key = "api_key"
        if redis.exists(key) == 0:
            setting = Setting.select('value').where("key", "api_key").first()
            value_key = setting.value
            redis.set("api_key", str(value_key))
        else:
            value_key = redis.get(key).decode()

        if str(api_key) != str(value_key):
            return Response.re(err=ErrWebhookKeyNot)

        from core.view_soar.workflow.view import get_workflow_success_fail
        sum_value, success_sum, fail_sum = get_workflow_success_fail(uuid)
        data = {
            "success_sum": success_sum,
            "fail_sum": fail_sum
        }
        return Response.re(data=data)
    return Response.re(err=ErrMethodNotAllowed)


@r.route("/get/workflow_logs", methods=['GET', 'POST'])
def api_get_workflow_logs():
    """
    获取工作流日志接口
    ---
    consumes:
      - application/json
    parameters:
      - in: body
        name: body
        required: true
        description: 请求体必须包含 API 密钥和工作流 UUID
        schema:
          type: object
          required:
            - key
            - uuid
          properties:
            key:
              type: string
              description: API 密钥
            uuid:
              type: string
              description: 工作流的 UUID
    responses:
      200:
        description: 返回工作流日志数据
        schema:
          type: object
          properties:
            logs:
              type: array
              items:
                type: object
      400:
        description: 缺少必需参数或 API 密钥错误
      500:
        description: 内部服务器错误
    """
    if request.method == "POST":
        api_key = request.json.get("key", "")
        uuid = request.json.get("uuid", "")

        if str(api_key).strip() == "" or api_key is None:
            return Response.re(err=ErrWebhookkey)

        if str(uuid).strip() == "" or uuid is None:
            return Response.re(err=ErrWebhookUUID)

        key = "api_key"
        if redis.exists(key) == 0:
            setting = Setting.select('value').where("key", "api_key").first()
            value_key = setting.value
            redis.set("api_key", str(value_key))
        else:
            value_key = redis.get(key).decode()

        if str(api_key) != str(value_key):
            return Response.re(err=ErrWebhookKeyNot)

        from core.view_soar.workflow.view import get_workflow_logs
        logs_list = get_workflow_logs(uuid)
        return Response.re(data=logs_list.serialize())
    return Response.re(err=ErrMethodNotAllowed)


@r.route("/get/executing", methods=['GET', 'POST'])
def api_get_executing():
    """
    获取执行中的任务数量接口
    ---
    consumes:
      - application/json
    parameters:
      - in: body
        name: body
        required: true
        description: 请求体必须包含 API 密钥，可选传入工作流 UUID
        schema:
          type: object
          required:
            - key
          properties:
            key:
              type: string
              description: API 密钥
            uuid:
              type: string
              description: 工作流的 UUID（可选）
    responses:
      200:
        description: 返回执行中的任务数量
        schema:
          type: object
          properties:
            sum:
              type: integer
      400:
        description: 缺少必需参数或 API 密钥错误
      500:
        description: 内部服务器错误
    """
    if request.method == "POST":
        api_key = request.json.get("key", "")
        uuid = request.json.get("uuid", "")

        if str(api_key).strip() == "" or api_key is None:
            return Response.re(err=ErrWebhookkey)

        key = "api_key"
        if redis.exists(key) == 0:
            setting = Setting.select('value').where("key", "api_key").first()
            value_key = setting.value
            redis.set("api_key", str(value_key))
        else:
            value_key = redis.get(key).decode()

        if str(api_key) != str(value_key):
            return Response.re(err=ErrWebhookKeyNot)

        if str(uuid).strip() == "" or uuid is None:
            if redis.exists("exec_sum") == 1:
                exec_sum = int(redis.get("exec_sum"))
            else:
                exec_sum = 0
        else:
            key_uuid = uuid + "&&exec_sum"
            if redis.exists(key_uuid) == 1:
                exec_sum = int(redis.get(key_uuid))
            else:
                exec_sum = 0

        return Response.re(data={"sum": exec_sum})
    return Response.re(err=ErrMethodNotAllowed)
