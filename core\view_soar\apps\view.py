#!/usr/bin/env python
# encoding:utf-8
from . import *
import json
import shutil
import os
from werkzeug.utils import secure_filename

@r.route("/get/app/list", methods=['GET', 'POST'])
def get_app_list():
    """
    获取应用列表
    ---
    consumes:
      - application/json
    produces:
      - application/json
    parameters:
      - in: query
        name: dummy
        type: string
        required: false
        description: 示例参数（实际接口无必传参数）
    responses:
      200:
        description: 成功返回应用列表
        schema:
          type: object
          properties:
            data:
              type: object
              additionalProperties:
                type: object
                properties:
                  app_dir:
                    type: string
                  icon:
                    type: string
                  # 其他字段根据 app_d 中定义
      500:
        description: 内部服务器错误
    """
    if request.method == "GET":
        app_data = {}

        dir_list = File.find_apps(path=current_app.config["apps_path"])

        for d in dir_list:
            app_json = File.find_app_json(path=current_app.config["apps_path"], app_dir=d)
            app_d = json.loads(app_json)
            app_d["app_dir"] = d
            app_d["icon"] = d + "/icon.png"
            app_data[d] = app_d

        return Response.re(data=app_data)


@r.route("/post/app/del", methods=['GET', 'POST'])
def post_app_del():
    """
    删除指定应用
    ---
    consumes:
      - application/json
    parameters:
      - in: body
        name: body
        description: 请求体中必须包含 app_dir 参数，标识要删除的应用目录
        required: true
        schema:
          type: object
          required:
            - app_dir
          properties:
            app_dir:
              type: string
              description: 应用目录标识
    responses:
      200:
        description: 删除成功
      500:
        description: 删除失败
    """
    if request.method == "POST":
        app_dir = request.json.get("app_dir", "")
        del_path = current_app.config["apps_path"] + "/" + str(app_dir).replace(".", "").replace("/", "")
        try:
            shutil.rmtree(del_path)
        except Exception as e:
            return Response.re(err=ErrAppDel)
        else:
            return Response.re()


@r.route("/post/app/import", methods=['GET', 'POST'])
def post_app_import():
    """
    导入应用
    ---
    consumes:
      - multipart/form-data
    parameters:
      - in: formData
        name: file
        type: file
        required: true
        description: 待导入的应用压缩包（zip 文件）
    responses:
      200:
        description: 导入成功
      400:
        description: 应用已存在或文件格式错误
      500:
        description: 导入失败
    """
    if request.method == "POST":
        f = request.files['file']
        filename = secure_filename(f.filename)
        if filename.split('.')[-1] == "zip":
            tmp_dir = current_app.config["tmp_path"]
            app_dir = current_app.config["apps_path"]
            if not os.path.exists(tmp_dir):
                os.makedirs(tmp_dir)
            file_path = tmp_dir + "/" + filename
            f.save(file_path)
            save_path = app_dir + "/" + filename.replace(".zip", "")
            if os.path.exists(save_path):
                return Response.re(err=ErrUploadAppExist)
            status, text = Zip.save(zip_path=file_path, save_path=save_path)
            if status:
                shutil.rmtree(tmp_dir)
                return Response.re()
            else:
                shutil.rmtree(tmp_dir)
                return Response.re(err=ErrMsg(errcode=9030, errmsg=text))
        else:
            return Response.re(err=ErrUploadZip)


@r.route("/post/app/upload", methods=['GET', 'POST'])
def post_app_upload():
    """
    上传应用信息
    ---
    consumes:
      - application/json
    parameters:
      - in: body
        name: body
        description: 应用上传所需的各项信息
        required: true
        schema:
          type: object
          required:
            - wid
            - name
            - type
            - author
            - email
            - description
            - version
            - github
            - app_dir
          properties:
            wid:
              type: string
              description: 应用 ID
            name:
              type: string
              description: 应用名称
            type:
              type: string
              description: 应用类型
            author:
              type: string
              description: 作者名称
            email:
              type: string
              description: 作者邮箱
            description:
              type: string
              description: 应用描述
            version:
              type: string
              description: 应用版本
            github:
              type: string
              description: GitHub 地址
            app_dir:
              type: string
              description: 应用目录标识
    responses:
      200:
        description: 上传成功
      500:
        description: 上传失败
    """
    if request.method == "POST":
        wid = request.json.get("wid", "")
        name = request.json.get("name", "")
        type = request.json.get("type", "")
        author = request.json.get("author", "")
        email = request.json.get("email", "")
        description = request.json.get("description", "")
        version = request.json.get("version", "")
        github = request.json.get("github", "")
        app_dir = request.json.get("app_dir", "")

        result = Cloud(apps_path=current_app.config["apps_path"]).upload(
            wid,
            name,
            type,
            author,
            email,
            description,
            version,
            github,
            app_dir
        )

        if result == "success":
            return Response.re()
        else:
            return Response.re(err=ErrMsg(errcode=9028, errmsg=result))


@r.route("/post/app/download", methods=['GET', 'POST'])
def post_app_download():
    """
    下载应用
    ---
    consumes:
      - application/json
    parameters:
      - in: body
        name: body
        description: 请求体中需包含 zip_url、app_dir 和 wid
        required: true
        schema:
          type: object
          required:
            - zip_url
            - app_dir
            - wid
          properties:
            zip_url:
              type: string
              description: 压缩包下载地址
            app_dir:
              type: string
              description: 应用目录标识
            wid:
              type: string
              description: 应用 ID
    responses:
      200:
        description: 下载成功
      500:
        description: 下载失败
    """
    if request.method == "POST":
        zip_url = request.json.get("zip_url", "")
        app_dir = request.json.get("app_dir", "")
        wid = request.json.get("wid", "")

        bools, text = Cloud(apps_path=current_app.config["apps_path"]).download(
            zip_url=zip_url,
            app_dir=app_dir,
            wid=wid
        )

        if bools:
            return Response.re()
        else:
            return Response.re(err=ErrMsg(errcode=9029, errmsg=text))


@r.route("/get/app/cloud_list", methods=['GET', 'POST'])
def post_app_cloud_list():
    """
    获取云端应用列表
    ---
    consumes:
      - application/json
    parameters:
      - in: body
        name: body
        description: 此接口不需要额外参数
        required: false
        schema:
          type: object
    responses:
      200:
        description: 返回云端应用列表数据
        schema:
          type: object
          properties:
            data:
              type: array
              items:
                type: object
    """
    if request.method == "POST":
        result = Cloud().list()
        return Response.re(data=result)


@r.route("/get/app/cloud_info", methods=['GET', 'POST'])
def post_app_cloud_info():
    """
    获取云端应用详细信息
    ---
    consumes:
      - application/json
    parameters:
      - in: body
        name: body
        description: 请求体中需包含 wid，用于查询应用详细信息
        required: true
        schema:
          type: object
          required:
            - wid
          properties:
            wid:
              type: string
              description: 应用 ID
    responses:
      200:
        description: 返回云端应用详细信息
        schema:
          type: object
          properties:
            data:
              type: object
    """
    if request.method == "POST":
        wid = request.json.get("wid", "")
        print(wid)
        result = Cloud().wid_info(wid=wid)
        return Response.re(data=result)
