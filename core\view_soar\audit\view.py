#!/usr/bin/env python
# encoding:utf-8
from . import *
import json

@r.route("/get/audit/list", methods=['GET', 'POST'])
def get_audit_list():
    """
    获取审核列表
    ---
    consumes:
      - application/json
    produces:
      - application/json
    parameters:
      - in: body
        name: body
        description: 可选查询参数，用于过滤审核记录
        required: false
        schema:
          type: object
          properties:
            keywords:
              type: string
              description: 搜索关键词（将会匹配工作流名称或用户昵称）
            type:
              type: string
              default: "0"
              description: 审核状态类型，"all" 表示全部
            page:
              type: integer
              default: 1
              description: 当前页码
            page_count:
              type: integer
              default: 10
              description: 每页记录数
    responses:
      200:
        description: 返回审核记录列表及分页信息
        schema:
          type: object
          properties:
            data:
              type: object
              properties:
                list:
                  type: array
                  items:
                    type: object
                    properties:
                      id:
                        type: string
                      workflow_uuid:
                        type: string
                      only_id:
                        type: string
                      user_id:
                        type: string
                      audit_app:
                        type: string
                      start_app:
                        type: string
                      status:
                        type: string
                      update_time:
                        type: string
                        description: 更新时间，格式为 YYYY-MM-DD HH:MM:SS
                      create_time:
                        type: string
                        description: 创建时间，格式为 YYYY-MM-DD HH:MM:SS
                      name:
                        type: string
                        description: 工作流名称
                      nick_name:
                        type: string
                        description: 用户昵称
                      avatar:
                        type: string
                        description: 用户头像
                pagination:
                  type: object
                  properties:
                    current_page:
                      type: integer
                    page_size:
                      type: integer
                    total_count:
                      type: integer
                    total_pages:
                      type: integer
      400:
        description: 请求参数错误
      500:
        description: 内部服务器错误
    """
    if request.method == "POST":
        keywords = request.json.get("keywords", "")
        type = request.json.get("type", "0")
        page = request.json.get("page", 1)
        page_count = request.json.get("page_count", 10)

        audit_list = Audit.join(
            Workflow.__table__,
            Audit.__table__ + '.workflow_uuid',
            '=',
            Workflow.__table__ + '.uuid'
        ).join(
            Users.__table__,
            Audit.__table__ + '.user_id',
            '=',
            Users.__table__ + '.id'
        ).select(
            Audit.__table__ + '.id',
            Audit.__table__ + '.workflow_uuid',
            Audit.__table__ + '.only_id',
            Audit.__table__ + ".user_id",
            Audit.__table__ + '.audit_app',
            Audit.__table__ + '.start_app',
            Audit.__table__ + '.status',
            Audit.__table__ + '.update_time',
            Audit.__table__ + '.create_time',
            Workflow.__table__ + '.name',
            Users.__table__ + '.nick_name',
            Users.__table__ + '.avatar'
        )

        if str(type) != "all":
            audit_list = audit_list.where(Audit.__table__ + ".status", type)

        if str(keywords) == "":
            audit_list = audit_list.order_by('id', 'desc').paginate(page_count, page)
        else:
            audit_list = audit_list.where(
                'name',
                'like',
                '%{keywords}%'.format(keywords=keywords)
            ).or_where(
                'nick_name',
                'like',
                '%{keywords}%'.format(keywords=keywords)
            ).order_by('id', 'desc').paginate(page_count, page)

        return Response.re(data=Page(model=audit_list).to())


@r.route("/post/audit/update", methods=['GET', 'POST'])
def post_audit_update():
    """
    更新审核状态
    ---
    consumes:
      - application/json
    parameters:
      - in: body
        name: body
        description: 请求体中需要传入审核记录更新所需参数
        required: true
        schema:
          type: object
          required:
            - id
            - status
            - only_id
            - workflow_uuid
            - audit_app
            - start_app
            - user
          properties:
            id:
              type: string
              description: 审核记录ID
            status:
              type: string
              description: 更新后的审核状态
            only_id:
              type: string
              description: 唯一标识ID
            workflow_uuid:
              type: string
              description: 工作流UUID
            audit_app:
              type: string
              description: 审核应用标识
            start_app:
              type: string
              description: 启动应用标识
            user:
              type: string
              description: 当前操作用户
    responses:
      200:
        description: 审核状态更新成功
      400:
        description: 请求参数错误
      500:
        description: 内部服务器错误
    """
    if request.method == "POST":
        id = request.json.get("id", "")
        status = request.json.get("status", "")
        only_id = request.json.get("only_id", "")
        workflow_uuid = request.json.get("workflow_uuid", "")
        audit_app = request.json.get("audit_app", "")
        start_app = request.json.get("start_app", "")
        user = request.json.get("user", "")

        auto_execute(
            workflow_uuid,
            audit_status=status,
            audit_app=audit_app,
            start_app=start_app,
            only_id=only_id,
            user=user
        )

        Audit.where('id', id).update({
            "status": status,
            "update_time": Time.get_date_time()
        })

        return Response.re()

# 获取处于待审状态剧本数量的API
@r.route("/post/audit/count", methods=['GET', 'POST'])
def get_audit_count():
    """
    获取处于待审状态剧本数量
    ---
    consumes:
      - application/json
    responses:
      200:
        description: 返回处于待审状态剧本数量
        schema:
          type: object
          properties:
            count:
              type: integer
    """
    if request.method == "POST":
        count = Audit.where('status', 'pending').count()
        return Response.re(data=count)
