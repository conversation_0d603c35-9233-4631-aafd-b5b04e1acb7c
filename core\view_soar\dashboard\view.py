#!/usr/bin/env python
# encoding:utf-8
from . import *
import json
import shutil
import os
from werkzeug.utils import secure_filename

@r.route("/get/dashboard/logs", methods=['GET', 'POST'])
def get_dashboard_logs():
    """
    获取仪表盘日志信息
    ---
    consumes:
      - application/json
    produces:
      - application/json
    parameters:
      - in: body
        name: body
        description: 此接口无需额外参数，直接返回最新 100 条日志记录
        required: false
        schema:
          type: object
    responses:
      200:
        description: 返回日志记录数据
        schema:
          type: object
          properties:
            data:
              type: array
              items:
                type: object
                properties:
                  id:
                    type: string
                  only_id:
                    type: string
                  uuid:
                    type: string
                  app_name:
                    type: string
                  result:
                    type: string
                  create_time:
                    type: string
                    description: 创建时间（格式：YYYY-MM-DD HH:MM:SS）
                  status:
                    type: string
                  args:
                    type: string
                  name:
                    type: string
                    description: 工作流名称
      500:
        description: 内部服务器错误
    """
    if request.method == "POST":
        logs_list = Logs.join(
            Workflow.__table__,
            Logs.__table__ + '.uuid',
            '=',
            Workflow.__table__ + '.uuid'
        ).select(
            Logs.__table__ + '.id',
            Logs.__table__ + '.only_id',
            Logs.__table__ + '.uuid',
            Logs.__table__ + ".app_name",
            Logs.__table__ + '.result',
            Logs.__table__ + '.create_time',
            Logs.__table__ + '.status',
            Logs.__table__ + '.args',
            Workflow.__table__ + '.name'
        ).order_by(
            Workflow.__table__ + '.id',
            'desc'
        ).limit(100).get()

        return Response.re(data=logs_list.serialize())


@r.route("/get/dashboard/sums", methods=['GET', 'POST'])
def get_dashboard_sums():
    """
    获取仪表盘统计数据
    ---
    consumes:
      - application/json
    parameters:
      - in: body
        name: body
        description: 此接口无需传递参数
        required: false
        schema:
          type: object
    responses:
      200:
        description: 返回统计数据，包括用户数、工作流数、日志总数、错误日志数和执行任务数
        schema:
          type: object
          properties:
            data:
              type: object
              properties:
                user_count:
                  type: integer
                workflow_count:
                  type: integer
                logs_count:
                  type: integer
                logs_err_count:
                  type: integer
                exec_sum:
                  type: integer
      500:
        description: 内部服务器错误
    """
    if request.method == "POST":
        user_count = Users.count()
        workflow_count = Workflow.count()
        logs_count = Logs.count()
        logs_err_count = Logs.where("status", "!=", 0).count()

        if redis.exists("exec_sum") == 1:
            exec_sum = int(redis.get("exec_sum"))
        else:
            exec_sum = 0

        data = {
            "user_count": user_count,
            "workflow_count": workflow_count,
            "logs_count": logs_count,
            "logs_err_count": logs_err_count,
            "exec_sum": exec_sum
        }

        return Response.re(data=data)


@r.route("/get/dashboard/workflow", methods=['GET', 'POST'])
def get_dashboard_workflow():
    """
    获取工作流分类统计数据
    ---
    consumes:
      - application/json
    parameters:
      - in: body
        name: body
        description: 此接口无需传递参数
        required: false
        schema:
          type: object
    responses:
      200:
        description: 返回每个工作流分类名称及其数量
        schema:
          type: object
          properties:
            data:
              type: array
              items:
                type: object
                properties:
                  type:
                    type: string
                    description: 工作流分类名称
                  value:
                    type: integer
                    description: 该分类的数量
      500:
        description: 内部服务器错误
    """
    if request.method == "POST":
        sql = '''
        SELECT
            {type}.name AS type,
            sum(1) AS value
        FROM
            {workflow}
        JOIN {type} ON {workflow}.type_id = {type}.id
        GROUP BY
            {type}.name;
        '''.format(
            type=Types.__table__,
            workflow=Workflow.__table__
        )

        workflow_data = db.select(sql)
        return Response.re(data=workflow_data)


@r.route("/get/dashboard/exec", methods=['GET', 'POST'])
def get_dashboard_exec():
    """
    获取仪表盘执行数据统计
    ---
    consumes:
      - application/json
    parameters:
      - in: body
        name: body
        description: 请求体中可传入参数 type 指定统计类型：
          1 - 当天每小时统计；
          2 - 昨天每小时统计；
          3 - 本周每日统计；
          4 - 本月每日统计；
          5 - 上个月每日统计；
          6 - 本年每月统计
        required: false
        schema:
          type: object
          properties:
            type:
              type: integer
              default: 1
              description: 统计类型
    responses:
      200:
        description: 返回统计数据，格式为时间点及对应的数值
        schema:
          type: object
          properties:
            data:
              type: array
              items:
                type: object
                properties:
                  time:
                    type: string
                  value:
                    type: integer
      500:
        description: 内部服务器错误
    """
    if request.method == "POST":
        type = request.json.get("type", 1)
        result = []

        if type == 1:
            sql = '''
            SELECT
                DATE_FORMAT(create_time, '%%H') AS time,
                count(id) AS value 
            FROM
                {logs} 
            WHERE
                to_days(create_time) = to_days(now())
            GROUP BY
                time;
            '''.format(logs=Logs.__table__)
            exec_data = db.select(sql)
            time_data = {}
            for t in Time.get_hour():
                time_data[t] = 0
            for t in exec_data:
                time_data[t.time] = t.value
            for t in time_data:
                result.append({"time": t, "value": time_data[t]})
        elif type == 2:
            sql = '''
            SELECT
                DATE_FORMAT(create_time, '%%H') AS time,
                count(id) AS value 
            FROM
                {logs} 
            WHERE
                TO_DAYS(NOW()) - TO_DAYS(create_time) <= 1  
            GROUP BY
                time;
            '''.format(logs=Logs.__table__)
            exec_data = db.select(sql)
            time_data = {}
            for t in Time.get_hour():
                time_data[t] = 0
            for t in exec_data:
                time_data[t.time] = t.value
            for t in time_data:
                result.append({"time": t, "value": time_data[t]})
        elif type == 3:
            sql = '''
            SELECT
                DATE_FORMAT(create_time, '%%m-%%d') AS time,
                count(id) AS value
            FROM
                {logs}  
            WHERE
                YEARWEEK(date_format(create_time, '%%Y-%%m-%%d')) = YEARWEEK(now()) 
            GROUP BY
                time;
            '''.format(logs=Logs.__table__)
            exec_data = db.select(sql)
            time_data = {}
            for t in Time.get_week():
                time_data[t] = 0
            for t in exec_data:
                time_data[t.time] = t.value
            for t in time_data:
                result.append({"time": t, "value": time_data[t]})
        elif type == 4:
            sql = '''
            SELECT
                DATE_FORMAT(create_time, '%%m-%%d') AS time,
                count(id) AS value
            FROM
                {logs}
            WHERE
                date_format(create_time, '%%Y-%%m') = date_format(now(), '%%Y-%%m') 
            GROUP BY
                time
            ORDER BY time;
            '''.format(logs=Logs.__table__)
            exec_data = db.select(sql)
            time_data = {}
            for t in Time.get_month():
                time_data[t] = 0
            for t in exec_data:
                time_data[t.time] = t.value
            for t in time_data:
                result.append({"time": t, "value": time_data[t]})
        elif type == 5:
            sql = '''
            SELECT
                DATE_FORMAT(create_time, '%%m-%%d') AS time,
                count(id) AS value
            FROM
                {logs}
            WHERE
               date_format(create_time, '%%Y-%%m') = date_format(DATE_SUB(curdate(), INTERVAL 1 MONTH),'%%Y-%%m') 
            GROUP BY
                time
            ORDER BY time;
            '''.format(logs=Logs.__table__)
            exec_data = db.select(sql)
            time_data = {}
            for t in Time.get_upper_month():
                time_data[t] = 0
            for t in exec_data:
                time_data[t.time] = t.value
            for t in time_data:
                result.append({"time": t, "value": time_data[t]})
        elif type == 6:
            sql = '''
            SELECT
                DATE_FORMAT(create_time, '%%m') AS time,
                count(id) AS value
            FROM
                {logs} 
            WHERE
                YEAR(create_time)=YEAR(NOW())
            GROUP BY
                time
            ORDER BY time;
            '''.format(logs=Logs.__table__)
            exec_data = db.select(sql)
            time_data = {}
            for t in Time.get_year():
                time_data[t] = 0
            for t in exec_data:
                time_data[t.time] = t.value
            for t in time_data:
                result.append({"time": t, "value": time_data[t]})
        return Response.re(data=result)


@r.route("/get/dashboard/login_history", methods=['GET', 'POST'])
def get_dashboard_login_history():
    """
    获取用户登录历史记录
    ---
    consumes:
      - application/json
    parameters:
      - in: body
        name: body
        description: 此接口无需传递参数，返回最近 100 条登录记录
        required: false
        schema:
          type: object
    responses:
      200:
        description: 返回登录历史记录数据
        schema:
          type: object
          properties:
            data:
              type: array
              items:
                type: object
                properties:
                  id:
                    type: string
                  account:
                    type: string
                  nick_name:
                    type: string
                  avatar:
                    type: string
                  login_time:
                    type: string
                    description: 登录时间（格式：YYYY-MM-DD HH:MM:SS）
      500:
        description: 内部服务器错误
    """
    if request.method == "POST":
        login_history_list = LoginHistory.join(
            Users.__table__,
            LoginHistory.__table__ + '.user_id',
            '=',
            Users.__table__ + '.id'
        ).select(
            Users.__table__ + '.id',
            Users.__table__ + '.account',
            Users.__table__ + '.nick_name',
            Users.__table__ + '.avatar',
            LoginHistory.__table__ + ".login_time",
        ).order_by(
            LoginHistory.__table__ + '.id',
            'desc'
        ).limit(100).get()

        return Response.re(data=login_history_list.serialize())
@r.route("/get/screen/info", methods=['GET'])
def get_screen_info():
    """
    获取大屏信息
    ---
    summary: 获取大屏信息的相关统计数据
    description: 返回系统中各类剧本和日志的数量，包括执行中的剧本数、待审剧本数、日志数等
    produces:
      - application/json
    responses:
      200:
        description: 成功获取大屏信息
        content:
          application/json:
            schema:
              type: object
              properties:
                workflow_count:
                  type: integer
                  description: 剧本总数量
                audit_pending_workflow_count:
                  type: integer
                  description: 待审剧本数量
                logs_count:
                  type: integer
                  description: 执行日志总数量
                type_count:
                  type: integer
                  description: 剧本类型数量
                exec_sum:
                  type: integer
                  description: 当前执行中的剧本数量
                logs_err_count:
                  type: integer
                  description: 执行日志中异常数量
        examples:
          application/json:
            {
              "workflow_count": 100,
              "audit_pending_workflow_count": 5,
              "logs_count": 500,
              "type_count": 3,
              "exec_sum": 2,
              "logs_err_count": 10
            }
      500:
        description: 获取大屏信息失败，服务器内部错误
    """
    # 大屏信息为
    # 1. 剧本数量 workflow_count
    # 2. 待审剧本数量 audit_pending_workflow_count
    # 3. 执行日志数量 logs_count
    # 4. 剧本类型数量 type_count
    # 5. 正在执行数量 exec_sum
    # 6. 执行异常数量 logs_err_count
    workflow_count = Workflow.count()
    audit_pending_workflow_count = Audit.where('status', 'pending').count()
    logs_count = Logs.count()
    type_count = Workflow.query().distinct().select('type_id').count()
    logs_err_count = Logs.where("status", "!=", 0).count()
    
    if redis.exists("exec_sum") == 1:
        exec_sum = int(redis.get("exec_sum"))
    else:
        exec_sum = 0

    data = {
        "workflow_count": workflow_count,
        "audit_pending_workflow_count": audit_pending_workflow_count,
        "logs_count": logs_count,
        "type_count": type_count,
        "exec_sum": exec_sum,
        "logs_err_count": logs_err_count
    }
  
    return Response.re(data=data)

@r.route("/get/vul/info", methods=['GET'])
def get_vul_info():
  data =  {
      "sum":46,
      "high-risk":5,
      "details":[
        {
          "ip": "************",
          "sum": 1,
          "status": "高危",
          "department": "大数据部门"
        },
        {
          "ip": "************",
          "sum": 2,
          "status": "中危",
          "department": "大数据部门"
        },
        {
          "ip": "************",
          "sum": 5,
          "status": "低危",
          "department": "大数据部门"
        },
        {
          "ip": "*************",
          "sum": 1,
          "status": "中危",
          "department": "信息安全部门"
        },
        {
          "ip": "*************",
          "sum": 3,
          "status": "低危",
          "department": "信息安全部门"
        },
        {
          "ip": "************",
          "sum": 1,
          "status": "低危",
          "department": "智能化部门"
        },
        {
          "ip": "*************",
          "sum": 2,
          "status": "高危",
          "department": "智能化部门"
        },
        {
          "ip": "*************",
          "sum": 7,
          "status": "低危",
          "department": "智能化部门"
        },
        {
          "ip": "*************",
          "sum": 1,
          "status": "中危",
          "department": "大数据部门"
        },
        {
          "ip": "*************",
          "sum": 3,
          "status": "低危",
          "department": "大数据部门"
        }
      ]
  }
  return Response.re(data=data)

@r.route("/get/asset/info",methods=['GET'])
def get_asset_info():
  server = 100
  terminal = 101
  netDevice = 103
  assetSum = server + terminal + netDevice
  data = {
    "server":server,
    "serverPe": f"{server/assetSum:.2%}",
    "terminal": terminal,
    "terminalPe": f"{terminal/assetSum:.2%}",
    "netDevice": netDevice,
    "netDevicePe":f"{netDevice/assetSum:.2%}",
    "assetSum":assetSum
  }
  return Response.re(data=data)

