from . import *
import json

@r.route("/get/logs/list", methods=['GET', 'POST'])
def get_logs_list():
    """
    获取日志列表接口
    ---
    consumes:
      - application/json
    produces:
      - application/json
    parameters:
      - in: body
        name: body
        description: 请求体参数，用于分页及过滤日志记录
        required: false
        schema:
          type: object
          properties:
            keywords:
              type: string
              description: 关键字，用于匹配日志的 result 或 app_name 字段
            type:
              type: string
              default: "0"
              description: 日志过滤类型，"0" 表示不过滤，其他值将用作 uuid 条件过滤
            page:
              type: integer
              default: 1
              description: 当前页码
            page_count:
              type: integer
              default: 10
              description: 每页记录数
    responses:
      200:
        description: 返回日志记录列表和分页信息
        schema:
          type: object
          properties:
            data:
              type: object
              properties:
                list:
                  type: array
                  items:
                    type: object
                    properties:
                      id:
                        type: string
                      only_id:
                        type: string
                      uuid:
                        type: string
                      app_name:
                        type: string
                      result:
                        type: string
                      create_time:
                        type: string
                        description: 创建时间（格式：YYYY-MM-DD HH:MM:SS）
                      status:
                        type: string
                      args:
                        type: string
                      name:
                        type: string
                        description: 工作流名称
                pagination:
                  type: object
                  properties:
                    current_page:
                      type: integer
                    page_size:
                      type: integer
                    total_count:
                      type: integer
                    total_pages:
                      type: integer
      500:
        description: 内部服务器错误
    """
    if request.method == "POST":
        keywords = request.json.get("keywords", "")
        type = request.json.get("type", "0")
        page = request.json.get("page", 1)
        page_count = request.json.get("page_count", 10)

        logs_list = Logs.join(
            Workflow.__table__,
            Logs.__table__ + '.uuid',
            '=',
            Workflow.__table__ + '.uuid'
        ).select(
            Logs.__table__ + '.id',
            Logs.__table__ + '.only_id',
            Logs.__table__ + '.uuid',
            Logs.__table__ + ".app_name",
            Logs.__table__ + '.result',
            Logs.__table__ + '.create_time',
            Logs.__table__ + '.status',
            Logs.__table__ + '.args',
            Workflow.__table__ + '.name'
        )

        if str(type) != "0":
            logs_list = logs_list.where(Logs.__table__ + ".uuid", type)

        if str(keywords) == "":
            logs_list = logs_list.order_by('id', 'desc').paginate(page_count, page)
        else:
            logs_list = logs_list.where(
                Logs.__table__ + '.result',
                'like',
                '%{keywords}%'.format(keywords=keywords)
            ).or_where(
                Logs.__table__ + '.app_name',
                'like',
                '%{keywords}%'.format(keywords=keywords)
            ).order_by('id', 'desc').paginate(page_count, page)

        return Response.re(data=Page(model=logs_list).to())


@r.route("/post/logs/del", methods=['GET', 'POST'])
def post_logs_del():
    """
    删除日志记录接口
    ---
    consumes:
      - application/json
    parameters:
      - in: body
        name: body
        description: 请求体中必须包含日志记录的 id，用于删除对应日志
        required: true
        schema:
          type: object
          required:
            - id
          properties:
            id:
              type: string
              description: 日志记录ID
    responses:
      200:
        description: 删除成功，无返回数据
      400:
        description: 请求参数错误
      500:
        description: 内部服务器错误
    """
    if request.method == "POST":
        id = request.json.get("id", "")
        Logs.where('id', id).delete()
        return Response.re()


@r.route("/post/logs/error", methods=['GET', 'POST'])
def post_logs_error():
    """
    获取所有错误日志列表接口（status=2）
    ---
    produces:
      - application/json
    responses:
      200:
        description: 返回所有错误日志记录列表
        schema:
          type: object
          properties:
            data:
              type: array
              items:
                type: object
                properties:
                  id:
                    type: string
                  only_id:
                    type: string
                  uuid:
                    type: string
                  app_name:
                    type: string
                  result:
                    type: string
                  create_time:
                    type: string
                    description: 创建时间（格式：YYYY-MM-DD HH:MM:SS）
                  status:
                    type: string
                  args:
                    type: string
                  name:
                    type: string
                    description: 工作流名称
      500:
        description: 内部服务器错误
    """
    if request.method == "POST":
        try:
            logs_list = Logs.join(
                Workflow.__table__,
                Logs.__table__ + '.uuid',
                '=',
                Workflow.__table__ + '.uuid'
            ).select(
                Logs.__table__ + '.id',
                Logs.__table__ + '.only_id',
                Logs.__table__ + '.uuid',
                Logs.__table__ + ".app_name",
                Logs.__table__ + '.result',
                Logs.__table__ + '.create_time',
                Logs.__table__ + '.status',
                Logs.__table__ + '.args',
                Workflow.__table__ + '.name'
            ).where(Logs.__table__ + '.status', '2').order_by('id', 'desc').get().serialize()

            return Response.re(data=logs_list)
        except Exception as e:
            current_app.logger.error(f"获取错误日志时发生错误: {str(e)}")
            return Response.re(errcode=500, errmsg="获取错误日志失败")