#!/usr/bin/env python
# encoding:utf-8

from . import *
import uuid
from core.model import Rectification, RescanRecord
from datetime import datetime, date
from flask import request
from flasgger import swag_from
import pandas as pd
import os
from werkzeug.utils import secure_filename

@r.route("/post/rectification/save", methods=['POST'])
def save_rectification():
    """
    新建或编辑整改单
    如果JSON体中提供了 rect_id，则为编辑模式；否则为新建模式。
    ---
    tags:
      - 整改单管理
    parameters:
      - name: body
        in: body
        required: true
        schema:
          type: object
          properties:
            rect_id:
              type: string
              description: "整改单ID。提供则为编辑，不提供则为新建。"
            serial_number: { type: string, description: "整改单编号" }
            vuln_name: { type: string, description: "隐患名称" }
            retest_status: { type: boolean, description: "复测状态" }
            source_unit: { type: string, description: "来源单位" }
            source_code: { type: string, description: "来源编号" }
            issue_date: { type: string, format: "date", description: "下发日期YYYY-MM-DD" }
            rect_type: { type: string, description: "整改单类型" }
            description: { type: string, description: "隐患描述" }
            vuln_type: { type: string, description: "隐患类型" }
            vuln_level: { type: string, description: "隐患级别" }
            vuln_count: { type: integer, description: "隐患个数" }
            ip: { type: string, description: "隐患IP" }
            responsible_dept: { type: string, description: "责任单位/部门" }
            is_responded: { type: boolean, description: "是否反馈" }
            response_date: { type: string, format: "date", description: "反馈日期YYYY-MM-DD" }
            is_fixed: { type: boolean, description: "是否整改" }
            fix_details: { type: string, description: "整改详情" }
            note: { type: string, description: "备注" }
            rect_deadline: { type: string, format: "date", description: "整改截止日期YYYY-MM-DD" }
            remind_time: { type: string, format: "date", description: "提醒日期YYYY-MM-DD" }
    responses:
      200:
        description: 操作成功
      400:
        description: "请求参数错误或日期格式无效"
      404:
        description: "未找到要编辑的整改单"
      409:
        description: "数据冲突，如唯一键重复"
      500:
        description: "服务器内部错误"
    """
    data = request.json
    try:
        if not data:
            return Response.re(ErrMsg(400, "请求体不能为空"))
        
        rect_id = data.get('rect_id')
        now = datetime.now()

        # 处理日期字段
        for date_field in ['issue_date', 'response_date', 'rect_deadline', 'remind_time']:
            if data.get(date_field) and data[date_field]:
                try:
                    data[date_field] = datetime.strptime(data[date_field], '%Y-%m-%d').date()
                except (ValueError, TypeError):
                    return Response.re(ErrMsg(400, f"日期格式无效: {date_field}. 请使用YYYY-MM-DD 格式."))

        if rect_id:
            # --- 编辑模式 ---
            rect = Rectification.where('rect_id', rect_id).first()
            if not rect:
                return Response.re(ErrMsg(404, "未找到要更新的整改单"))

            # 逐一检查并更新字段
            if 'serial_number' in data: rect.serial_number = data.get('serial_number')
            if 'source_unit' in data: rect.source_unit = data.get('source_unit')
            if 'source_code' in data: rect.source_code = data.get('source_code')
            if 'issue_date' in data: rect.issue_date = data.get('issue_date')
            if 'rect_type' in data: rect.rect_type = data.get('rect_type')
            if 'description' in data: rect.description = data.get('description')
            if 'vuln_name' in data: rect.vuln_name = data.get('vuln_name')            
            if 'vuln_type' in data: rect.vuln_type = data.get('vuln_type')
            if 'vuln_level' in data: rect.vuln_level = data.get('vuln_level')
            if 'vuln_count' in data: rect.vuln_count = data.get('vuln_count')
            if 'ip' in data: rect.ip = data.get('ip')
            if 'responsible_dept' in data: rect.responsible_dept = data.get('responsible_dept')
            if 'is_responded' in data: rect.is_responded = data.get('is_responded')
            if 'response_date' in data: rect.response_date = data.get('response_date')
            if 'is_fixed' in data: rect.is_fixed = data.get('is_fixed')
            if 'fix_details' in data: rect.fix_details = data.get('fix_details')
            if 'retest_status' in data: rect.retest_status = data.get('retest_status')
            if 'note' in data: rect.note = data.get('note')
            if 'rect_deadline' in data: rect.rect_deadline = data.get('rect_deadline')
            if 'remind_time' in data: rect.remind_time = data.get('remind_time')
            
            rect.update_time = now

            # 避免MassAssignmentError报错
            rect.save() 
            logger.info(f"已更新整改单: {rect_id}")
        else:
            # --- 新建模式 ---
            if 'serial_number' not in data or not data['serial_number']:
                 return Response.re(ErrMsg(400, "新建整改单时，必须提供'serial_number'字段。"))

            rect = Rectification()

            new_rect_id = str(uuid.uuid4())
            rect.rect_id = new_rect_id

            rect.create_time = now
            rect.update_time = now
            
            rect.serial_number = data.get('serial_number')
            rect.source_unit = data.get('source_unit')
            rect.source_code = data.get('source_code')
            rect.issue_date = data.get('issue_date')
            rect.rect_type = data.get('rect_type')
            rect.description = data.get('description')
            rect.vuln_name = data.get('vuln_name')
            rect.vuln_type = data.get('vuln_type')
            rect.vuln_level = data.get('vuln_level')
            rect.vuln_count = data.get('vuln_count')
            rect.ip = data.get('ip')
            rect.responsible_dept = data.get('responsible_dept')
            rect.is_responded = data.get('is_responded', False)
            rect.response_date = data.get('response_date')
            rect.is_fixed = data.get('is_fixed', False)
            rect.fix_details = data.get('fix_details')
            rect.retest_status = data.get('retest_status', False)
            rect.note = data.get('note')
            rect.rect_deadline = data.get('rect_deadline')
            rect.remind_time = data.get('remind_time')
            
            rect.save()
            rect_id = new_rect_id
            logger.info(f"已创建新整改单: {rect_id}")
            
        return Response.re(data={"rect_id": rect_id})

    except Exception as e:
        error_str = str(e).lower()
        if ('duplicate' in error_str or 'unique' in error_str):
            return Response.re(ErrMsg(409, f"操作失败：数据冲突，可能存在唯一键重复（如整改单编号 '{data.get('serial_number')}'）。"))
        
        logger.error(f"保存整改单失败: {repr(e)}", exc_info=True)
        return Response.re(ErrMsg(500, f"服务器内部错误: {repr(e)}"))

@r.route("/post/rectification/import", methods=['POST'])
def import_rectifications_from_excel():
    """
    通过Excel文件批量导入整改单
    ---
    tags:
      - 整改单管理
    consumes:
      - multipart/form-data
    parameters:
      - name: file
        in: formData
        type: file
        required: true
        description: "包含整改单信息的Excel文件 (.xlsx, .xls)"
    responses:
      200:
        description: "导入成功"
        schema:
          type: object
          properties:
            imported_count: { type: integer, description: "成功导入的记录数" }
            failed_count: { type: integer, description: "失败的记录数" }
            total_rows: { type: integer, description: "总行数" }
            import_batch_id: { type: string, description: "本次导入的批次ID" }
            errors:
              type: array
              items:
                type: object
                properties:
                  row_index: { type: integer }
                  error: { type: string }
      400:
        description: "请求错误，如未上传文件或文件格式不受支持"
      500:
        description: "服务器内部错误"
    """
    if 'file' not in request.files:
        return Response.re(ErrMsg(400, "未找到上传的文件部分"))

    file = request.files['file']
    if file.filename == '':
        return Response.re(ErrMsg(400, "未选择任何文件"))

    # 验证文件类型
    allowed_extensions = {'xlsx', 'xls'}
    if '.' not in file.filename or file.filename.rsplit('.', 1)[1].lower() not in allowed_extensions:
        return Response.re(ErrMsg(400, "文件类型不支持，请上传 .xlsx 或 .xls 文件"))

    try:
        filename = secure_filename(file.filename)
        df = pd.read_excel(file)

        # 定义Excel列标题到数据库字段的映射
        header_map = {
            "整改单编号": "serial_number","来源单位": "source_unit", "来源编号": "source_code", 
            "下发时间": "issue_date","整改单类型": "rect_type", "隐患描述": "description", 
            "隐患名称": "vuln_name", "隐患类型": "vuln_type","隐患级别": "vuln_level", 
            "隐患个数": "vuln_count", "隐患IP": "ip", "责任单位/部门": "responsible_dept", 
            "是否反馈": "is_responded", "反馈时间": "response_date", 
            "是否整改": "is_fixed", "整改详情": "fix_details", "复测状态": "retest_status","备注": "note", 
            "整改截止时间": "rect_deadline", "提醒时间": "remind_time"
        }
        
        # 只处理映射中存在的列
        df.rename(columns=header_map, inplace=True)
        
        imported_count = 0
        failed_count = 0
        errors = []
        import_batch_id = str(uuid.uuid4())
        now = datetime.now()

        for index, row in df.iterrows():
            row_index = index + 2  # Excel行号从2开始（1是表头）
            
            try:
                # 检查必需字段
                if 'serial_number' not in row or pd.isna(row['serial_number']):
                    raise ValueError("缺少必需的“整改单编号”")

                rect = Rectification()
                rect.rect_id = str(uuid.uuid4())
                rect.create_time = now
                rect.update_time = now
                
                # 导入元数据
                rect.is_excel_imported = True
                rect.excel_filename = filename
                rect.excel_row_index = row_index
                rect.import_batch_id = import_batch_id

                # 逐一赋值
                for col_name, field_name in header_map.items():
                    # 检查DataFrame中是否存在该列
                    if field_name in row and pd.notna(row[field_name]):
                        value = row[field_name]
                        # 日期和布尔值转换
                        if field_name in ['issue_date', 'response_date', 'rect_deadline', 'remind_time']:
                            if isinstance(value, datetime):
                                value = value.date()
                            else: # 如果是字符串，则尝试转换
                                value = datetime.strptime(str(value).split(' ')[0], '%Y-%m-%d').date()
                        elif field_name in ['is_responded', 'is_fixed', 'retest_status']: # 新增字段适配
                            value = str(value).lower() in ['true', '1', '是', 'yes']

                        setattr(rect, field_name, value)

                rect.save()
                imported_count += 1

            except Exception as row_error:
                failed_count += 1
                errors.append({"row_index": row_index, "error": repr(row_error)})
                logger.warning(f"导入Excel第 {row_index} 行失败: {repr(row_error)}")

        return Response.re(data={
            "imported_count": imported_count,
            "failed_count": failed_count,
            "total_rows": len(df),
            "import_batch_id": import_batch_id,
            "errors": errors
        })

    except Exception as e:
        logger.error(f"处理Excel文件失败: {repr(e)}", exc_info=True)
        return Response.re(ErrMsg(500, f"服务器内部错误: {repr(e)}"))

@r.route("/post/rectification/delete", methods=['POST'])
def batch_delete_rectification():
    """
    批量删除整改单
    根据提供的ID列表，删除一个或多个整改单记录。
    ---
    tags:
      - 整改单管理
    parameters:
      - name: body
        in: body
        required: true
        schema:
          type: object
          properties:
            rect_ids:
              type: array
              items:
                type: string
              description: "需要删除的整改单ID列表"
    responses:
      200:
        description: "删除成功"
      400:
        description: "请求参数错误"
      500:
        description: "服务器内部错误"
    """
    try:
        data = request.json
        rect_ids = data.get('rect_ids')

        if not isinstance(rect_ids, list) or not rect_ids:
            return Response.re(ErrMsg(400, "参数 rect_ids 必须是一个非空的ID列表"))

        # 删除w5_rescan_record表关联记录
        RescanRecord.where_in('rect_id', rect_ids).delete()
        
        # 删除w5_rectification表记录
        deleted_count = Rectification.where_in('rect_id', rect_ids).delete()

        logger.info(f"成功批量删除 {deleted_count} 条整改单及其关联记录。")
        return Response.re(data={"deleted_count": deleted_count})

    except Exception as e:
        logger.error(f"批量删除整改单失败: {e}", exc_info=True)
        return Response.re(ErrMsg(500, f"服务器内部错误: {str(e)}"))


@r.route("/post/rectification/fix", methods=['POST'])
def batch_update_fix_status():
    """
    批量更新整改状态
    根据ID列表，批量修改整改单的 '是否已整改' (is_fixed) 状态。
    ---
    tags:
      - 整改单管理
    parameters:
      - name: body
        in: body
        required: true
        schema:
          type: object
          properties:
            rect_ids:
              type: array
              items:
                type: string
              description: "需要更新的整改单ID列表"
            is_fixed:
              type: boolean
              description: "目标整改状态 (true 或 false)"
    responses:
      200:
        description: "更新成功"
      400:
        description: "请求参数错误"
      500:
        description: "服务器内部错误"
    """
    try:
        data = request.json
        rect_ids = data.get('rect_ids')
        is_fixed = data.get('is_fixed')

        if not isinstance(rect_ids, list) or not rect_ids:
            return Response.re(ErrMsg(400, "参数 rect_ids 必须是一个非空的ID列表"))
        if not isinstance(is_fixed, bool):
            return Response.re(ErrMsg(400, "参数 is_fixed 必须是布尔值 (true/false)"))

        update_data = {
            "is_fixed": is_fixed,
            "update_time": datetime.now()
        }
        
        updated_count = Rectification.where_in('rect_id', rect_ids).update(update_data)
        
        logger.info(f"成功将 {updated_count} 条整改单的整改状态更新为 {is_fixed}")
        return Response.re(data={"updated_count": updated_count})

    except Exception as e:
        logger.error(f"批量更新整改状态失败: {e}", exc_info=True)
        return Response.re(ErrMsg(500, f"服务器内部错误: {str(e)}"))

@r.route("/post/rectification/rescan", methods=['POST'])
def batch_update_rescan_status():
    """
    批量更新复查状态
    根据ID列表，批量修改整改单的 '是否已反馈' (is_responded) 状态，并更新 '上次复测时间'。
    ---
    tags:
      - 整改单管理
    parameters:
      - name: body
        in: body
        required: true
        schema:
          type: object
          properties:
            rect_ids:
              type: array
              items:
                type: string
              description: "需要更新的整改单ID列表"
            is_responded:
              type: boolean
              description: "目标复查/反馈状态 (true 或 false)"
    responses:
      200:
        description: "更新成功"
      400:
        description: "请求参数错误"
      500:
        description: "服务器内部错误"
    """
    try:
        data = request.json
        rect_ids = data.get('rect_ids')
        is_responded = data.get('is_responded')

        if not isinstance(rect_ids, list) or not rect_ids:
            return Response.re(ErrMsg(400, "参数 rect_ids 必须是一个非空的ID列表"))
        if not isinstance(is_responded, bool):
            return Response.re(ErrMsg(400, "参数 is_responded 必须是布尔值 (true/false)"))

        now = datetime.now()
        update_data = {
            "is_responded": is_responded,
            "last_rescan_time": now.date(), # 更新上次复测时间
            "update_time": now
        }
        
        updated_count = Rectification.where_in('rect_id', rect_ids).update(update_data)
        
        logger.info(f"成功将 {updated_count} 条整改单的复查状态更新为 {is_responded}")
        return Response.re(data={"updated_count": updated_count})

    except Exception as e:
        logger.error(f"批量更新复查状态失败: {e}", exc_info=True)
        return Response.re(ErrMsg(500, f"服务器内部错误: {str(e)}"))


@r.route("/post/rectification/list", methods=['POST'])
def post_rectification_list():
    """
    查询整改单列表
    支持分页和多条件筛选查询。
    ---
    tags:
      - 整改单管理
    parameters:
      - name: body
        in: body
        required: false
        schema:
          type: object
          properties:
            page: { type: integer, default: 1, description: "页码" }
            page_size: { type: integer, default: 10, description: "每页数量" }
            vuln_name: { type: string, description: "按隐患名称模糊查询" }
            retest_status: { type: boolean, description: "按复测状态筛选" }
            rect_type: { type: string, description: "按整改单类型筛选" }
            vuln_level: { type: string, description: "按隐患级别筛选" }
            ip: { type: string, description: "按IP地址模糊查询" }
            responsible_dept: { type: string, description: "按责任单位/部门模糊查询" }
            is_fixed: { type: boolean, description: "按是否整改筛选" }
            is_responded: { type: boolean, description: "按是否反馈/复查筛选" }
    responses:
      200:
        description: "成功返回整改单列表"
      500:
        description: "服务器内部错误"
    """
    try:
        data = request.json or {}
        page = int(data.get('page', 1))
        page_size = int(data.get('page_size', 10))
        
        query = Rectification.select()
        
        # 字符串模糊匹配筛选
        for field in ['rect_type', 'vuln_level', 'ip', 'responsible_dept', 'vuln_name']:
            value = data.get(field)
            if value:
                query = query.where(field, 'like', f'%{value}%')
        
        # 布尔值筛选
        for field in ['is_fixed', 'is_responded', 'retest_status']:
            value = data.get(field)
            if value is not None:
                query = query.where(field, value)

        total_count = query.count()
        total_pages = (total_count + page_size - 1) // page_size if page_size > 0 else 0
        
        records = query.order_by('create_time', 'desc').offset((page - 1) * page_size).limit(page_size).get()
        
        result_list = []
        for rec in records:
            rec_data = {
                'rect_id': rec.rect_id,
                'serial_number': rec.serial_number,
                'source_unit': rec.source_unit,
                'source_code': rec.source_code,
                'issue_date': rec.issue_date.strftime('%Y-%m-%d') if rec.issue_date else None,
                'rect_type': rec.rect_type,
                'description': rec.description,
                'vuln_name': rec.vuln_name,
                'vuln_type': rec.vuln_type,
                'vuln_level': rec.vuln_level,
                'vuln_count': rec.vuln_count,
                'ip': rec.ip,
                'responsible_dept': rec.responsible_dept,
                'is_responded': rec.is_responded,
                'response_date': rec.response_date.strftime('%Y-%m-%d') if rec.response_date else None,
                'is_fixed': rec.is_fixed,
                'fix_details': rec.fix_details,
                'retest_status': rec.retest_status,
                'note': rec.note,
                'rect_deadline': rec.rect_deadline.strftime('%Y-%m-%d') if rec.rect_deadline else None,
                'remind_time': rec.remind_time.strftime('%Y-%m-%d') if rec.remind_time else None,
                'last_rescan_time': rec.last_rescan_time.strftime('%Y-%m-%d') if rec.last_rescan_time else None,
                'create_time': rec.create_time.strftime('%Y-%m-%d %H:%M:%S') if rec.create_time else None,
                'update_time': rec.update_time.strftime('%Y-%m-%d %H:%M:%S') if rec.update_time else None
            }
            result_list.append(rec_data)

        response_data = {
            'list': result_list,
            'pagination': {
                'current_page': page,
                'page_size': page_size,
                'total_count': total_count,
                'total_pages': total_pages
            }
        }
        return Response.re(data=response_data)

    except Exception as e:
        logger.error(f"查询整改单列表失败: {e}", exc_info=True)
        return Response.re(ErrMsg(500, f"服务器内部错误: {str(e)}"))
    
@r.route("/post/rectification/detail", methods=['POST'])
def post_rectification_detail():
    """
    查询单个整改单详情
    根据整改单ID获取详细信息。
    ---
    tags:
      - 整改单管理
    parameters:
      - name: body
        in: body
        required: true
        schema:
          type: object
          properties:
            rect_id:
              type: string
              required: true
              description: "整改单ID"
    responses:
      200:
        description: "成功返回整改单详情"
      400:
        description: "请求参数错误"
      404:
        description: "整改单不存在"
      500:
        description: "服务器内部错误"
    """
    try:
        data = request.json or {}
        rect_id = data.get('rect_id')
        
        # 校验必要参数
        if not rect_id:
            return Response.re(ErrMsg(400, "缺少整改单ID参数"))
        
        # 查询数据库
        rec = Rectification.where('rect_id', rect_id).first()
        if not rec:
            return Response.re(ErrMsg(404, "整改单不存在"))
        
        # 构建响应数据
        rec_data = {
            'rect_id': rec.rect_id,
            'serial_number': rec.serial_number,
            'source_unit': rec.source_unit,
            'source_code': rec.source_code,
            'issue_date': rec.issue_date.strftime('%Y-%m-%d') if rec.issue_date else None,
            'rect_type': rec.rect_type,
            'description': rec.description,
            'vuln_name': rec.vuln_name,
            'vuln_type': rec.vuln_type,
            'vuln_level': rec.vuln_level,
            'vuln_count': rec.vuln_count,
            'ip': rec.ip,
            'responsible_dept': rec.responsible_dept,
            'is_responded': rec.is_responded,
            'response_date': rec.response_date.strftime('%Y-%m-%d') if rec.response_date else None,
            'is_fixed': rec.is_fixed,
            'fix_details': rec.fix_details,
            'retest_status': rec.retest_status,
            'note': rec.note,
            'rect_deadline': rec.rect_deadline.strftime('%Y-%m-%d') if rec.rect_deadline else None,
            'remind_time': rec.remind_time.strftime('%Y-%m-%d') if rec.remind_time else None,
            'last_rescan_time': rec.last_rescan_time.strftime('%Y-%m-%d') if rec.last_rescan_time else None,
            'create_time': rec.create_time.strftime('%Y-%m-%d %H:%M:%S') if rec.create_time else None,
            'update_time': rec.update_time.strftime('%Y-%m-%d %H:%M:%S') if rec.update_time else None
        }
        
        return Response.re(data=rec_data)

    except Exception as e:
        logger.error(f"查询整改单详情失败: {e}", exc_info=True)
        return Response.re(ErrMsg(500, f"服务器内部错误: {str(e)}"))
