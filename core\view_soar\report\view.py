#!/usr/bin/env python
# encoding:utf-8
from . import *
import json

@r.route("/get/report/list", methods=['GET', 'POST'])
def get_report_list():
    """
    获取报表列表
    ---
    consumes:
      - application/json
    produces:
      - application/json
    parameters:
      - in: body
        name: body
        description: 请求体参数，用于分页和关键字查询报表记录
        required: false
        schema:
          type: object
          properties:
            keywords:
              type: string
              description: 关键字，用于匹配 workflow_name、remarks 或 report_no
            page:
              type: integer
              default: 1
              description: 当前页码
            page_count:
              type: integer
              default: 10
              description: 每页记录数
    responses:
      200:
        description: 成功返回报表列表和分页信息
        schema:
          type: object
          properties:
            data:
              type: object
              properties:
                list:
                  type: array
                  items:
                    type: object
                    properties:
                      id:
                        type: string
                      report_no:
                        type: string
                      workflow_name:
                        type: string
                      remarks:
                        type: string
                      create_time:
                        type: string
                        description: 创建时间（格式：YYYY-MM-DD HH:MM:SS）
                pagination:
                  type: object
                  properties:
                    current_page:
                      type: integer
                    page_size:
                      type: integer
                    total_count:
                      type: integer
                    total_pages:
                      type: integer
      500:
        description: 内部服务器错误
    """
    if request.method == "POST":
        keywords = request.json.get("keywords", "")
        page = request.json.get("page", 1)
        page_count = request.json.get("page_count", 10)

        report_list = Report.select(
            Report.__table__ + '.id',
            Report.__table__ + '.report_no',
            Report.__table__ + '.workflow_name',
            Report.__table__ + ".remarks",
            Report.__table__ + '.create_time',
        )

        if str(keywords) == "":
            report_list = report_list.order_by('id', 'desc').paginate(page_count, page)
        else:
            report_list = report_list.where(
                Report.__table__ + '.workflow_name',
                'like',
                '%{keywords}%'.format(keywords=keywords)
            ).or_where(
                Report.__table__ + '.remarks',
                'like',
                '%{keywords}%'.format(keywords=keywords)
            ).or_where(
                Report.__table__ + '.report_no',
                'like',
                '%{keywords}%'.format(keywords=keywords)
            ).order_by('id', 'desc').paginate(page_count, page)

        return Response.re(data=Page(model=report_list).to())


@r.route("/get/report/log", methods=['GET', 'POST'])
def get_report_log():
    """
    获取报表日志记录
    ---
    consumes:
      - application/json
    produces:
      - application/json
    parameters:
      - in: body
        name: body
        description: 请求体中必须传递 only_id 参数，用于查询报表对应的日志记录
        required: true
        schema:
          type: object
          required:
            - only_id
          properties:
            only_id:
              type: string
              description: 报表的唯一标识ID
    responses:
      200:
        description: 成功返回日志记录列表
        schema:
          type: object
          properties:
            data:
              type: array
              items:
                type: object
                properties:
                  app_uuid:
                    type: string
                  app_name:
                    type: string
                  status:
                    type: string
                  html:
                    type: string
                  args:
                    type: object
                    description: 日志参数，若为空则为原始字符串，否则为解析后的 JSON 对象
                  create_time:
                    type: string
                    description: 日志创建时间
      500:
        description: 内部服务器错误
    """
    if request.method == "POST":
        only_id = request.json.get("only_id", "")

        logs_list = Logs.select(
            'app_uuid',
            'app_name',
            'status',
            'html',
            'args',
            'create_time',
        ).where("only_id", only_id).get()

        result_data = []

        for log in logs_list:
            data = {}
            data['app_uuid'] = log.app_uuid
            data['app_name'] = log.app_name
            data['status'] = log.status
            data['html'] = log.html

            if str(log.args).strip() == "":
                data['args'] = log.args
            else:
                data['args'] = json.loads(log.args)

            data['create_time'] = log.create_time
            result_data.append(data)

        return Response.re(data=result_data)


@r.route("/post/report/del", methods=['GET', 'POST'])
def post_report_del():
    """
    删除报表记录
    ---
    consumes:
      - application/json
    parameters:
      - in: body
        name: body
        description: 请求体中必须传递报表记录的 id，用于删除对应的报表记录
        required: true
        schema:
          type: object
          required:
            - id
          properties:
            id:
              type: string
              description: 报表记录ID
    responses:
      200:
        description: 删除成功，无返回数据
      400:
        description: 请求参数错误
      500:
        description: 内部服务器错误
    """
    if request.method == "POST":
        id = request.json.get("id", "")
        Report.where('id', id).delete()
        return Response.re()
