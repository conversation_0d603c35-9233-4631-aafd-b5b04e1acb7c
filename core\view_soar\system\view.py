#!/usr/bin/env python
# encoding:utf-8
from . import *
import json
import shutil
import os
from werkzeug.utils import secure_filename

@r.route("/get/system/list", methods=['GET', 'POST'])
def get_system_list():
    """
    获取系统配置信息列表
    ---
    consumes:
      - application/json
    produces:
      - application/json
    parameters:
      - in: body
        name: body
        description: 此接口无需传入参数，返回系统配置数据列表（key-value 格式）
        required: false
        schema:
          type: object
    responses:
      200:
        description: 成功返回系统配置信息
        schema:
          type: object
          properties:
            data:
              type: object
              additionalProperties:
                type: string
      500:
        description: 内部服务器错误
    """
    if request.method == "POST":
        setting = Setting.select('id', 'key', 'value').get()
        data = {}
        for s in setting:
            data[s.key] = s.value
        return Response.re(data=data)


@r.route("/post/system/w5key", methods=['GET', 'POST'])
def post_system_w5key():
    """
    更新系统 w5_key 配置
    ---
    consumes:
      - application/json
    parameters:
      - in: body
        name: body
        description: 请求体中必须包含 w5key 参数，用于更新系统配置中的 w5_key
        required: true
        schema:
          type: object
          required:
            - w5key
          properties:
            w5key:
              type: string
              description: 系统 w5_key 配置的值
    responses:
      200:
        description: 更新成功
      400:
        description: 请求参数错误
      500:
        description: 内部服务器错误
    """
    if request.method == "POST":
        w5key = request.json.get("w5key", "")
        Setting.where("key", "w5_key").update({
            "value": w5key,
            "update_time": Time.get_date_time()
        })
        redis.set("w5_key", str(w5key))
        return Response.re()


@r.route("/post/system/apikey", methods=['GET', 'POST'])
def post_system_apikey():
    """
    生成新的 API 密钥
    ---
    consumes:
      - application/json
    parameters:
      - in: body
        name: body
        description: 此接口无需传入参数，会自动生成新的 API 密钥
        required: false
        schema:
          type: object
    responses:
      200:
        description: 成功生成 API 密钥
      500:
        description: 内部服务器错误
    """
    if request.method == "POST":
        make_api_key()
        return Response.re()


@r.route("/post/system/del", methods=['GET', 'POST'])
def post_system_del():
    """
    删除系统数据
    ---
    consumes:
      - application/json
    parameters:
      - in: body
        name: body
        description: 请求体中必须传入 type 参数，用于指定删除的数据类型：
          1 - 删除 Workflow 数据
          2 - 删除 Logs 数据
          3 - 删除 Variablen 数据
          4 - 清空 Redis 数据库
          5 - 删除 Report 数据
        required: true
        schema:
          type: object
          required:
            - type
          properties:
            type:
              type: integer
              description: 删除数据的类型（1-5）
    responses:
      200:
        description: 数据删除成功
      400:
        description: 请求参数错误
      500:
        description: 内部服务器错误
    """
    if request.method == "POST":
        type = request.json.get("type", 0)
        if type == 1:
            Workflow.truncate()
        elif type == 2:
            Logs.truncate()
        elif type == 3:
            Variablen.truncate()
        elif type == 4:
            redis.flushdb()
        elif type == 5:
            Report.truncate()
        return Response.re()


@r.route("/post/system/placement", methods=['GET', 'POST'])
def post_system_placement():
    """
    更新系统 placement 配置
    ---
    consumes:
      - application/json
    parameters:
      - in: body
        name: body
        description: 请求体中必须传入 placement 参数，用于更新系统的 placement 配置
        required: true
        schema:
          type: object
          required:
            - placement
          properties:
            placement:
              type: string
              description: 系统 placement 配置的值
    responses:
      200:
        description: 配置更新成功
      400:
        description: 请求参数错误
      500:
        description: 内部服务器错误
    """
    if request.method == "POST":
        placement = request.json.get("placement", "")
        Setting.where("key", "placement").update({
            "value": placement,
            "update_time": Time.get_date_time()
        })
        return Response.re()


@r.route("/get/system/placement", methods=['GET', 'POST'])
def get_system_placement():
    """
    获取系统 placement 配置信息
    ---
    consumes:
      - application/json
    parameters:
      - in: body
        name: body
        description: 此接口无需传入参数，返回系统 placement 配置信息
        required: false
        schema:
          type: object
    responses:
      200:
        description: 成功返回 placement 配置信息
        schema:
          type: object
          properties:
            data:
              type: object
              additionalProperties:
                type: string
      500:
        description: 内部服务器错误
    """
    if request.method == "POST":
        setting = Setting.where("key", "placement").get()
        data = {}
        for s in setting:
            data[s.key] = s.value
        return Response.re(data=data)


def get_w5_json():
    try:
        result = requests.get(url=current_app.config["update_path"] + "/w5.json", timeout=5)
        return result.json()
    except Exception as e:
        return "fail"


@r.route("/get/system/w5json", methods=['GET', 'POST'])
def post_system_w5json():
    """
    获取 w5.json 配置数据
    ---
    consumes:
      - application/json
    parameters:
      - in: body
        name: body
        description: 此接口无需传入参数，返回从 update_path 获取的 w5.json 数据
        required: false
        schema:
          type: object
    responses:
      200:
        description: 成功返回 w5.json 数据（JSON 格式）
      500:
        description: 内部服务器错误或超时
    """
    if request.method == "POST":
        w5_data = get_w5_json()
        return Response.re(data=w5_data)


def make_api_key():
    api_key = Random.make_token(string="api_key")

    Setting.where("key", "api_key").update(
        {
            "value": api_key,
            "update_time": Time.get_date_time()
        }
    )

    redis.set("api_key", str(api_key))


def init_key():
    setting = Setting.select("value").where('key', "api_key").first()
    if setting:
        if str(setting.value).strip() == "" or setting.value is None:
            make_api_key()


def init_timer():
    def setting():
        manage_timer = ManageTimer()
        manage_timer.start()
        rpc = ThreadedServer(service=manage_timer, port=53124, auto_register=False)
        rpc.start()
    try:
        s = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        s.connect(("127.0.0.1", 53124))
        s.shutdown(2)
    except:
        result = redis.set("manage_timer_lock", 1, nx=True, ex=8)
        if result:
            t = threading.Thread(target=setting)
            t.setDaemon(True)
            t.start()


def init_async():
    def setting():
        new_loop = asyncio.new_event_loop()
        asyncio.set_event_loop(new_loop)
        new_loop.run_forever()
    t = threading.Thread(target=setting)
    t.setDaemon(True)
    t.start()
