#!/usr/bin/env python
# encoding:utf-8
from . import *
import json

@r.route("/get/timer/list", methods=['GET', 'POST'])
def get_timer_list():
    """
    获取定时任务列表
    ---
    consumes:
      - application/json
    produces:
      - application/json
    parameters:
      - in: body
        name: body
        description: 请求体参数，用于分页和关键字查询定时任务记录
        required: false
        schema:
          type: object
          properties:
            keywords:
              type: string
              description: 搜索关键词（匹配工作流名称）
            page:
              type: integer
              default: 1
              description: 当前页码
            page_count:
              type: integer
              default: 10
              description: 每页记录数
    responses:
      200:
        description: 返回定时任务记录列表及分页信息
        schema:
          type: object
          properties:
            data:
              type: object
              properties:
                list:
                  type: array
                  items:
                    type: object
                    properties:
                      id:
                        type: string
                      timer_uuid:
                        type: string
                      uuid:
                        type: string
                      type:
                        type: string
                      interval_type:
                        type: string
                      time:
                        type: string
                      start_date:
                        type: string
                      end_date:
                        type: string
                      jitter:
                        type: string
                      status:
                        type: string
                      update_time:
                        type: string
                      create_time:
                        type: string
                      name:
                        type: string
                        description: 工作流名称
                pagination:
                  type: object
                  properties:
                    current_page:
                      type: integer
                    page_size:
                      type: integer
                    total_count:
                      type: integer
                    total_pages:
                      type: integer
      500:
        description: 内部服务器错误
    """
    if request.method == "POST":
        keywords = request.json.get("keywords", "")
        page = request.json.get("page", 1)
        page_count = request.json.get("page_count", 10)

        timer_list = Timer.join(
            Workflow.__table__,
            Timer.__table__ + '.uuid',
            '=',
            Workflow.__table__ + '.uuid'
        ).select(
            Timer.__table__ + '.id',
            Timer.__table__ + '.timer_uuid',
            Timer.__table__ + '.uuid',
            Timer.__table__ + ".type",
            Timer.__table__ + '.interval_type',
            Timer.__table__ + '.time',
            Timer.__table__ + '.start_date',
            Timer.__table__ + '.end_date',
            Timer.__table__ + '.jitter',
            Timer.__table__ + '.status',
            Timer.__table__ + '.update_time',
            Timer.__table__ + '.create_time',
            Workflow.__table__ + '.name'
        )

        if str(keywords) == "":
            timer_list = timer_list.order_by('id', 'desc').paginate(page_count, page)
        else:
            timer_list = timer_list.where(
                Workflow.__table__ + '.name',
                'like',
                '%{keywords}%'.format(keywords=keywords)
            ).order_by('id', 'desc').paginate(page_count, page)

        return Response.re(data=Page(model=timer_list).to())


@r.route("/post/timer/start_pause", methods=['GET', 'POST'])
def post_timer_start_pause():
    """
    启动或暂停定时任务
    ---
    consumes:
      - application/json
    produces:
      - application/json
    parameters:
      - in: body
        name: body
        description: 请求体中必须包含 uuid 和 type 参数，uuid 指定任务ID（或 "all" 表示全部），type 指定操作类型："start" 或 "pause"
        required: true
        schema:
          type: object
          required:
            - uuid
            - type
          properties:
            uuid:
              type: string
              description: 定时任务的 UUID 或 "all"
            type:
              type: string
              description: 操作类型，"start" 表示启动，"pause" 表示暂停
    responses:
      200:
        description: 操作成功
        schema:
          type: object
          properties:
            data:
              type: string
              description: 成功信息
      500:
        description: 内部服务器错误
    """
    if request.method == "POST":
        uuid = request.json.get("uuid", "")
        type = request.json.get("type", "")

        conn = rpyc.connect('localhost', 53124)

        if type == "start":
            if uuid == "all":
                conn.root.resume_all()
            else:
                conn.root.resume(uuid)
        elif type == "pause":
            if uuid == "all":
                conn.root.pause_all()
            else:
                conn.root.pause(uuid)

        conn.close()
        return Response.re()
