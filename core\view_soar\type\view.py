#!/usr/bin/env python
# encoding:utf-8
from . import *

@r.route("/get/type/list", methods=['GET', 'POST'])
def get_type_list():
    """
    获取类型列表
    ---
    consumes:
      - application/json
    produces:
      - application/json
    parameters:
      - in: body
        name: body
        description: 请求体参数，用于按类型或关键字搜索类型记录
        required: false
        schema:
          type: object
          properties:
            type:
              type: string
              default: "0"
              description: 类型标识，"0" 表示不过滤
            keywords:
              type: string
              description: 搜索关键词，用于匹配 name 字段
    responses:
      200:
        description: 返回类型记录列表
        schema:
          type: object
          properties:
            data:
              type: array
              items:
                type: object
                properties:
                  id:
                    type: string
                  type:
                    type: string
                  name:
                    type: string
                  update_time:
                    type: string
                    description: 更新时间
                  create_time:
                    type: string
                    description: 创建时间
      500:
        description: 内部服务器错误
    """
    if request.method == "POST":
        type = request.json.get("type", "0")
        keywords = request.json.get("keywords", "")

        type_list = Types.select('id', 'type', 'name', 'update_time', 'create_time')

        if str(type) != "0":
            type_list = type_list.where("type", type)

        if str(keywords) == "":
            type_list = type_list.order_by('id', 'desc').get()
        else:
            type_list = type_list.or_where('name', 'like', '%{keywords}%'.format(keywords=keywords)).order_by(
                'id', 'desc').get()

        return Response.re(data=type_list.serialize())


@r.route("/post/type/add", methods=['GET', 'POST'])
def post_type_add():
    """
    添加新的类型记录
    ---
    consumes:
      - application/json
    parameters:
      - in: body
        name: body
        description: 请求体中必须包含 type 和 name 参数，用于添加新的类型记录
        required: true
        schema:
          type: object
          required:
            - type
            - name
          properties:
            type:
              type: string
              description: 类型标识
            name:
              type: string
              description: 类型名称
    responses:
      200:
        description: 添加成功
      400:
        description: 类型记录已存在或请求参数错误
      500:
        description: 内部服务器错误
    """
    if request.method == "POST":
        type = request.json.get("type", "")
        name = request.json.get("name", "")

        is_type_use = Types.where('type', type).where('name', name).first()
        if is_type_use:
            return Response.re(err=ErrType)

        Types.insert({
            'type': type,
            'name': name,
            'update_time': Time.get_date_time(),
            'create_time': Time.get_date_time()
        })

        return Response.re()


@r.route("/post/type/update", methods=['GET', 'POST'])
def post_type_update():
    """
    更新类型记录
    ---
    consumes:
      - application/json
    parameters:
      - in: body
        name: body
        description: 请求体中必须包含 id、type 和 name 参数，用于更新已有的类型记录
        required: true
        schema:
          type: object
          required:
            - id
            - type
            - name
          properties:
            id:
              type: string
              description: 类型记录ID
            type:
              type: string
              description: 新的类型标识
            name:
              type: string
              description: 新的类型名称
    responses:
      200:
        description: 更新成功
      400:
        description: 类型记录已存在或请求参数错误
      500:
        description: 内部服务器错误
    """
    if request.method == "POST":
        id = request.json.get("id", "")
        type = request.json.get("type", "")
        name = request.json.get("name", "")

        is_type_use = Types.where('type', type).where('name', name).first()
        if is_type_use:
            return Response.re(err=ErrType)

        Types.where('id', id).update(
            {
                "name": name,
                "type": type,
                "update_time": Time.get_date_time()
            }
        )

        return Response.re()


@r.route("/post/type/del", methods=['GET', 'POST'])
def post_type_del():
    """
    删除类型记录
    ---
    consumes:
      - application/json
    parameters:
      - in: body
        name: body
        description: 请求体中必须包含 id 和 type 参数，用于删除类型记录
        required: true
        schema:
          type: object
          required:
            - id
            - type
          properties:
            id:
              type: string
              description: 类型记录ID
            type:
              type: string
              description: 类型标识，1 表示 Workflow 类型，2 表示 Variablen 类型
    responses:
      200:
        description: 删除成功
      400:
        description: 类型正在被使用，无法删除
      500:
        description: 内部服务器错误
    """
    if request.method == "POST":
        id = request.json.get("id", "")
        type = request.json.get("type", "")

        db_name = ""
        if str(type) == "1":
            db_name = Workflow.__table__
        elif str(type) == "2":
            db_name = Variablen.__table__

        is_type_use = db.table(db_name).where('type_id', id).first()
        if is_type_use:
            return Response.re(err=ErrTypeUse)

        Types.where('id', id).delete()
        return Response.re()
