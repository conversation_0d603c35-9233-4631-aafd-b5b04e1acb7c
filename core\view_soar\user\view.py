#!/usr/bin/env python
# encoding:utf-8
from . import *
import json

@r.route("/get/user/list", methods=['GET', 'POST'])
def get_user_list():
    """
    获取用户列表接口
    ---
    consumes:
      - application/json
    produces:
      - application/json
    parameters:
      - in: body
        name: body
        description: 请求体参数，用于分页及关键字查询用户记录
        required: false
        schema:
          type: object
          properties:
            keywords:
              type: string
              description: 关键字，用于匹配账号、昵称或邮箱
            page:
              type: integer
              default: 1
              description: 当前页码
            page_count:
              type: integer
              default: 10
              description: 每页记录数
    responses:
      200:
        description: 返回用户列表及分页信息
        schema:
          type: object
          properties:
            data:
              type: object
              properties:
                list:
                  type: array
                  items:
                    type: object
                    properties:
                      id:
                        type: string
                      account:
                        type: string
                      nick_name:
                        type: string
                      email:
                        type: string
                      status:
                        type: string
                      update_time:
                        type: string
                      create_time:
                        type: string
                      avatar:
                        type: string
                      role_id:
                        type: string
                      role_name:
                        type: string
                pagination:
                  type: object
                  properties:
                    current_page:
                      type: integer
                    page_size:
                      type: integer
                    total_count:
                      type: integer
                    total_pages:
                      type: integer
      500:
        description: 内部服务器错误
    """
    if request.method == "POST":
        keywords = request.json.get("keywords", "")
        page = request.json.get("page", 1)
        page_count = request.json.get("page_count", 10)

        if str(keywords) == "":
            user_list = Users.join(
                UserRole.__table__,
                Users.__table__ + '.id',
                '=',
                UserRole.__table__ + '.user_id'
            ).join(
                Role.__table__,
                UserRole.__table__ + '.role_id',
                '=',
                Role.__table__ + '.id'
            ).select(
                Users.__table__ + '.id',
                Users.__table__ + '.account',
                Users.__table__ + '.nick_name',
                Users.__table__ + '.email',
                Users.__table__ + '.status',
                Users.__table__ + '.update_time',
                Users.__table__ + '.create_time',
                Users.__table__ + '.avatar',
                Role.__table__ + '.id as role_id',
                Role.__table__ + '.name as role_name',
            ).order_by('id', 'desc').paginate(page_count, page)
        else:
            user_list = Users.join(
                UserRole.__table__,
                Users.__table__ + '.id',
                '=',
                UserRole.__table__ + '.user_id'
            ).join(
                Role.__table__,
                UserRole.__table__ + '.role_id',
                '=',
                Role.__table__ + '.id'
            ).select(
                Users.__table__ + '.id',
                Users.__table__ + '.account',
                Users.__table__ + '.nick_name',
                Users.__table__ + '.email',
                Users.__table__ + '.status',
                Users.__table__ + '.update_time',
                Users.__table__ + '.create_time',
                Users.__table__ + '.avatar',
                Role.__table__ + '.id as role_id',
                Role.__table__ + '.name as role_name',
            ).where(
                Users.__table__ + '.account',
                'like',
                '%{keywords}%'.format(keywords=keywords)
            ).or_where(
                Users.__table__ + '.nick_name',
                'like',
                '%{keywords}%'.format(keywords=keywords)
            ).or_where(
                Users.__table__ + '.email',
                'like',
                '%{keywords}%'.format(keywords=keywords)
            ).order_by('id', 'desc').paginate(page_count, page)

        return Response.re(data=Page(model=user_list).to())


@r.route("/get/user/simple_list", methods=['GET', 'POST'])
def get_user_simple_list():
    """
    获取用户简单列表接口
    ---
    consumes:
      - application/json
    produces:
      - application/json
    parameters:
      - in: body
        name: body
        description: 此接口无需传入参数，返回用户 ID 和昵称列表
        required: false
        schema:
          type: object
    responses:
      200:
        description: 返回用户简单列表数据
        schema:
          type: object
          properties:
            data:
              type: array
              items:
                type: object
                properties:
                  id:
                    type: string
                  nick_name:
                    type: string
      500:
        description: 内部服务器错误
    """
    if request.method == "POST":
        sql = '''
        select id,nick_name from `w5_users` where status=0 ORDER BY CONVERT(nick_name USING GBK);
        '''
        result = db.select(sql)
        return Response.re(data=result)


@r.route("/get/user/info", methods=['GET', 'POST'])
def get_user_info():
    """
    获取用户详细信息接口
    ---
    consumes:
      - application/json
    produces:
      - application/json
    parameters:
      - in: body
        name: body
        description: 请求体中必须包含用户的 id
        required: true
        schema:
          type: object
          required:
            - id
          properties:
            id:
              type: string
              description: 用户ID
    responses:
      200:
        description: 返回用户详细信息
        schema:
          type: object
          properties:
            data:
              type: object
              properties:
                id:
                  type: string
                account:
                  type: string
                nick_name:
                  type: string
                email:
                  type: string
                update_time:
                  type: string
                create_time:
                  type: string
                avatar:
                  type: string
      500:
        description: 内部服务器错误
    """
    if request.method == "POST":
        id = request.json.get("id", "")
        user_info = Users.select(
            'id',
            'account',
            'nick_name',
            'email',
            'update_time',
            'create_time',
            'avatar'
        ).where("id", id).first()
        return Response.re(data=user_info.serialize())


@r.route("/post/user/add", methods=['GET', 'POST'])
def post_user_add():
    """
    添加新用户接口
    ---
    consumes:
      - application/json
    parameters:
      - in: body
        name: body
        description: 请求体中必须包含账号、密码、昵称、邮箱、头像和角色ID，用于添加新用户
        required: true
        schema:
          type: object
          required:
            - account
            - passwd
            - nick_name
            - email
            - role_id
          properties:
            account:
              type: string
              description: 用户账号
            passwd:
              type: string
              description: 用户密码
            nick_name:
              type: string
              description: 用户昵称
            email:
              type: string
              description: 用户邮箱
            avatar:
              type: string
              description: 用户头像（可选）
            role_id:
              type: string
              description: 用户所属角色ID
    responses:
      200:
        description: 用户添加成功
      400:
        description: 用户已存在或请求参数错误
      500:
        description: 内部服务器错误
    """
    if request.method == "POST":
        account = request.json.get("account", "")
        passwd = request.json.get("passwd", "")
        nick_name = request.json.get("nick_name", "")
        email = request.json.get("email", "")
        avatar = request.json.get("avatar", "")
        role_id = request.json.get("role_id", "")

        is_user_use = Users.where('account', account).first()
        if is_user_use:
            return Response.re(err=ErrUser)

        md5_password = Random.make_md5_password(string=passwd)
        Users.insert({
            'account': account,
            'passwd': md5_password,
            'nick_name': nick_name,
            'email': email,
            "avatar": avatar,
            'status': 0,
            'update_time': Time.get_date_time(),
            'create_time': Time.get_date_time()
        })
        user_info = Users.select('id').where('account', account).first()
        UserRole.where('user_id', user_info.id).delete()
        UserRole.insert({
            'user_id': user_info.id,
            'role_id': role_id,
            'create_time': Time.get_date_time()
        })
        return Response.re()


@r.route("/post/user/update", methods=['GET', 'POST'])
def post_user_update():
    """
    更新用户信息接口
    ---
    consumes:
      - application/json
    parameters:
      - in: body
        name: body
        description: 请求体中必须包含用户ID及需更新的信息（昵称、邮箱、密码、头像、角色ID）
        required: true
        schema:
          type: object
          required:
            - id
            - nick_name
            - email
            - role_id
          properties:
            id:
              type: string
              description: 用户ID
            nick_name:
              type: string
              description: 新昵称
            email:
              type: string
              description: 新邮箱
            passwd:
              type: string
              description: 新密码（可为空，若为空则不更新密码）
            avatar:
              type: string
              description: 新头像
            role_id:
              type: string
              description: 新的角色ID
    responses:
      200:
        description: 用户信息更新成功
      400:
        description: 请求参数错误
      500:
        description: 内部服务器错误
    """
    if request.method == "POST":
        id = request.json.get("id", "")
        nick_name = request.json.get("nick_name", "")
        email = request.json.get("email", "")
        passwd = request.json.get("passwd", "")
        avatar = request.json.get("avatar", "")
        role_id = request.json.get("role_id", "")

        if str(passwd) == "":
            Users.where('id', id).update(
                {
                    "nick_name": nick_name,
                    "email": email,
                    "avatar": avatar,
                    "update_time": Time.get_date_time()
                }
            )
        else:
            md5_password = Random.make_md5_password(string=passwd)

            Users.where('id', id).update(
                {
                    "nick_name": nick_name,
                    "email": email,
                    "avatar": avatar,
                    "passwd": md5_password,
                    "update_time": Time.get_date_time()
                }
            )

        UserRole.where('user_id', id).delete()
        UserRole.insert({
            'user_id': id,
            'role_id': role_id,
            'create_time': Time.get_date_time()
        })
        return Response.re()


@r.route("/post/user/del", methods=['GET', 'POST'])
def post_user_del():
    """
    删除用户接口
    ---
    consumes:
      - application/json
    parameters:
      - in: body
        name: body
        description: 请求体中必须包含要删除的用户ID。注意：用户不能删除自己
        required: true
        schema:
          type: object
          required:
            - id
          properties:
            id:
              type: string
              description: 要删除的用户ID
    responses:
      200:
        description: 删除成功
      400:
        description: 请求参数错误或试图删除当前登录用户
      500:
        description: 内部服务器错误
    """
    if request.method == "POST":
        id = request.json.get("id", "")
        token = request.headers.get("token")
        is_user_use = Users.where('token', token).first()
        if str(is_user_use.id) == str(id):
            return Response.re(err=ErrUserDel)
        Users.where('id', id).delete()
        return Response.re()


@r.route("/post/user/status", methods=['GET', 'POST'])
def post_user_status():
    """
    更新用户状态接口
    ---
    consumes:
      - application/json
    parameters:
      - in: body
        name: body
        description: 请求体中必须包含用户ID和要更新的状态，用户不能更新自己的状态
        required: true
        schema:
          type: object
          required:
            - id
            - status
          properties:
            id:
              type: string
              description: 用户ID
            status:
              type: string
              description: 更新后的状态值
    responses:
      200:
        description: 用户状态更新成功
      400:
        description: 请求参数错误或尝试更新当前登录用户状态
      500:
        description: 内部服务器错误
    """
    if request.method == "POST":
        id = request.json.get("id", "")
        status = request.json.get("status", "")
        token = request.headers.get("token")
        is_user_use = Users.where('token', token).first()
        if str(is_user_use.id) == str(id):
            return Response.re(err=ErrUserSwitch)
        Users.where('id', id).update({
            "status": status,
            "update_time": Time.get_date_time()
        })
        return Response.re()


@r.route("/post/user/login_history", methods=['GET', 'POST'])
def post_user_login_history():
    """
    记录用户登录历史接口
    ---
    consumes:
      - application/json
    parameters:
      - in: body
        name: body
        description: 请求体中必须包含 user_id，用于记录该用户的登录时间
        required: true
        schema:
          type: object
          required:
            - user_id
          properties:
            user_id:
              type: string
              description: 用户ID
    responses:
      200:
        description: 登录历史记录添加成功
      500:
        description: 内部服务器错误
    """
    if request.method == "POST":
        user_id = request.json.get("user_id", "")
        LoginHistory.insert({
            'user_id': user_id,
            'login_time': Time.get_date_time()
        })
        return Response.re()


@r.route("/get/nav/list", methods=['GET', 'POST'])
def get_nav_list():
    """
    获取导航列表接口
    ---
    consumes:
      - application/json
    produces:
      - application/json
    parameters:
      - in: body
        name: body
        description: 此接口无需传入参数，返回导航列表（包含 id 和 name）
        required: false
        schema:
          type: object
    responses:
      200:
        description: 返回导航列表数据
        schema:
          type: object
          properties:
            data:
              type: array
              items:
                type: object
                properties:
                  key:
                    type: string
                  name:
                    type: string
      500:
        description: 内部服务器错误
    """
    if request.method == "POST":
        nav_list = Nav.select('id', 'name',).order_by('order', 'asc').get()
        result = []
        for nav in nav_list:
            result.append({
                "key": str(nav.id),
                "name": nav.name
            })
        return Response.re(data=result)


@r.route("/get/role/list", methods=['GET', 'POST'])
def get_role_list():
    """
    获取角色列表接口
    ---
    consumes:
      - application/json
    produces:
      - application/json
    parameters:
      - in: body
        name: body
        description: 此接口无需传入参数，返回所有角色记录
        required: false
        schema:
          type: object
    responses:
      200:
        description: 返回角色列表数据
        schema:
          type: object
          properties:
            data:
              type: array
              items:
                type: object
                properties:
                  id:
                    type: string
                  name:
                    type: string
                  remarks:
                    type: string
                  update_time:
                    type: string
      500:
        description: 内部服务器错误
    """
    if request.method == "POST":
        role_list = Role.select('id', 'name', 'remarks', 'update_time').get()
        return Response.re(data=role_list.serialize())


@r.route("/get/role_nav/list", methods=['GET', 'POST'])
def get_role_nav_list():
    """
    获取角色导航列表接口
    ---
    consumes:
      - application/json
    produces:
      - application/json
    parameters:
      - in: body
        name: body
        description: 请求体中必须包含 role_id，用于查询该角色拥有的导航ID列表
        required: true
        schema:
          type: object
          required:
            - role_id
          properties:
            role_id:
              type: string
              description: 角色ID
    responses:
      200:
        description: 返回该角色的导航ID列表
        schema:
          type: object
          properties:
            data:
              type: array
              items:
                type: string
      500:
        description: 内部服务器错误
    """
    if request.method == "POST":
        role_id = request.json.get("role_id", "")
        role_nav_list = RoleNav.select('nav_id').where('role_id', role_id).get()
        result = set()
        for nav in role_nav_list:
            result.add(str(nav.nav_id))
        return Response.re(data=list(result))


@r.route("/get/user_nav/list", methods=['GET', 'POST'])
def get_user_nav_list():
    """
    获取用户导航列表接口
    ---
    consumes:
      - application/json
    produces:
      - application/json
    parameters:
      - in: body
        name: body
        description: 请求体中必须包含 user_id，用于查询该用户的导航信息
        required: true
        schema:
          type: object
          required:
            - user_id
          properties:
            user_id:
              type: string
              description: 用户ID
    responses:
      200:
        description: 返回该用户的导航列表
        schema:
          type: object
          properties:
            data:
              type: array
              items:
                type: object
                properties:
                  name:
                    type: string
                  path:
                    type: string
                  key:
                    type: string
                  icon:
                    type: string
                  is_menu:
                    type: boolean
      500:
        description: 内部服务器错误
    """
    if request.method == "POST":
        user_id = request.json.get("user_id", "")
        nav_list = UserRole.join(
            Role.__table__,
            UserRole.__table__ + '.role_id',
            '=',
            Role.__table__ + '.id'
        ).join(
            RoleNav.__table__,
            Role.__table__ + '.id',
            '=',
            RoleNav.__table__ + '.role_id'
        ).join(
            Nav.__table__,
            Nav.__table__ + '.id',
            '=',
            RoleNav.__table__ + '.nav_id'
        ).distinct().select(
            Nav.__table__ + '.name',
            Nav.__table__ + '.path',
            Nav.__table__ + '.key',
            Nav.__table__ + '.icon',
            Nav.__table__ + '.is_menu',
            Nav.__table__ + '.order'
        ).where(
            UserRole.__table__ + '.user_id', user_id
        ).order_by('order', 'asc').get()
        result = []
        for nav in nav_list:
            result.append({
                "name": nav.name,
                "path": nav.path,
                "key": nav.key,
                "icon": nav.icon,
                "is_menu": nav.is_menu
            })
        return Response.re(data=result)


@r.route("/post/role_nav/add", methods=['GET', 'POST'])
def post_role_nav_add():
    """
    添加或更新角色及其导航关联
    ---
    consumes:
      - application/json
    parameters:
      - in: body
        name: body
        description: 请求体中包含角色ID、名称、导航列表和备注信息。当 id 为 0 时为新增，否则为更新
        required: true
        schema:
          type: object
          required:
            - id
            - name
            - nav_key
          properties:
            id:
              type: integer
              description: 角色ID，新增时传 0
            name:
              type: string
              description: 角色名称
            nav_key:
              type: array
              items:
                type: string
              description: 导航项列表
            remarks:
              type: string
              description: 备注信息
    responses:
      200:
        description: 角色及导航关联添加或更新成功
      400:
        description: 角色已存在或请求参数错误
      500:
        description: 内部服务器错误
    """
    if request.method == "POST":
        id = request.json.get("id", "")
        name = request.json.get("name", "")
        nav_key = request.json.get("nav_key", "")
        remarks = request.json.get("remarks", "")

        if id == 0:
            is_exist = Role.select('name').where('name', name).first()
            if is_exist:
                return Response.re(err=ErrRoleExist)
            Role.insert_get_id({
                'name': name,
                'remarks': remarks,
                'update_time': Time.get_date_time(),
                'create_time': Time.get_date_time()
            })
            role_info = Role.select('id').where('name', name).first()
            for nav in nav_key:
                RoleNav.insert({
                    'role_id': role_info.id,
                    'nav_id': nav,
                    'create_time': Time.get_date_time()
                })
        else:
            Role.where('id', id).update({
                "name": name,
                "remarks": remarks,
                "update_time": Time.get_date_time()
            })
            RoleNav.where('role_id', id).delete()
            for nav in nav_key:
                RoleNav.insert({
                    'role_id': id,
                    'nav_id': nav,
                    'create_time': Time.get_date_time()
                })
        return Response.re()


@r.route("/post/role_nav/del", methods=['GET', 'POST'])
def post_role_nav_del():
    """
    删除角色及其导航关联接口
    ---
    consumes:
      - application/json
    parameters:
      - in: body
        name: body
        description: 请求体中必须传入角色ID，用于删除对应角色及其导航关联记录
        required: true
        schema:
          type: object
          required:
            - id
          properties:
            id:
              type: string
              description: 角色ID
    responses:
      200:
        description: 删除成功
      400:
        description: 请求参数错误
      500:
        description: 内部服务器错误
    """
    if request.method == "POST":
        id = request.json.get("id", "")
        Role.where('id', id).delete()
        RoleNav.where('role_id', id).delete()
        return Response.re()
