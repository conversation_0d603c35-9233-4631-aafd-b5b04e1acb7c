#!/usr/bin/env python
# encoding:utf-8
from . import *
import json

@r.route("/get/variablen/list", methods=['GET', 'POST'])
def get_variablen_list():
    """
    获取变量列表
    ---
    consumes:
      - application/json
    produces:
      - application/json
    parameters:
      - in: body
        name: body
        description: 请求体中可选参数，用于按类型和关键字查询变量记录
        required: false
        schema:
          type: object
          properties:
            type:
              type: string
              default: "0"
              description: 类型标识，"0" 表示不过滤
            keywords:
              type: string
              description: 搜索关键字，用于匹配 key 或 value 字段
    responses:
      200:
        description: 返回变量记录列表
        schema:
          type: object
          properties:
            data:
              type: array
              items:
                type: object
                properties:
                  id:
                    type: string
                  type_id:
                    type: string
                  type_name:
                    type: string
                  key:
                    type: string
                  value:
                    type: string
                  remarks:
                    type: string
                  status:
                    type: string
                  update_time:
                    type: string
                    description: 更新时间
                  create_time:
                    type: string
                    description: 创建时间
      500:
        description: 内部服务器错误
    """
    if request.method == "POST":
        type = request.json.get("type", "0")
        keywords = request.json.get("keywords", "")

        variablen_list = Variablen.join(
            Types.__table__,
            Variablen.__table__ + '.type_id',
            '=',
            Types.__table__ + '.id'
        ).select(
            Variablen.__table__ + '.id',
            Variablen.__table__ + '.type_id',
            Types.__table__ + '.name as type_name',
            Variablen.__table__ + '.key',
            Variablen.__table__ + '.value',
            Variablen.__table__ + '.remarks',
            Variablen.__table__ + '.status',
            Variablen.__table__ + '.update_time',
            Variablen.__table__ + '.create_time'
        )

        if str(type) != "0":
            variablen_list = variablen_list.where("type_id", type)

        if str(keywords) == "":
            variablen_list = variablen_list.order_by("id", 'desc').get()
        else:
            variablen_list = variablen_list.or_where(
                'key',
                'like',
                '%{keywords}%'.format(keywords=keywords)
            ).or_where(
                'value',
                'like',
                '%{keywords}%'.format(keywords=keywords)
            ).order_by('id', 'desc').get()

        return Response.re(data=variablen_list.serialize())


@r.route("/post/variablen/add", methods=['GET', 'POST'])
def post_variablen_add():
    """
    添加变量记录接口
    ---
    consumes:
      - application/json
    parameters:
      - in: body
        name: body
        description: 请求体中必须包含 type_id、key、value 及 remarks 参数，用于添加新的变量记录
        required: true
        schema:
          type: object
          required:
            - type_id
            - key
            - value
          properties:
            type_id:
              type: string
              description: 类型ID
            key:
              type: string
              description: 变量键
            value:
              type: string
              description: 变量值
            remarks:
              type: string
              description: 备注信息
    responses:
      200:
        description: 变量记录添加成功
      400:
        description: 变量键已存在或请求参数错误
      500:
        description: 内部服务器错误
    """
    if request.method == "POST":
        type_id = request.json.get("type_id", "")
        key = request.json.get("key", "")
        value = request.json.get("value", "")
        remarks = request.json.get("remarks", "")

        is_key_use = Variablen.where('key', key).first()
        if is_key_use:
            return Response.re(err=ErrVariablenUse)

        Variablen.insert({
            'type_id': type_id,
            'key': key,
            'value': value,
            'remarks': remarks,
            'status': 0,
            'update_time': Time.get_date_time(),
            'create_time': Time.get_date_time()
        })

        return Response.re()


@r.route("/post/variablen/update", methods=['GET', 'POST'])
def post_variablen_update():
    """
    更新变量记录接口
    ---
    consumes:
      - application/json
    parameters:
      - in: body
        name: body
        description: 请求体中必须包含 id、type_id、key、value 及 remarks 参数，用于更新现有的变量记录
        required: true
        schema:
          type: object
          required:
            - id
            - type_id
            - key
            - value
          properties:
            id:
              type: string
              description: 变量记录ID
            type_id:
              type: string
              description: 类型ID
            key:
              type: string
              description: 变量键
            value:
              type: string
              description: 变量值
            remarks:
              type: string
              description: 备注信息
    responses:
      200:
        description: 变量记录更新成功
      400:
        description: 变量键已存在（且非本记录）或请求参数错误
      500:
        description: 内部服务器错误
    """
    if request.method == "POST":
        id = request.json.get("id", "")
        type_id = request.json.get("type_id", "")
        key = request.json.get("key", "")
        value = request.json.get("value", "")
        remarks = request.json.get("remarks", "")

        is_key_use = Variablen.where('key', key).first()
        if is_key_use:
            if str(is_key_use.id) != str(id):
                return Response.re(err=ErrVariablenUse)

        Variablen.where('id', id).update(
            {
                "type_id": type_id,
                "key": key,
                "value": value,
                'remarks': remarks,
                "update_time": Time.get_date_time()
            }
        )

        return Response.re()


@r.route("/post/variablen/del", methods=['GET', 'POST'])
def post_variablen_del():
    """
    删除变量记录接口
    ---
    consumes:
      - application/json
    parameters:
      - in: body
        name: body
        description: 请求体中必须包含 id 参数，用于删除指定的变量记录
        required: true
        schema:
          type: object
          required:
            - id
          properties:
            id:
              type: string
              description: 变量记录ID
    responses:
      200:
        description: 删除成功，无返回数据
      400:
        description: 请求参数错误
      500:
        description: 内部服务器错误
    """
    if request.method == "POST":
        id = request.json.get("id", "")
        Variablen.where('id', id).delete()
        return Response.re()


@r.route("/post/variablen/status", methods=['GET', 'POST'])
def post_variablen_status():
    """
    更新变量状态接口
    ---
    consumes:
      - application/json
    parameters:
      - in: body
        name: body
        description: 请求体中必须包含 id 和 status 参数，用于更新变量记录的状态
        required: true
        schema:
          type: object
          required:
            - id
            - status
          properties:
            id:
              type: string
              description: 变量记录ID
            status:
              type: string
              description: 新状态值
    responses:
      200:
        description: 状态更新成功
      400:
        description: 请求参数错误
      500:
        description: 内部服务器错误
    """
    if request.method == "POST":
        id = request.json.get("id", "")
        status = request.json.get("status", "")

        Variablen.where('id', id).update(
            {
                "status": status,
                "update_time": Time.get_date_time()
            }
        )

        return Response.re()
