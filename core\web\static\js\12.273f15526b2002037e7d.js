webpackJsonp([12],{"hJM+":function(t,a){},jjR1:function(t,a,e){"use strict";Object.defineProperty(a,"__esModule",{value:!0});var s=[{title:"剧本",dataIndex:"name",key:"name",scopedSlots:{customRender:"name"}},{title:"创建时间",key:"create_time",dataIndex:"create_time",scopedSlots:{customRender:"create_time"}},{title:"更新时间",key:"update_time",dataIndex:"update_time",scopedSlots:{customRender:"update_time"}},{title:"状态",key:"status",dataIndex:"status",scopedSlots:{customRender:"status"}},{title:"审核人",dataIndex:"avatar",key:"avatar",scopedSlots:{customRender:"avatar"},width:80},{dataIndex:"nick_name",key:"nick_name",scopedSlots:{customRender:"nick_name"}},{title:"操作",key:"action",scopedSlots:{customRender:"action"},width:60}],n={name:"auditHome",data:function(){return{columns:s,loading:!1,so_text:"",data:[],log_data:[],id:0,uuid:"",only_id:"",audit_app:"",start_app:"",visible_audit_log:!1,select_type:"all",pagination:{total:0,defaultPageSize:10,showTotal:function(t){return"共 ".concat(t," 条数据")},showSizeChanger:!0,pageSizeOptions:["10","15","20","50","100"],onShowSizeChange:this.onPageShowSizeChange,onChange:this.onPageChange},curr_page:1,curr_page_size:10}},mounted:function(){this.onLoad()},methods:{onPageShowSizeChange:function(t,a){this.curr_page=t,this.curr_page_size=a,this.onLoad(this.so_text,this.select_type,t,a)},onPageChange:function(t,a){this.curr_page=t,this.curr_page_size=a,this.onLoad(this.so_text,this.select_type,t,a)},onLoad:function(){var t=this,a=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"",e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"all",s=arguments.length>2&&void 0!==arguments[2]?arguments[2]:1,n=arguments.length>3&&void 0!==arguments[3]?arguments[3]:10;this.loading=!0,this.$http.post("/api/v1/soar/get/audit/list",{keywords:a,type:e,page:s,page_count:n}).then(function(a){0==a.code?(t.data=a.data.list,t.pagination.total=a.data.total_count,t.loading=!1):(t.$message.error(a.msg),t.loading=!1)})},onSearch:function(t){this.so_text=t,this.onLoad(this.so_text,this.select_type)},onSelect:function(t){this.select_type=t,this.onLoad(this.so_text,this.select_type)},onCloseShow:function(){this.visible_audit_log=!1},onFilterOption:function(t,a){return a.componentOptions.children[0].text.toLowerCase().indexOf(t.toLowerCase())>=0},onReportLog:function(t){var a=this;this.$http.post("/api/v1/soar/get/report/log",{only_id:t}).then(function(t){0==t.code?(a.log_data=t.data,a.visible_audit_log=!0):a.$message.error(t.msg)})},onShowAudit:function(t,a,e,s,n){this.id=t,this.uuid=a,this.only_id=e,this.audit_app=s,this.start_app=n,this.onReportLog(e)},onAuditUpdate:function(t){var a=this;this.$http.post("/api/v1/soar/post/audit/update",{id:this.id,status:t,user:this.$cookies.get("nick_name"),workflow_uuid:this.uuid,only_id:this.only_id,audit_app:this.audit_app,start_app:this.start_app}).then(function(t){0==t.code?(a.onLoad(a.so_text,a.select_type,a.curr_page,a.curr_page_size),a.visible_audit_log=!1):a.$message.error(t.msg)})}}},o={render:function(){var t=this,a=t.$createElement,e=t._self._c||a;return e("a-layout-content",[e("div",{staticClass:"header_div"},[e("a-select",{staticClass:"align",staticStyle:{width:"120px"},attrs:{"show-search":"","filter-option":t.onFilterOption,"default-value":"all"},on:{change:t.onSelect}},[e("a-select-option",{attrs:{value:"all"}},[t._v("全部")]),t._v(" "),e("a-select-option",{attrs:{value:"0"}},[t._v("待审核")]),t._v(" "),e("a-select-option",{attrs:{value:"1"}},[t._v("审计通过")]),t._v(" "),e("a-select-option",{attrs:{value:"2"}},[t._v("审计拒绝")])],1),t._v(" "),e("a-input-search",{staticClass:"align",staticStyle:{width:"200px"},attrs:{placeholder:"请输入剧本/审核人"},on:{search:t.onSearch}})],1),t._v(" "),e("a-table",{attrs:{rowKey:"id",columns:t.columns,"data-source":t.data,loading:t.loading,pagination:t.pagination},scopedSlots:t._u([{key:"avatar",fn:function(a){return e("span",{},[e("div",{staticClass:"tableAvatar",domProps:{innerHTML:t._s(a)}})])}},{key:"nick_name",fn:function(a){return e("span",{},[e("b",[t._v(t._s(a))])])}},{key:"name",fn:function(a){return e("a-tag",{attrs:{color:"#cf3f3d"}},[t._v("\n            "+t._s(a)+"\n        ")])}},{key:"update_time",fn:function(a){return e("span",{},[t._v("\n            "+t._s(t.Dayjs(a).subtract(8,"hour").format("YYYY-MM-DD HH:mm:ss"))+"\n        ")])}},{key:"create_time",fn:function(a){return e("span",{},[t._v("\n            "+t._s(t.Dayjs(a).subtract(8,"hour").format("YYYY-MM-DD HH:mm:ss"))+"\n        ")])}},{key:"status",fn:function(a){return e("div",{},[0===a?e("a-tag",{attrs:{slot:"name",color:"#888888"},slot:"name"},[t._v("\n                待审计\n            ")]):1===a?e("a-tag",{attrs:{slot:"name",color:"#499b34"},slot:"name"},[t._v("\n                审计通过\n            ")]):2===a?e("a-tag",{attrs:{slot:"name",color:"#761b1b"},slot:"name"},[t._v("\n                审计拒绝\n            ")]):t._e()],1)}},{key:"action",fn:function(a,s){return e("span",{},[0==s.status?e("a-tooltip",{attrs:{placement:"top"}},[e("template",{slot:"title"},[e("span",[t._v("审计")])]),t._v(" "),e("a-icon",{staticClass:"pointer",attrs:{type:"audit"},on:{click:function(a){return t.onShowAudit(s.id,s.workflow_uuid,s.only_id,s.audit_app,s.start_app)}}})],2):1==s.status?e("a-tooltip",{attrs:{placement:"top"}},[e("template",{slot:"title"},[e("span",[t._v("审计通过")])]),t._v(" "),e("a-icon",{staticClass:"pointer",staticStyle:{color:"#3ac119"},attrs:{type:"check-circle"}})],2):2==s.status?e("a-tooltip",{attrs:{placement:"top"}},[e("template",{slot:"title"},[e("span",[t._v("审计拒绝")])]),t._v(" "),e("a-icon",{staticClass:"pointer",staticStyle:{color:"#bb1515"},attrs:{type:"close-circle"}})],2):t._e()],1)}}])}),t._v(" "),e("a-modal",{attrs:{title:"审计执行日志",cancelText:"拒绝",okText:"通过",maskClosable:!1,width:800,visible:t.visible_audit_log},on:{cancel:t.onCloseShow}},[t._t("default",function(){return[e("a-button",{attrs:{type:"danger"},on:{click:function(a){return t.onAuditUpdate(2)}}},[t._v("\n                拒绝\n            ")]),t._v(" "),e("a-button",{attrs:{type:"primary"},on:{click:function(a){return t.onAuditUpdate(1)}}},[t._v("\n                通过\n            ")])]},{slot:"footer"}),t._v(" "),e("div",{attrs:{id:"report"}},[e("a-row",{staticClass:"report",staticStyle:{margin:"auto"},attrs:{gutter:16}},t._l(t.log_data,function(a,s){return e("a-col",{key:s,staticClass:"div2",attrs:{span:24}},[e("a-descriptions",{attrs:{column:1,bordered:"",size:"small"}},[e("div",{attrs:{slot:"title"},slot:"title"},[e("span",{staticClass:"title"},[e("span",{staticClass:"jing"},[t._v(t._s(s+1)+"、")]),t._v(t._s(a.app_name)+"\n                            ")]),t._v(" "),"开始"!=a.app_name&&"结束"!=a.app_name&&"用户输入"!=a.app_name&&"WebHook"!=a.app_name&&"定时器"!=a.app_name&&"人工审计"!=a.app_name?e("span",{staticClass:"desc"},[t._v("\n                                "+t._s(a.args.description)+"\n                            ")]):t._e()]),t._v(" "),e("a-descriptions-item",{attrs:{label:"UUID"}},[e("b",[t._v(t._s(a.app_uuid))])]),t._v(" "),"开始"!=a.app_name&&"结束"!=a.app_name&&"用户输入"!=a.app_name&&"WebHook"!=a.app_name&&"定时器"!=a.app_name&&"人工审计"!=a.app_name?e("a-descriptions-item",{attrs:{label:"动作"}},[e("b",[t._v(t._s(a.args.action_name))])]):t._e(),t._v(" "),"开始"!=a.app_name&&"结束"!=a.app_name&&"用户输入"!=a.app_name&&"WebHook"!=a.app_name&&"定时器"!=a.app_name&&"人工审计"!=a.app_name?e("a-descriptions-item",{attrs:{label:"参数"}},t._l(a.args,function(a,s,n){return e("div",{key:n,staticClass:"args_list"},["action"!=s&&"action_name"!=s&&"description"!=s&&"node_name"!=s?e("div",[e("div",{staticClass:"args_title"},[e("span",{staticClass:"jing"},[t._v("|")]),t._v(" "+t._s(s))]),t._v(" "),e("div",[t._v(t._s(a))])]):t._e()])}),0):t._e(),t._v(" "),e("a-descriptions-item",{attrs:{label:"状态"}},[0==a.status?e("a-tag",{attrs:{color:"#469823"}},[t._v("正常")]):t._e(),t._v(" "),1==a.status?e("a-tag",{attrs:{color:"#9e8c0a"}},[t._v("警告")]):t._e(),t._v(" "),2==a.status?e("a-tag",{attrs:{color:"#9c5656"}},[t._v("错误")]):t._e(),t._v(" "),3==a.status?e("a-tag",{attrs:{color:"#bf0c0c"}},[t._v("危险")]):t._e()],1),t._v(" "),"用户输入"!==a.app_name&&"WebHook"!==a.app_name?e("a-descriptions-item",{attrs:{label:"结果"}},[e("pre",{staticStyle:{"white-space":"pre-wrap","word-wrap":"break-word"},domProps:{innerHTML:t._s(a.html)}})]):e("a-descriptions-item",{attrs:{label:"输入"}},[e("pre",{staticStyle:{"white-space":"pre-wrap","word-wrap":"break-word"},domProps:{innerHTML:t._s(a.html)}})]),t._v(" "),e("a-descriptions-item",{attrs:{label:"执行时间"}},[t._v("\n                            "+t._s(t.Dayjs(a.create_time).subtract(8,"hour").format("YYYY-MM-DD HH:mm:ss"))+"\n                        ")])],1)],1)}),1)],1)],2)],1)},staticRenderFns:[]};var i=e("owSs")(n,o,!1,function(t){e("hJM+")},"data-v-48ee060b",null);a.default=i.exports}});