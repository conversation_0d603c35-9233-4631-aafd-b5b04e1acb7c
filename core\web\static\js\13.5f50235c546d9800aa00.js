webpackJsonp([13],{Luci:function(e,t,s){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var a={name:"login",data:function(){return{form:this.$form.createForm(this),loading:!1,curr_theme:"dark",baseURL:this.BaseURL}},mounted:function(){this.getTheme(),document.querySelector(".ant-layout-content").style.padding="0px"},methods:{getTheme:function(){if(this.$cookies.isKey("theme")){var e=this.$cookies.get("theme");document.getElementById("app").className=e,this.curr_theme=e}else document.getElementById("app").className="dark",this.curr_theme="dark"},login:function(e){var t=this;e.preventDefault(),this.form.validateFields(function(e,s){e||(t.loading=!0,t.$http.post("/api/v1/soar/login",{account:s.userName,passwd:s.password}).then(function(e){if(0==e.code){var s=e.data.token,a=e.data.nick_name,o=e.data.account,i=e.data.user_id;t.$cookies.set("token",s),t.$cookies.set("nick_name",a),t.$cookies.set("account",o),t.$cookies.set("user_id",i),t.$cookies.set("theme",t.curr_theme),t.loading=!1,window.location.href="/"}else t.$message.error(e.msg),t.loading=!1}).catch(function(e){t.loading=!1}))})}}},o={render:function(){var e=this,t=e.$createElement,s=e._self._c||t;return s("a-layout-content",[s("a-row",{attrs:{type:"flex"}},[s("a-col",{staticStyle:{width:"100%"}},[s("div",{staticClass:"loginDiv"},[s("div",{staticClass:"div"},[s("div",{staticClass:"logos"},[s("div",[s("img",{staticClass:"zimg",attrs:{src:e.baseURL+"/public/logo.png"}})]),e._v(" "),s("div",[s("p",{staticClass:"ptitle"},[s("span",{staticClass:"lo"},[e._v("W5")]),e._v("SOAR")])]),e._v(" "),s("p",{staticClass:"p2"},[e._v("「 无需编写代码的安全自动化平台 」")])]),e._v(" "),s("div",{staticClass:"formDiv"},[s("a-form",{staticClass:"login-form",attrs:{form:e.form},on:{submit:e.login}},[s("a-form-item",[s("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["userName",{rules:[{required:!0,message:"账号(邮箱)不可为空"},{min:2,message:"账号(邮箱)长度不能少于2位"},{max:50,message:"账号(邮箱)长度不能超过50位"}]}],expression:"[\n                  'userName',\n                  {\n                    rules: [\n                      { required: true, message: '账号(邮箱)不可为空' },\n                      { min: 2, message: '账号(邮箱)长度不能少于2位' },\n                      { max: 50, message: '账号(邮箱)长度不能超过50位' },\n                    ],\n                  },\n                ]"}],attrs:{placeholder:"请输入账号名 or 邮箱"}},[s("a-icon",{staticStyle:{color:"#a9a9a9"},attrs:{slot:"prefix",type:"user"},slot:"prefix"})],1)],1),e._v(" "),s("a-form-item",[s("a-input-password",{directives:[{name:"decorator",rawName:"v-decorator",value:["password",{rules:[{required:!0,message:"密码不可为空"},{min:8,message:"请输入8-16位密码"},{max:16,message:"请输入8-16位密码"}]}],expression:"[\n                  'password',\n                  {\n                    rules: [\n                      { required: true, message: '密码不可为空' },\n                      { min: 8, message: '请输入8-16位密码' },\n                      { max: 16, message: '请输入8-16位密码' },\n                    ],\n                  },\n                ]"}],attrs:{type:"password",placeholder:"请输入密码"}},[s("a-icon",{staticStyle:{color:"#a9a9a9"},attrs:{slot:"prefix",type:"lock"},slot:"prefix"})],1)],1),e._v(" "),s("a-form-item",[s("a-button",{staticClass:"login-form-button",attrs:{type:"primary","html-type":"submit",loading:e.loading}},[e._v("登 录")])],1)],1)],1),e._v(" "),s("div",{staticClass:"footer"},[s("span",[e._v("Copyright © 2020 "),s("a",{staticStyle:{color:"#cd302d"},attrs:{href:"https://w5.io"}},[e._v("W5.IO")]),e._v(" All rights reserved.")])])])])])],1)],1)},staticRenderFns:[]};var i=s("owSs")(a,o,!1,function(e){s("vWCL")},"data-v-191295ee",null);t.default=i.exports},vWCL:function(e,t){}});