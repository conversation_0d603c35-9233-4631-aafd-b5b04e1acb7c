webpackJsonp([17],{"/4Dz":function(t,e,r){"use strict";var i=r("Ly3a"),n=r("kfsg"),s=r("ImkM"),a=r("otaM"),o=r("tLaq"),u=r("bzwA"),c=r("6Sc5"),h=r("5aM9"),l=r("hAT3"),f=r("WbTk");n("match",function(t,e,r){return[function(e){var r=c(this),n=a(e)?void 0:h(e,t);return n?i(n,e,r):new RegExp(e)[t](u(r))},function(t){var i=s(this),n=u(t),a=r(e,i,n);if(a.done)return a.value;if(!i.global)return f(i,n);var c=i.unicode;i.lastIndex=0;for(var h,p=[],g=0;null!==(h=f(i,n));){var d=u(h[0]);p[g]=d,""===d&&(i.lastIndex=l(n,o(i.lastIndex),c)),g++}return 0===g?null:p}]})},"/5tp":function(t,e,r){"use strict";var i=r("cJVG"),n=r("q64P"),s=r("7HxT"),a=r("upx8"),o=r("cU97"),u=a("IE_PROTO"),c=Object,h=c.prototype;t.exports=o?c.getPrototypeOf:function(t){var e=s(t);if(i(e,u))return e[u];var r=e.constructor;return n(r)&&e instanceof r?r.prototype:e instanceof c?h:null}},"/ACy":function(t,e,r){"use strict";var i=r("pe4O"),n=r("jZ4S").concat("length","prototype");e.f=Object.getOwnPropertyNames||function(t){return i(t,n)}},"/emV":function(t,e,r){"use strict";var i=r("Ly3a"),n=r("ImkM"),s=r("5aM9");t.exports=function(t,e,r){var a,o;n(t);try{if(!(a=s(t,"return"))){if("throw"===e)throw r;return r}a=i(a,t)}catch(t){o=!0,a=t}if("throw"===e)throw r;if(o)throw a;return n(a),r}},"/lQz":function(t,e,r){var i=r("UUgM").default;t.exports=function(t,e){if("object"!=i(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=i(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)},t.exports.__esModule=!0,t.exports.default=t.exports},"0Qg9":function(t,e,r){"use strict";var i,n,s,a=r("mn8y"),o=r("XLq/"),u=r("l8ma"),c=r("s6mA"),h=r("cJVG"),l=r("n+zG"),f=r("upx8"),p=r("OV1V"),g=o.TypeError,d=o.WeakMap;if(a||l.state){var v=l.state||(l.state=new d);v.get=v.get,v.has=v.has,v.set=v.set,i=function(t,e){if(v.has(t))throw new g("Object already initialized");return e.facade=t,v.set(t,e),e},n=function(t){return v.get(t)||{}},s=function(t){return v.has(t)}}else{var y=f("state");p[y]=!0,i=function(t,e){if(h(t,y))throw new g("Object already initialized");return e.facade=t,c(t,y,e),e},n=function(t){return h(t,y)?t[y]:{}},s=function(t){return h(t,y)}}t.exports={set:i,get:n,has:s,enforce:function(t){return s(t)?n(t):i(t,{})},getterFor:function(t){return function(e){var r;if(!u(e)||(r=n(e)).type!==t)throw new g("Incompatible receiver, "+t+" required");return r}}}},"0nPW":function(t,e,r){"use strict";t.exports=!1},"1JoV":function(t,e,r){"use strict";var i=r("PVqM");t.exports=!i(function(){var t=function(){}.bind();return"function"!=typeof t||t.hasOwnProperty("prototype")})},"1QUB":function(t,e,r){"use strict";var i=r("n+zG");t.exports=function(t,e){return i[t]||(i[t]=e||{})}},"1hha":function(t,e,r){"use strict";var i,n,s=r("Ly3a"),a=r("PIjx"),o=r("bzwA"),u=r("edI9"),c=r("nR2g"),h=r("1QUB"),l=r("IGON"),f=r("0Qg9").get,p=r("BlZm"),g=r("s23v"),d=h("native-string-replace",String.prototype.replace),v=RegExp.prototype.exec,y=v,m=a("".charAt),x=a("".indexOf),b=a("".replace),w=a("".slice),S=(n=/b*/g,s(v,i=/a/,"a"),s(v,n,"a"),0!==i.lastIndex||0!==n.lastIndex),P=c.BROKEN_CARET,T=void 0!==/()??/.exec("")[1];(S||T||P||p||g)&&(y=function(t){var e,r,i,n,a,c,h,p=this,g=f(p),O=o(t),A=g.raw;if(A)return A.lastIndex=p.lastIndex,e=s(y,A,O),p.lastIndex=A.lastIndex,e;var E=g.groups,C=P&&p.sticky,M=s(u,p),N=p.source,V=0,R=O;if(C&&(M=b(M,"y",""),-1===x(M,"g")&&(M+="g"),R=w(O,p.lastIndex),p.lastIndex>0&&(!p.multiline||p.multiline&&"\n"!==m(O,p.lastIndex-1))&&(N="(?: "+N+")",R=" "+R,V++),r=new RegExp("^(?:"+N+")",M)),T&&(r=new RegExp("^"+N+"$(?!\\s)",M)),S&&(i=p.lastIndex),n=s(v,C?r:p,R),C?n?(n.input=w(n.input,V),n[0]=w(n[0],V),n.index=p.lastIndex,p.lastIndex+=n[0].length):p.lastIndex=0:S&&n&&(p.lastIndex=p.global?n.index+n[0].length:i),T&&n&&n.length>1&&s(d,n[0],r,function(){for(a=1;a<arguments.length-2;a++)void 0===arguments[a]&&(n[a]=void 0)}),n&&E)for(n.groups=c=l(null),a=0;a<E.length;a++)c[(h=E[a])[0]]=n[h[1]];return n}),t.exports=y},"1j3i":function(t,e,r){"use strict";var i=r("ZswV"),n=TypeError;t.exports=function(t,e){if(i(e,t))return t;throw new n("Incorrect invocation")}},"2l35":function(t,e,r){"use strict";var i=r("2xqn"),n=r("1hha");i({target:"RegExp",proto:!0,forced:/./.exec!==n},{exec:n})},"2xqn":function(t,e,r){"use strict";var i=r("XLq/"),n=r("TkC3").f,s=r("s6mA"),a=r("LlCP"),o=r("I8+V"),u=r("yN9v"),c=r("TLw8");t.exports=function(t,e){var r,h,l,f,p,g=t.target,d=t.global,v=t.stat;if(r=d?i:v?i[g]||o(g,{}):i[g]&&i[g].prototype)for(h in e){if(f=e[h],l=t.dontCallGetSet?(p=n(r,h))&&p.value:r[h],!c(d?h:g+(v?".":"#")+h,t.forced)&&void 0!==l){if(typeof f==typeof l)continue;u(f,l)}(t.sham||l&&l.sham)&&s(f,"sham",!0),a(r,h,f,t)}}},"33+b":function(t,e,r){"use strict";var i=r("2xqn"),n=r("Ly3a"),s=r("PyKl"),a=r("MxYK"),o=r("ATld"),u=r("tlqM");i({target:"Promise",stat:!0,forced:r("aeYa")},{all:function(t){var e=this,r=a.f(e),i=r.resolve,c=r.reject,h=o(function(){var r=s(e.resolve),a=[],o=0,h=1;u(t,function(t){var s=o++,u=!1;h++,n(r,e,t).then(function(t){u||(u=!0,a[s]=t,--h||i(a))},c)}),--h||i(a)});return h.error&&c(h.value),r.promise}})},"3WOZ":function(t,e,r){"use strict";var i=r("PIjx"),n=r("PVqM"),s=r("q64P"),a=r("AE8J"),o=r("oXN0"),u=r("yc0e"),c=function(){},h=o("Reflect","construct"),l=/^\s*(?:class|function)\b/,f=i(l.exec),p=!l.test(c),g=function(t){if(!s(t))return!1;try{return h(c,[],t),!0}catch(t){return!1}},d=function(t){if(!s(t))return!1;switch(a(t)){case"AsyncFunction":case"GeneratorFunction":case"AsyncGeneratorFunction":return!1}try{return p||!!f(l,u(t))}catch(t){return!0}};d.sham=!0,t.exports=!h||n(function(){var t;return g(g.call)||!g(Object)||!g(function(){t=!0})||t})?d:g},"3YiN":function(t,e,r){"use strict";var i=r("MfJZ"),n=Math.max,s=Math.min;t.exports=function(t,e){var r=i(t);return r<0?n(r+e,0):s(r,e)}},"4vPW":function(t,e,r){"use strict";var i=r("vsvG"),n=r("PVqM"),s=r("XLq/").String;t.exports=!!Object.getOwnPropertySymbols&&!n(function(){var t=Symbol("symbol detection");return!s(t)||!(Object(t)instanceof Symbol)||!Symbol.sham&&i&&i<41})},"5aM9":function(t,e,r){"use strict";var i=r("PyKl"),n=r("otaM");t.exports=function(t,e){var r=t[e];return n(r)?void 0:i(r)}},"6Sc5":function(t,e,r){"use strict";var i=r("otaM"),n=TypeError;t.exports=function(t){if(i(t))throw new n("Can't call method on "+t);return t}},"7HxT":function(t,e,r){"use strict";var i=r("6Sc5"),n=Object;t.exports=function(t){return n(i(t))}},"7f7/":function(t,e,r){"use strict";var i=r("RyuZ"),n=r("skRZ"),s=r("J8qN"),a=r("ImkM"),o=r("dIgP"),u=r("EQqk");e.f=i&&!n?Object.defineProperties:function(t,e){a(t);for(var r,i=o(e),n=u(e),c=n.length,h=0;c>h;)s.f(t,r=n[h++],i[r]);return t}},"87/F":function(t,e,r){"use strict";var i=r("BohO"),n=r("J8qN");t.exports=function(t,e,r){return r.get&&i(r.get,e,{getter:!0}),r.set&&i(r.set,e,{setter:!0}),n.f(t,e,r)}},"8GbP":function(t,e,r){"use strict";var i=r("PIjx"),n=r("7HxT"),s=Math.floor,a=i("".charAt),o=i("".replace),u=i("".slice),c=/\$([$&'`]|\d{1,2}|<[^>]*>)/g,h=/\$([$&'`]|\d{1,2})/g;t.exports=function(t,e,r,i,l,f){var p=r+t.length,g=i.length,d=h;return void 0!==l&&(l=n(l),d=c),o(f,d,function(n,o){var c;switch(a(o,0)){case"$":return"$";case"&":return t;case"`":return u(e,0,r);case"'":return u(e,p);case"<":c=l[u(o,1,-1)];break;default:var h=+o;if(0===h)return n;if(h>g){var f=s(h/10);return 0===f?n:f<=g?void 0===i[f-1]?a(o,1):i[f-1]+a(o,1):n}c=i[h-1]}return void 0===c?"":c})}},"8irG":function(t,e,r){"use strict";var i=r("FSu7"),n=String,s=TypeError;t.exports=function(t){if(i(t))return t;throw new s("Can't set "+n(t)+" as a prototype")}},"8lri":function(t,e,r){"use strict";var i=r("XLq/");t.exports=i.Promise},AE8J:function(t,e,r){"use strict";var i=r("rsXy"),n=r("q64P"),s=r("YrDN"),a=r("wwku")("toStringTag"),o=Object,u="Arguments"===s(function(){return arguments}());t.exports=i?s:function(t){var e,r,i;return void 0===t?"Undefined":null===t?"Null":"string"==typeof(r=function(t,e){try{return t[e]}catch(t){}}(e=o(t),a))?r:u?s(e):"Object"===(i=s(e))&&n(e.callee)?"Arguments":i}},ATld:function(t,e,r){"use strict";t.exports=function(t){try{return{error:!1,value:t()}}catch(t){return{error:!0,value:t}}}},B0J4:function(t,e,r){"use strict";var i=r("Ly3a"),n=r("cJVG"),s=r("ZswV"),a=r("edI9"),o=RegExp.prototype;t.exports=function(t){var e=t.flags;return void 0!==e||"flags"in o||n(t,"flags")||!s(o,t)?e:i(a,t)}},BNX0:function(t,e,r){"use strict";var i=r("PIjx"),n=r("PyKl");t.exports=function(t,e,r){try{return i(n(Object.getOwnPropertyDescriptor(t,e)[r]))}catch(t){}}},BYTi:function(t,e,r){"use strict";var i=r("QB24");t.exports=/web0s(?!.*chrome)/i.test(i)},BgVa:function(t,e,r){"use strict";var i=r("EfZq")("span").classList,n=i&&i.constructor&&i.constructor.prototype;t.exports=n===Object.prototype?void 0:n},BlZm:function(t,e,r){"use strict";var i=r("PVqM"),n=r("XLq/").RegExp;t.exports=i(function(){var t=n(".","s");return!(t.dotAll&&t.test("\n")&&"s"===t.flags)})},BohO:function(t,e,r){"use strict";var i=r("PIjx"),n=r("PVqM"),s=r("q64P"),a=r("cJVG"),o=r("RyuZ"),u=r("Y0S7").CONFIGURABLE,c=r("yc0e"),h=r("0Qg9"),l=h.enforce,f=h.get,p=String,g=Object.defineProperty,d=i("".slice),v=i("".replace),y=i([].join),m=o&&!n(function(){return 8!==g(function(){},"length",{value:8}).length}),x=String(String).split("String"),b=t.exports=function(t,e,r){"Symbol("===d(p(e),0,7)&&(e="["+v(p(e),/^Symbol\(([^)]*)\).*$/,"$1")+"]"),r&&r.getter&&(e="get "+e),r&&r.setter&&(e="set "+e),(!a(t,"name")||u&&t.name!==e)&&(o?g(t,"name",{value:e,configurable:!0}):t.name=e),m&&r&&a(r,"arity")&&t.length!==r.arity&&g(t,"length",{value:r.arity});try{r&&a(r,"constructor")&&r.constructor?o&&g(t,"prototype",{writable:!1}):t.prototype&&(t.prototype=void 0)}catch(t){}var i=l(t);return a(i,"source")||(i.source=y(x,"string"==typeof e?e:"")),t};Function.prototype.toString=b(function(){return s(this)&&f(this).source||c(this)},"toString")},BvRB:function(t,e,r){"use strict";var i=r("QB24");t.exports=/ipad|iphone|ipod/i.test(i)&&"undefined"!=typeof Pebble},ENN0:function(t,e,r){"use strict";var i,n=r("2xqn"),s=r("QmdU"),a=r("TkC3").f,o=r("tLaq"),u=r("bzwA"),c=r("r48G"),h=r("6Sc5"),l=r("TZjE"),f=r("0nPW"),p=s("".slice),g=Math.min,d=l("endsWith");n({target:"String",proto:!0,forced:!!(f||d||(i=a(String.prototype,"endsWith"),!i||i.writable))&&!d},{endsWith:function(t){var e=u(h(this));c(t);var r=arguments.length>1?arguments[1]:void 0,i=e.length,n=void 0===r?i:g(o(r),i),s=u(t);return p(e,n-s.length,n)===s}})},EQqk:function(t,e,r){"use strict";var i=r("pe4O"),n=r("jZ4S");t.exports=Object.keys||function(t){return i(t,n)}},EfZq:function(t,e,r){"use strict";var i=r("XLq/"),n=r("l8ma"),s=i.document,a=n(s)&&n(s.createElement);t.exports=function(t){return a?s.createElement(t):{}}},"F6+f":function(t,e,r){"use strict";var i=r("2xqn"),n=r("PIjx"),s=r("r48G"),a=r("6Sc5"),o=r("bzwA"),u=r("TZjE"),c=n("".indexOf);i({target:"String",proto:!0,forced:!u("includes")},{includes:function(t){return!!~c(o(a(this)),o(s(t)),arguments.length>1?arguments[1]:void 0)}})},FSu7:function(t,e,r){"use strict";var i=r("l8ma");t.exports=function(t){return i(t)||null===t}},Fk9C:function(t,e,r){"use strict";var i,n=r("2xqn"),s=r("QmdU"),a=r("TkC3").f,o=r("tLaq"),u=r("bzwA"),c=r("r48G"),h=r("6Sc5"),l=r("TZjE"),f=r("0nPW"),p=s("".slice),g=Math.min,d=l("startsWith");n({target:"String",proto:!0,forced:!!(f||d||(i=a(String.prototype,"startsWith"),!i||i.writable))&&!d},{startsWith:function(t){var e=u(h(this));c(t);var r=o(g(arguments.length>1?arguments[1]:void 0,e.length)),i=u(t);return p(e,r,r+i.length)===i}})},G40D:function(t,e,r){"use strict";var i=r("2xqn"),n=r("QmdU"),s=r("eSjb").indexOf,a=r("c4y/"),o=n([].indexOf),u=!!o&&1/o([1],1,-0)<0;i({target:"Array",proto:!0,forced:u||!a("indexOf")},{indexOf:function(t){var e=arguments.length>1?arguments[1]:void 0;return u?o(this,t,e)||0:s(this,t,e)}})},G8u6:function(t,e,r){"use strict";var i=r("oXN0"),n=r("PIjx"),s=r("/ACy"),a=r("hkNL"),o=r("ImkM"),u=n([].concat);t.exports=i("Reflect","ownKeys")||function(t){var e=s.f(o(t)),r=a.f;return r?u(e,r(t)):e}},GYF4:function(t,e,r){var i=r("aqOn");t.exports=function(t,e,r){return(e=i(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t},t.exports.__esModule=!0,t.exports.default=t.exports},GjT1:function(t,e,r){"use strict";var i=r("cu3t"),n=r("Ly3a"),s=r("PIjx"),a=r("kfsg"),o=r("PVqM"),u=r("ImkM"),c=r("q64P"),h=r("otaM"),l=r("MfJZ"),f=r("tLaq"),p=r("bzwA"),g=r("6Sc5"),d=r("hAT3"),v=r("5aM9"),y=r("8GbP"),m=r("WbTk"),x=r("wwku")("replace"),b=Math.max,w=Math.min,S=s([].concat),P=s([].push),T=s("".indexOf),O=s("".slice),A="$0"==="a".replace(/./,"$0"),E=!!/./[x]&&""===/./[x]("a","$0");a("replace",function(t,e,r){var s=E?"$":"$0";return[function(t,r){var i=g(this),s=h(t)?void 0:v(t,x);return s?n(s,t,i,r):n(e,p(i),t,r)},function(t,n){var a=u(this),o=p(t);if("string"==typeof n&&-1===T(n,s)&&-1===T(n,"$<")){var h=r(e,a,o,n);if(h.done)return h.value}var g=c(n);g||(n=p(n));var v,x=a.global;x&&(v=a.unicode,a.lastIndex=0);for(var A,E=[];null!==(A=m(a,o))&&(P(E,A),x);){""===p(A[0])&&(a.lastIndex=d(o,f(a.lastIndex),v))}for(var C,M="",N=0,V=0;V<E.length;V++){for(var R,I=p((A=E[V])[0]),k=b(w(l(A.index),o.length),0),_=[],L=1;L<A.length;L++)P(_,void 0===(C=A[L])?C:String(C));var D=A.groups;if(g){var j=S([I],_,k,o);void 0!==D&&P(j,D),R=p(i(n,void 0,j))}else R=y(I,o,k,_,D,n);k>=N&&(M+=O(o,N,k)+R,N=k+I.length)}return M+O(o,N)}]},!!o(function(){var t=/./;return t.exec=function(){var t=[];return t.groups={a:"7"},t},"7"!=="".replace(t,"$<a>")})||!A||E)},"Hq/V":function(t,e,r){"use strict";var i=r("Y0S7").PROPER,n=r("LlCP"),s=r("ImkM"),a=r("bzwA"),o=r("PVqM"),u=r("B0J4"),c=RegExp.prototype,h=c.toString,l=o(function(){return"/a/b"!==h.call({source:"a",flags:"b"})}),f=i&&"toString"!==h.name;(l||f)&&n(c,"toString",function(){var t=s(this);return"/"+a(t.source)+"/"+a(u(t))},{unsafe:!0})},HqZZ:function(t,e,r){"use strict";var i=r("2xqn"),n=r("Ly3a"),s=r("PyKl"),a=r("MxYK"),o=r("ATld"),u=r("tlqM");i({target:"Promise",stat:!0,forced:r("aeYa")},{race:function(t){var e=this,r=a.f(e),i=r.reject,c=o(function(){var a=s(e.resolve);u(t,function(t){n(a,e,t).then(r.resolve,i)})});return c.error&&i(c.value),r.promise}})},"I8+V":function(t,e,r){"use strict";var i=r("XLq/"),n=Object.defineProperty;t.exports=function(t,e){try{n(i,t,{value:e,configurable:!0,writable:!0})}catch(r){i[t]=e}return e}},IGON:function(t,e,r){"use strict";var i,n=r("ImkM"),s=r("7f7/"),a=r("jZ4S"),o=r("OV1V"),u=r("fOmt"),c=r("EfZq"),h=r("upx8")("IE_PROTO"),l=function(){},f=function(t){return"<script>"+t+"<\/script>"},p=function(t){t.write(f("")),t.close();var e=t.parentWindow.Object;return t=null,e},g=function(){try{i=new ActiveXObject("htmlfile")}catch(t){}var t,e;g="undefined"!=typeof document?document.domain&&i?p(i):((e=c("iframe")).style.display="none",u.appendChild(e),e.src=String("javascript:"),(t=e.contentWindow.document).open(),t.write(f("document.F=Object")),t.close(),t.F):p(i);for(var r=a.length;r--;)delete g.prototype[a[r]];return g()};o[h]=!0,t.exports=Object.create||function(t,e){var r;return null!==t?(l.prototype=n(t),r=new l,l.prototype=null,r[h]=t):r=g(),void 0===e?r:s.f(r,e)}},IOYf:function(t,e,r){"use strict";var i=function(){this.head=null,this.tail=null};i.prototype={add:function(t){var e={item:t,next:null},r=this.tail;r?r.next=e:this.head=e,this.tail=e},get:function(){var t=this.head;if(t)return null===(this.head=t.next)&&(this.tail=null),t.item}},t.exports=i},IU5P:function(t,e,r){"use strict";var i=r("XLq/"),n=r("8lri"),s=r("q64P"),a=r("TLw8"),o=r("yc0e"),u=r("wwku"),c=r("iuoH"),h=r("0nPW"),l=r("vsvG"),f=n&&n.prototype,p=u("species"),g=!1,d=s(i.PromiseRejectionEvent),v=a("Promise",function(){var t=o(n),e=t!==String(n);if(!e&&66===l)return!0;if(h&&(!f.catch||!f.finally))return!0;if(!l||l<51||!/native code/.test(t)){var r=new n(function(t){t(1)}),i=function(t){t(function(){},function(){})};if((r.constructor={})[p]=i,!(g=r.then(function(){})instanceof i))return!0}return!(e||"BROWSER"!==c&&"DENO"!==c||d)});t.exports={CONSTRUCTOR:v,REJECTION_EVENT:d,SUBCLASSING:g}},ImkM:function(t,e,r){"use strict";var i=r("l8ma"),n=String,s=TypeError;t.exports=function(t){if(i(t))return t;throw new s(n(t)+" is not an object")}},InaG:function(t,e,r){"use strict";var i=r("AE8J"),n=r("5aM9"),s=r("otaM"),a=r("vuRZ"),o=r("wwku")("iterator");t.exports=function(t){if(!s(t))return n(t,o)||n(t,"@@iterator")||a[i(t)]}},J8qN:function(t,e,r){"use strict";var i=r("RyuZ"),n=r("h1tM"),s=r("skRZ"),a=r("ImkM"),o=r("WRVK"),u=TypeError,c=Object.defineProperty,h=Object.getOwnPropertyDescriptor;e.f=i?s?function(t,e,r){if(a(t),e=o(e),a(r),"function"==typeof t&&"prototype"===e&&"value"in r&&"writable"in r&&!r.writable){var i=h(t,e);i&&i.writable&&(t[e]=r.value,r={configurable:"configurable"in r?r.configurable:i.configurable,enumerable:"enumerable"in r?r.enumerable:i.enumerable,writable:!1})}return c(t,e,r)}:c:function(t,e,r){if(a(t),e=o(e),a(r),n)try{return c(t,e,r)}catch(t){}if("get"in r||"set"in r)throw new u("Accessors not supported");return"value"in r&&(t[e]=r.value),t}},KPmO:function(t,e,r){"use strict";var i=r("PIjx"),n=r("6Sc5"),s=r("bzwA"),a=r("NdpH"),o=i("".replace),u=RegExp("^["+a+"]+"),c=RegExp("(^|[^"+a+"])["+a+"]+$"),h=function(t){return function(e){var r=s(n(e));return 1&t&&(r=o(r,u,"")),2&t&&(r=o(r,c,"$1")),r}};t.exports={start:h(1),end:h(2),trim:h(3)}},"Ka/V":function(t,e,r){"use strict";var i=r("wwku"),n=r("IGON"),s=r("J8qN").f,a=i("unscopables"),o=Array.prototype;void 0===o[a]&&s(o,a,{configurable:!0,value:n(null)}),t.exports=function(t){o[a][t]=!0}},LOdL:function(t,e){function r(t,e,r,i,n,s,a){try{var o=t[s](a),u=o.value}catch(t){return void r(t)}o.done?e(u):Promise.resolve(u).then(i,n)}t.exports=function(t){return function(){var e=this,i=arguments;return new Promise(function(n,s){var a=t.apply(e,i);function o(t){r(a,n,s,o,u,"next",t)}function u(t){r(a,n,s,o,u,"throw",t)}o(void 0)})}},t.exports.__esModule=!0,t.exports.default=t.exports},LlCP:function(t,e,r){"use strict";var i=r("q64P"),n=r("J8qN"),s=r("BohO"),a=r("I8+V");t.exports=function(t,e,r,o){o||(o={});var u=o.enumerable,c=void 0!==o.name?o.name:e;if(i(r)&&s(r,c,o),o.global)u?t[e]=r:a(e,r);else{try{o.unsafe?t[e]&&(u=!0):delete t[e]}catch(t){}u?t[e]=r:n.f(t,e,{value:r,enumerable:!1,configurable:!o.nonConfigurable,writable:!o.nonWritable})}return t}},Ly3a:function(t,e,r){"use strict";var i=r("1JoV"),n=Function.prototype.call;t.exports=i?n.bind(n):function(){return n.apply(n,arguments)}},MDJ8:function(t,e,r){"use strict";var i=r("PIjx");t.exports=i([].slice)},MExs:function(t,e,r){"use strict";var i=r("dIgP"),n=r("Ka/V"),s=r("vuRZ"),a=r("0Qg9"),o=r("J8qN").f,u=r("i6qB"),c=r("fzJj"),h=r("0nPW"),l=r("RyuZ"),f=a.set,p=a.getterFor("Array Iterator");t.exports=u(Array,"Array",function(t,e){f(this,{type:"Array Iterator",target:i(t),index:0,kind:e})},function(){var t=p(this),e=t.target,r=t.index++;if(!e||r>=e.length)return t.target=null,c(void 0,!0);switch(t.kind){case"keys":return c(r,!1);case"values":return c(e[r],!1)}return c([r,e[r]],!1)},"values");var g=s.Arguments=s.Array;if(n("keys"),n("values"),n("entries"),!h&&l&&"values"!==g.name)try{o(g,"name",{value:"values"})}catch(t){}},MfJZ:function(t,e,r){"use strict";var i=r("T74f");t.exports=function(t){var e=+t;return e!=e||0===e?0:i(e)}},MgMf:function(t,e,r){"use strict";var i=r("Ly3a"),n=r("PIjx"),s=r("kfsg"),a=r("ImkM"),o=r("otaM"),u=r("6Sc5"),c=r("XLW/"),h=r("hAT3"),l=r("tLaq"),f=r("bzwA"),p=r("5aM9"),g=r("WbTk"),d=r("nR2g"),v=r("PVqM"),y=d.UNSUPPORTED_Y,m=Math.min,x=n([].push),b=n("".slice),w=!v(function(){var t=/(?:)/,e=t.exec;t.exec=function(){return e.apply(this,arguments)};var r="ab".split(t);return 2!==r.length||"a"!==r[0]||"b"!==r[1]}),S="c"==="abbc".split(/(b)*/)[1]||4!=="test".split(/(?:)/,-1).length||2!=="ab".split(/(?:ab)*/).length||4!==".".split(/(.?)(.?)/).length||".".split(/()()/).length>1||"".split(/.?/).length;s("split",function(t,e,r){var n="0".split(void 0,0).length?function(t,r){return void 0===t&&0===r?[]:i(e,this,t,r)}:e;return[function(e,r){var s=u(this),a=o(e)?void 0:p(e,t);return a?i(a,e,s,r):i(n,f(s),e,r)},function(t,i){var s=a(this),o=f(t);if(!S){var u=r(n,s,o,i,n!==e);if(u.done)return u.value}var p=c(s,RegExp),d=s.unicode,v=(s.ignoreCase?"i":"")+(s.multiline?"m":"")+(s.unicode?"u":"")+(y?"g":"y"),w=new p(y?"^(?:"+s.source+")":s,v),P=void 0===i?4294967295:i>>>0;if(0===P)return[];if(0===o.length)return null===g(w,o)?[o]:[];for(var T=0,O=0,A=[];O<o.length;){w.lastIndex=y?0:O;var E,C=g(w,y?b(o,O):o);if(null===C||(E=m(l(w.lastIndex+(y?O:0)),o.length))===T)O=h(o,O,d);else{if(x(A,b(o,T,O)),A.length===P)return A;for(var M=1;M<=C.length-1;M++)if(x(A,C[M]),A.length===P)return A;O=T=E}}return x(A,b(o,T)),A}]},S||!w,y)},MxYK:function(t,e,r){"use strict";var i=r("PyKl"),n=TypeError;t.exports.f=function(t){return new function(t){var e,r;this.promise=new t(function(t,i){if(void 0!==e||void 0!==r)throw new n("Bad Promise constructor");e=t,r=i}),this.resolve=i(e),this.reject=i(r)}(t)}},NdpH:function(t,e,r){"use strict";t.exports="\t\n\v\f\r                　\u2028\u2029\ufeff"},"Np/I":function(t,e,r){"use strict";var i=r("oXN0"),n=r("87/F"),s=r("wwku"),a=r("RyuZ"),o=s("species");t.exports=function(t){var e=i(t);a&&e&&!e[o]&&n(e,o,{configurable:!0,get:function(){return this}})}},NtfB:function(t,e,r){"use strict";r("lYqr"),r("33+b"),r("jDMc"),r("HqZZ"),r("qQFl"),r("W7kU")},OV1V:function(t,e,r){"use strict";t.exports={}},OlBB:function(t,e,r){"use strict";var i=r("Y0S7").PROPER,n=r("PVqM"),s=r("NdpH");t.exports=function(t){return n(function(){return!!s[t]()||"​᠎"!=="​᠎"[t]()||i&&s[t].name!==t})}},OsTA:function(t,e,r){"use strict";var i=r("2xqn"),n=r("KPmO").trim;i({target:"String",proto:!0,forced:r("OlBB")("trim")},{trim:function(){return n(this)}})},P09H:function(t,e,r){"use strict";var i=r("Ly3a"),n=r("q64P"),s=r("l8ma"),a=TypeError;t.exports=function(t,e){var r,o;if("string"===e&&n(r=t.toString)&&!s(o=i(r,t)))return o;if(n(r=t.valueOf)&&!s(o=i(r,t)))return o;if("string"!==e&&n(r=t.toString)&&!s(o=i(r,t)))return o;throw new a("Can't convert object to primitive value")}},PIjx:function(t,e,r){"use strict";var i=r("1JoV"),n=Function.prototype,s=n.call,a=i&&n.bind.bind(s,s);t.exports=i?a:function(t){return function(){return s.apply(t,arguments)}}},PVqM:function(t,e,r){"use strict";t.exports=function(t){try{return!!t()}catch(t){return!0}}},Pm7c:function(t,e,r){"use strict";var i=r("l8ma"),n=r("YrDN"),s=r("wwku")("match");t.exports=function(t){var e;return i(t)&&(void 0!==(e=t[s])?!!e:"RegExp"===n(t))}},Prj2:function(t,e,r){"use strict";var i=r("wwku")("iterator"),n=!1;try{var s=0,a={next:function(){return{done:!!s++}},return:function(){n=!0}};a[i]=function(){return this},Array.from(a,function(){throw 2})}catch(t){}t.exports=function(t,e){try{if(!e&&!n)return!1}catch(t){return!1}var r=!1;try{var s={};s[i]=function(){return{next:function(){return{done:r=!0}}}},t(s)}catch(t){}return r}},PyKl:function(t,e,r){"use strict";var i=r("q64P"),n=r("lV/Q"),s=TypeError;t.exports=function(t){if(i(t))return t;throw new s(n(t)+" is not a function")}},QB24:function(t,e,r){"use strict";var i=r("XLq/").navigator,n=i&&i.userAgent;t.exports=n?String(n):""},QmdU:function(t,e,r){"use strict";var i=r("YrDN"),n=r("PIjx");t.exports=function(t){if("Function"===i(t))return n(t)}},Rk4o:function(t,e,r){"use strict";var i=TypeError;t.exports=function(t,e){if(t<e)throw new i("Not enough arguments");return t}},RyoU:function(t,e,r){"use strict";var i=r("QB24");t.exports=/(?:ipad|iphone|ipod).*applewebkit/i.test(i)},RyuZ:function(t,e,r){"use strict";var i=r("PVqM");t.exports=!i(function(){return 7!==Object.defineProperty({},1,{get:function(){return 7}})[1]})},T74f:function(t,e,r){"use strict";var i=Math.ceil,n=Math.floor;t.exports=Math.trunc||function(t){var e=+t;return(e>0?n:i)(e)}},TLw8:function(t,e,r){"use strict";var i=r("PVqM"),n=r("q64P"),s=/#|\.prototype\./,a=function(t,e){var r=u[o(t)];return r===h||r!==c&&(n(e)?i(e):!!e)},o=a.normalize=function(t){return String(t).replace(s,".").toLowerCase()},u=a.data={},c=a.NATIVE="N",h=a.POLYFILL="P";t.exports=a},TU0p:function(t,e,r){"use strict";var i=r("iuoH");t.exports="NODE"===i},TZjE:function(t,e,r){"use strict";var i=r("wwku")("match");t.exports=function(t){var e=/./;try{"/./"[t](e)}catch(r){try{return e[i]=!1,"/./"[t](e)}catch(t){}}return!1}},TkC3:function(t,e,r){"use strict";var i=r("RyuZ"),n=r("Ly3a"),s=r("ToB7"),a=r("XhzM"),o=r("dIgP"),u=r("WRVK"),c=r("cJVG"),h=r("h1tM"),l=Object.getOwnPropertyDescriptor;e.f=i?l:function(t,e){if(t=o(t),e=u(e),h)try{return l(t,e)}catch(t){}if(c(t,e))return a(!n(s.f,t,e),t[e])}},ToB7:function(t,e,r){"use strict";var i={}.propertyIsEnumerable,n=Object.getOwnPropertyDescriptor,s=n&&!i.call({1:2},1);e.f=s?function(t){var e=n(this,t);return!!e&&e.enumerable}:i},TvBJ:function(t,e,r){"use strict";var i=r("tLaq");t.exports=function(t){return i(t.length)}},"VWo+":function(t,e,r){"use strict";function i(t){"@babel/helpers - typeof";return(i="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}r.d(e,"a",function(){return o});var n=[512,512,456,512,328,456,335,512,405,328,271,456,388,335,292,512,454,405,364,328,298,271,496,456,420,388,360,335,312,292,273,512,482,454,428,405,383,364,345,328,312,298,284,271,259,496,475,456,437,420,404,388,374,360,347,335,323,312,302,292,282,273,265,512,497,482,468,454,441,428,417,405,394,383,373,364,354,345,337,328,320,312,305,298,291,284,278,271,265,259,507,496,485,475,465,456,446,437,428,420,412,404,396,388,381,374,367,360,354,347,341,335,329,323,318,312,307,302,297,292,287,282,278,273,269,265,261,512,505,497,489,482,475,468,461,454,447,441,435,428,422,417,411,405,399,394,389,383,378,373,368,364,359,354,350,345,341,337,332,328,324,320,316,312,309,305,301,298,294,291,287,284,281,278,274,271,268,265,262,259,257,507,501,496,491,485,480,475,470,465,460,456,451,446,442,437,433,428,424,420,416,412,408,404,400,396,392,388,385,381,377,374,370,367,363,360,357,354,350,347,344,341,338,335,332,329,326,323,320,318,315,312,310,307,304,302,299,297,294,292,289,287,285,282,280,278,275,273,271,269,267,265,263,261,259],s=[9,11,12,13,13,14,14,15,15,15,15,16,16,16,16,17,17,17,17,17,17,17,18,18,18,18,18,18,18,18,18,19,19,19,19,19,19,19,19,19,19,19,19,19,19,20,20,20,20,20,20,20,20,20,20,20,20,20,20,20,20,20,20,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24];function a(t,e,r,n,s){if("string"==typeof t&&(t=document.getElementById(t)),!(t&&"object"===i(t)&&"getContext"in t))throw new TypeError("Expecting canvas with `getContext` method in processCanvasRGB(A) calls!");var a=t.getContext("2d");try{return a.getImageData(e,r,n,s)}catch(t){throw new Error("unable to access image data: "+t)}}function o(t,e,r,i,o,c){if(!(isNaN(c)||c<1)){c|=0;var h=a(t,e,r,i,o);h=function(t,e,r,i,a,o){for(var c,h=t.data,l=2*o+1,f=i-1,p=a-1,g=o+1,d=g*(g+1)/2,v=new u,y=v,m=1;m<l;m++)y=y.next=new u,m===g&&(c=y);y.next=v;for(var x=null,b=null,w=0,S=0,P=n[o],T=s[o],O=0;O<a;O++){y=v;for(var A=h[S],E=h[S+1],C=h[S+2],M=h[S+3],N=0;N<g;N++)y.r=A,y.g=E,y.b=C,y.a=M,y=y.next;for(var V=0,R=0,I=0,k=0,_=g*A,L=g*E,D=g*C,j=g*M,B=d*A,q=d*E,U=d*C,z=d*M,X=1;X<g;X++){var F=S+((f<X?f:X)<<2),H=h[F],Y=h[F+1],G=h[F+2],W=h[F+3],Q=g-X;B+=(y.r=H)*Q,q+=(y.g=Y)*Q,U+=(y.b=G)*Q,z+=(y.a=W)*Q,V+=H,R+=Y,I+=G,k+=W,y=y.next}x=v,b=c;for(var Z=0;Z<i;Z++){var J=z*P>>>T;if(h[S+3]=J,0!==J){var $=255/J;h[S]=(B*P>>>T)*$,h[S+1]=(q*P>>>T)*$,h[S+2]=(U*P>>>T)*$}else h[S]=h[S+1]=h[S+2]=0;B-=_,q-=L,U-=D,z-=j,_-=x.r,L-=x.g,D-=x.b,j-=x.a;var K=Z+o+1;K=w+(K<f?K:f)<<2,V+=x.r=h[K],R+=x.g=h[K+1],I+=x.b=h[K+2],k+=x.a=h[K+3],B+=V,q+=R,U+=I,z+=k,x=x.next;var tt=b,et=tt.r,rt=tt.g,it=tt.b,nt=tt.a;_+=et,L+=rt,D+=it,j+=nt,V-=et,R-=rt,I-=it,k-=nt,b=b.next,S+=4}w+=i}for(var st=0;st<i;st++){var at=h[S=st<<2],ot=h[S+1],ut=h[S+2],ct=h[S+3],ht=g*at,lt=g*ot,ft=g*ut,pt=g*ct,gt=d*at,dt=d*ot,vt=d*ut,yt=d*ct;y=v;for(var mt=0;mt<g;mt++)y.r=at,y.g=ot,y.b=ut,y.a=ct,y=y.next;for(var xt=i,bt=0,wt=0,St=0,Pt=0,Tt=1;Tt<=o;Tt++){S=xt+st<<2;var Ot=g-Tt;gt+=(y.r=at=h[S])*Ot,dt+=(y.g=ot=h[S+1])*Ot,vt+=(y.b=ut=h[S+2])*Ot,yt+=(y.a=ct=h[S+3])*Ot,Pt+=at,bt+=ot,wt+=ut,St+=ct,y=y.next,Tt<p&&(xt+=i)}S=st,x=v,b=c;for(var At=0;At<a;At++){var Et=S<<2;h[Et+3]=ct=yt*P>>>T,ct>0?(ct=255/ct,h[Et]=(gt*P>>>T)*ct,h[Et+1]=(dt*P>>>T)*ct,h[Et+2]=(vt*P>>>T)*ct):h[Et]=h[Et+1]=h[Et+2]=0,gt-=ht,dt-=lt,vt-=ft,yt-=pt,ht-=x.r,lt-=x.g,ft-=x.b,pt-=x.a,Et=st+((Et=At+g)<p?Et:p)*i<<2,gt+=Pt+=x.r=h[Et],dt+=bt+=x.g=h[Et+1],vt+=wt+=x.b=h[Et+2],yt+=St+=x.a=h[Et+3],x=x.next,ht+=at=b.r,lt+=ot=b.g,ft+=ut=b.b,pt+=ct=b.a,Pt-=at,bt-=ot,wt-=ut,St-=ct,b=b.next,S+=i}}return t}(h,0,0,i,o,c),t.getContext("2d").putImageData(h,e,r)}}var u=function t(){!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t),this.r=0,this.g=0,this.b=0,this.a=0,this.next=null}},VxHz:function(t,e,r){"use strict";var i=r("PyKl"),n=r("7HxT"),s=r("r+vj"),a=r("TvBJ"),o=TypeError,u="Reduce of empty array with no initial value",c=function(t){return function(e,r,c,h){var l=n(e),f=s(l),p=a(l);if(i(r),0===p&&c<2)throw new o(u);var g=t?p-1:0,d=t?-1:1;if(c<2)for(;;){if(g in f){h=f[g],g+=d;break}if(g+=d,t?g<0:p<=g)throw new o(u)}for(;t?g>=0:p>g;g+=d)g in f&&(h=r(h,f[g],g,l));return h}};t.exports={left:c(!1),right:c(!0)}},W7kU:function(t,e,r){"use strict";var i=r("2xqn"),n=r("oXN0"),s=r("0nPW"),a=r("8lri"),o=r("IU5P").CONSTRUCTOR,u=r("ZVMV"),c=n("Promise"),h=s&&!o;i({target:"Promise",stat:!0,forced:s||o},{resolve:function(t){return u(h&&this===c?a:this,t)}})},WRVK:function(t,e,r){"use strict";var i=r("ruWB"),n=r("nkgB");t.exports=function(t){var e=i(t,"string");return n(e)?e:e+""}},WbTk:function(t,e,r){"use strict";var i=r("Ly3a"),n=r("ImkM"),s=r("q64P"),a=r("YrDN"),o=r("1hha"),u=TypeError;t.exports=function(t,e){var r=t.exec;if(s(r)){var c=i(r,t,e);return null!==c&&n(c),c}if("RegExp"===a(t))return i(o,t,e);throw new u("RegExp#exec called on incompatible receiver")}},"XLW/":function(t,e,r){"use strict";var i=r("ImkM"),n=r("b9GD"),s=r("otaM"),a=r("wwku")("species");t.exports=function(t,e){var r,o=i(t).constructor;return void 0===o||s(r=i(o)[a])?e:n(r)}},"XLq/":function(t,e,r){"use strict";(function(e){var r=function(t){return t&&t.Math===Math&&t};t.exports=r("object"==typeof globalThis&&globalThis)||r("object"==typeof window&&window)||r("object"==typeof self&&self)||r("object"==typeof e&&e)||r("object"==typeof this&&this)||function(){return this}()||Function("return this")()}).call(e,r("/Ibk"))},XhzM:function(t,e,r){"use strict";t.exports=function(t,e){return{enumerable:!(1&t),configurable:!(2&t),writable:!(4&t),value:e}}},Y0S7:function(t,e,r){"use strict";var i=r("RyuZ"),n=r("cJVG"),s=Function.prototype,a=i&&Object.getOwnPropertyDescriptor,o=n(s,"name"),u=o&&"something"===function(){}.name,c=o&&(!i||i&&a(s,"name").configurable);t.exports={EXISTS:o,PROPER:u,CONFIGURABLE:c}},YrDN:function(t,e,r){"use strict";var i=r("PIjx"),n=i({}.toString),s=i("".slice);t.exports=function(t){return s(n(t),8,-1)}},ZVMV:function(t,e,r){"use strict";var i=r("ImkM"),n=r("l8ma"),s=r("MxYK");t.exports=function(t,e){if(i(t),n(e)&&e.constructor===t)return e;var r=s.f(t);return(0,r.resolve)(e),r.promise}},ZswV:function(t,e,r){"use strict";var i=r("PIjx");t.exports=i({}.isPrototypeOf)},aNVd:function(t,e,r){"use strict";t.exports={CSSRuleList:0,CSSStyleDeclaration:0,CSSValueList:0,ClientRectList:0,DOMRectList:0,DOMStringList:0,DOMTokenList:1,DataTransferItemList:0,FileList:0,HTMLAllCollection:0,HTMLCollection:0,HTMLFormElement:0,HTMLSelectElement:0,MediaList:0,MimeTypeArray:0,NamedNodeMap:0,NodeList:1,PaintRequestList:0,Plugin:0,PluginArray:0,SVGLengthList:0,SVGNumberList:0,SVGPathSegList:0,SVGPointList:0,SVGStringList:0,SVGTransformList:0,SourceBufferList:0,StyleSheetList:0,TextTrackCueList:0,TextTrackList:0,TouchList:0}},aeYa:function(t,e,r){"use strict";var i=r("8lri"),n=r("Prj2"),s=r("IU5P").CONSTRUCTOR;t.exports=s||!n(function(t){i.all(t).then(void 0,function(){})})},aqOn:function(t,e,r){var i=r("UUgM").default,n=r("/lQz");t.exports=function(t){var e=n(t,"string");return"symbol"==i(e)?e:e+""},t.exports.__esModule=!0,t.exports.default=t.exports},b9GD:function(t,e,r){"use strict";var i=r("3WOZ"),n=r("lV/Q"),s=TypeError;t.exports=function(t){if(i(t))return t;throw new s(n(t)+" is not a constructor")}},bprL:function(t,e,r){"use strict";var i=r("J8qN").f,n=r("cJVG"),s=r("wwku")("toStringTag");t.exports=function(t,e,r){t&&!r&&(t=t.prototype),t&&!n(t,s)&&i(t,s,{configurable:!0,value:e})}},bzwA:function(t,e,r){"use strict";var i=r("AE8J"),n=String;t.exports=function(t){if("Symbol"===i(t))throw new TypeError("Cannot convert a Symbol value to a string");return n(t)}},"c4y/":function(t,e,r){"use strict";var i=r("PVqM");t.exports=function(t,e){var r=[][t];return!!r&&i(function(){r.call(null,e||function(){return 1},1)})}},cFQS:function(t,e,r){"use strict";var i,n,s,a,o=r("XLq/"),u=r("cu3t"),c=r("qn+b"),h=r("q64P"),l=r("cJVG"),f=r("PVqM"),p=r("fOmt"),g=r("MDJ8"),d=r("EfZq"),v=r("Rk4o"),y=r("RyoU"),m=r("TU0p"),x=o.setImmediate,b=o.clearImmediate,w=o.process,S=o.Dispatch,P=o.Function,T=o.MessageChannel,O=o.String,A=0,E={};f(function(){i=o.location});var C=function(t){if(l(E,t)){var e=E[t];delete E[t],e()}},M=function(t){return function(){C(t)}},N=function(t){C(t.data)},V=function(t){o.postMessage(O(t),i.protocol+"//"+i.host)};x&&b||(x=function(t){v(arguments.length,1);var e=h(t)?t:P(t),r=g(arguments,1);return E[++A]=function(){u(e,void 0,r)},n(A),A},b=function(t){delete E[t]},m?n=function(t){w.nextTick(M(t))}:S&&S.now?n=function(t){S.now(M(t))}:T&&!y?(a=(s=new T).port2,s.port1.onmessage=N,n=c(a.postMessage,a)):o.addEventListener&&h(o.postMessage)&&!o.importScripts&&i&&"file:"!==i.protocol&&!f(V)?(n=V,o.addEventListener("message",N,!1)):n="onreadystatechange"in d("script")?function(t){p.appendChild(d("script")).onreadystatechange=function(){p.removeChild(this),C(t)}}:function(t){setTimeout(M(t),0)}),t.exports={set:x,clear:b}},cJVG:function(t,e,r){"use strict";var i=r("PIjx"),n=r("7HxT"),s=i({}.hasOwnProperty);t.exports=Object.hasOwn||function(t,e){return s(n(t),e)}},cU97:function(t,e,r){"use strict";var i=r("PVqM");t.exports=!i(function(){function t(){}return t.prototype.constructor=null,Object.getPrototypeOf(new t)!==t.prototype})},cu3t:function(t,e,r){"use strict";var i=r("1JoV"),n=Function.prototype,s=n.apply,a=n.call;t.exports="object"==typeof Reflect&&Reflect.apply||(i?a.bind(s):function(){return a.apply(s,arguments)})},d9Mx:function(t,e,r){"use strict";var i=r("2xqn"),n=r("VxHz").left,s=r("c4y/"),a=r("vsvG");i({target:"Array",proto:!0,forced:!r("TU0p")&&a>79&&a<83||!s("reduce")},{reduce:function(t){var e=arguments.length;return n(this,t,e,e>1?arguments[1]:void 0)}})},dIgP:function(t,e,r){"use strict";var i=r("r+vj"),n=r("6Sc5");t.exports=function(t){return i(n(t))}},e1h6:function(t,e,r){"use strict";t.exports=function(t,e){try{arguments.length}catch(t){}}},eSjb:function(t,e,r){"use strict";var i=r("dIgP"),n=r("3YiN"),s=r("TvBJ"),a=function(t){return function(e,r,a){var o=i(e),u=s(o);if(0===u)return!t&&-1;var c,h=n(a,u);if(t&&r!=r){for(;u>h;)if((c=o[h++])!=c)return!0}else for(;u>h;h++)if((t||h in o)&&o[h]===r)return t||h||0;return!t&&-1}};t.exports={includes:a(!0),indexOf:a(!1)}},edI9:function(t,e,r){"use strict";var i=r("ImkM");t.exports=function(){var t=i(this),e="";return t.hasIndices&&(e+="d"),t.global&&(e+="g"),t.ignoreCase&&(e+="i"),t.multiline&&(e+="m"),t.dotAll&&(e+="s"),t.unicode&&(e+="u"),t.unicodeSets&&(e+="v"),t.sticky&&(e+="y"),e}},fOmt:function(t,e,r){"use strict";var i=r("oXN0");t.exports=i("document","documentElement")},fyMe:function(t,e,r){"use strict";var i=r("PIjx"),n=0,s=Math.random(),a=i(1..toString);t.exports=function(t){return"Symbol("+(void 0===t?"":t)+")_"+a(++n+s,36)}},fzJj:function(t,e,r){"use strict";t.exports=function(t,e){return{value:t,done:e}}},gvHS:function(t,e,r){"use strict";var i=r("PIjx"),n=r("MfJZ"),s=r("bzwA"),a=r("6Sc5"),o=i("".charAt),u=i("".charCodeAt),c=i("".slice),h=function(t){return function(e,r){var i,h,l=s(a(e)),f=n(r),p=l.length;return f<0||f>=p?t?"":void 0:(i=u(l,f))<55296||i>56319||f+1===p||(h=u(l,f+1))<56320||h>57343?t?o(l,f):i:t?c(l,f,f+2):h-56320+(i-55296<<10)+65536}};t.exports={codeAt:h(!1),charAt:h(!0)}},h1tM:function(t,e,r){"use strict";var i=r("RyuZ"),n=r("PVqM"),s=r("EfZq");t.exports=!i&&!n(function(){return 7!==Object.defineProperty(s("div"),"a",{get:function(){return 7}}).a})},hAT3:function(t,e,r){"use strict";var i=r("gvHS").charAt;t.exports=function(t,e,r){return e+(r?i(t,e).length:1)}},hkNL:function(t,e,r){"use strict";e.f=Object.getOwnPropertySymbols},i6qB:function(t,e,r){"use strict";var i=r("2xqn"),n=r("Ly3a"),s=r("0nPW"),a=r("Y0S7"),o=r("q64P"),u=r("lkBH"),c=r("/5tp"),h=r("ojX3"),l=r("bprL"),f=r("s6mA"),p=r("LlCP"),g=r("wwku"),d=r("vuRZ"),v=r("mqu/"),y=a.PROPER,m=a.CONFIGURABLE,x=v.IteratorPrototype,b=v.BUGGY_SAFARI_ITERATORS,w=g("iterator"),S=function(){return this};t.exports=function(t,e,r,a,g,v,P){u(r,e,a);var T,O,A,E=function(t){if(t===g&&R)return R;if(!b&&t&&t in N)return N[t];switch(t){case"keys":case"values":case"entries":return function(){return new r(this,t)}}return function(){return new r(this)}},C=e+" Iterator",M=!1,N=t.prototype,V=N[w]||N["@@iterator"]||g&&N[g],R=!b&&V||E(g),I="Array"===e&&N.entries||V;if(I&&(T=c(I.call(new t)))!==Object.prototype&&T.next&&(s||c(T)===x||(h?h(T,x):o(T[w])||p(T,w,S)),l(T,C,!0,!0),s&&(d[C]=S)),y&&"values"===g&&V&&"values"!==V.name&&(!s&&m?f(N,"name","values"):(M=!0,R=function(){return n(V,this)})),g)if(O={values:E("values"),keys:v?R:E("keys"),entries:E("entries")},P)for(A in O)!b&&!M&&A in N||p(N,A,O[A]);else i({target:e,proto:!0,forced:b||M},O);return s&&!P||N[w]===R||p(N,w,R,{name:g}),d[e]=R,O}},iuoH:function(t,e,r){"use strict";var i=r("XLq/"),n=r("QB24"),s=r("YrDN"),a=function(t){return n.slice(0,t.length)===t};t.exports=a("Bun/")?"BUN":a("Cloudflare-Workers")?"CLOUDFLARE":a("Deno/")?"DENO":a("Node.js/")?"NODE":i.Bun&&"string"==typeof Bun.version?"BUN":i.Deno&&"object"==typeof Deno.version?"DENO":"process"===s(i.process)?"NODE":i.window&&i.document?"BROWSER":"REST"},jDMc:function(t,e,r){"use strict";var i=r("2xqn"),n=r("0nPW"),s=r("IU5P").CONSTRUCTOR,a=r("8lri"),o=r("oXN0"),u=r("q64P"),c=r("LlCP"),h=a&&a.prototype;if(i({target:"Promise",proto:!0,forced:s,real:!0},{catch:function(t){return this.then(void 0,t)}}),!n&&u(a)){var l=o("Promise").prototype.catch;h.catch!==l&&c(h,"catch",l,{unsafe:!0})}},jZ4S:function(t,e,r){"use strict";t.exports=["constructor","hasOwnProperty","isPrototypeOf","propertyIsEnumerable","toLocaleString","toString","valueOf"]},kfsg:function(t,e,r){"use strict";r("2l35");var i=r("Ly3a"),n=r("LlCP"),s=r("1hha"),a=r("PVqM"),o=r("wwku"),u=r("s6mA"),c=o("species"),h=RegExp.prototype;t.exports=function(t,e,r,l){var f=o(t),p=!a(function(){var e={};return e[f]=function(){return 7},7!==""[t](e)}),g=p&&!a(function(){var e=!1,r=/a/;return"split"===t&&((r={}).constructor={},r.constructor[c]=function(){return r},r.flags="",r[f]=/./[f]),r.exec=function(){return e=!0,null},r[f](""),!e});if(!p||!g||r){var d=/./[f],v=e(f,""[t],function(t,e,r,n,a){var o=e.exec;return o===s||o===h.exec?p&&!a?{done:!0,value:i(d,e,r,n)}:{done:!0,value:i(t,r,e,n)}:{done:!1}});n(String.prototype,t,v[0]),n(h,f,v[1])}l&&u(h[f],"sham",!0)}},l8ma:function(t,e,r){"use strict";var i=r("q64P");t.exports=function(t){return"object"==typeof t?null!==t:i(t)}},"lV/Q":function(t,e,r){"use strict";var i=String;t.exports=function(t){try{return i(t)}catch(t){return"Object"}}},lYqr:function(t,e,r){"use strict";var i,n,s,a=r("2xqn"),o=r("0nPW"),u=r("TU0p"),c=r("XLq/"),h=r("Ly3a"),l=r("LlCP"),f=r("ojX3"),p=r("bprL"),g=r("Np/I"),d=r("PyKl"),v=r("q64P"),y=r("l8ma"),m=r("1j3i"),x=r("XLW/"),b=r("cFQS").set,w=r("qJSV"),S=r("e1h6"),P=r("ATld"),T=r("IOYf"),O=r("0Qg9"),A=r("8lri"),E=r("IU5P"),C=r("MxYK"),M=E.CONSTRUCTOR,N=E.REJECTION_EVENT,V=E.SUBCLASSING,R=O.getterFor("Promise"),I=O.set,k=A&&A.prototype,_=A,L=k,D=c.TypeError,j=c.document,B=c.process,q=C.f,U=q,z=!!(j&&j.createEvent&&c.dispatchEvent),X=function(t){var e;return!(!y(t)||!v(e=t.then))&&e},F=function(t,e){var r,i,n,s=e.value,a=1===e.state,o=a?t.ok:t.fail,u=t.resolve,c=t.reject,l=t.domain;try{o?(a||(2===e.rejection&&Q(e),e.rejection=1),!0===o?r=s:(l&&l.enter(),r=o(s),l&&(l.exit(),n=!0)),r===t.promise?c(new D("Promise-chain cycle")):(i=X(r))?h(i,r,u,c):u(r)):c(s)}catch(t){l&&!n&&l.exit(),c(t)}},H=function(t,e){t.notified||(t.notified=!0,w(function(){for(var r,i=t.reactions;r=i.get();)F(r,t);t.notified=!1,e&&!t.rejection&&G(t)}))},Y=function(t,e,r){var i,n;z?((i=j.createEvent("Event")).promise=e,i.reason=r,i.initEvent(t,!1,!0),c.dispatchEvent(i)):i={promise:e,reason:r},!N&&(n=c["on"+t])?n(i):"unhandledrejection"===t&&S("Unhandled promise rejection",r)},G=function(t){h(b,c,function(){var e,r=t.facade,i=t.value;if(W(t)&&(e=P(function(){u?B.emit("unhandledRejection",i,r):Y("unhandledrejection",r,i)}),t.rejection=u||W(t)?2:1,e.error))throw e.value})},W=function(t){return 1!==t.rejection&&!t.parent},Q=function(t){h(b,c,function(){var e=t.facade;u?B.emit("rejectionHandled",e):Y("rejectionhandled",e,t.value)})},Z=function(t,e,r){return function(i){t(e,i,r)}},J=function(t,e,r){t.done||(t.done=!0,r&&(t=r),t.value=e,t.state=2,H(t,!0))},$=function(t,e,r){if(!t.done){t.done=!0,r&&(t=r);try{if(t.facade===e)throw new D("Promise can't be resolved itself");var i=X(e);i?w(function(){var r={done:!1};try{h(i,e,Z($,r,t),Z(J,r,t))}catch(e){J(r,e,t)}}):(t.value=e,t.state=1,H(t,!1))}catch(e){J({done:!1},e,t)}}};if(M&&(L=(_=function(t){m(this,L),d(t),h(i,this);var e=R(this);try{t(Z($,e),Z(J,e))}catch(t){J(e,t)}}).prototype,(i=function(t){I(this,{type:"Promise",done:!1,notified:!1,parent:!1,reactions:new T,rejection:!1,state:0,value:null})}).prototype=l(L,"then",function(t,e){var r=R(this),i=q(x(this,_));return r.parent=!0,i.ok=!v(t)||t,i.fail=v(e)&&e,i.domain=u?B.domain:void 0,0===r.state?r.reactions.add(i):w(function(){F(i,r)}),i.promise}),n=function(){var t=new i,e=R(t);this.promise=t,this.resolve=Z($,e),this.reject=Z(J,e)},C.f=q=function(t){return t===_||void 0===t?new n(t):U(t)},!o&&v(A)&&k!==Object.prototype)){s=k.then,V||l(k,"then",function(t,e){var r=this;return new _(function(t,e){h(s,r,t,e)}).then(t,e)},{unsafe:!0});try{delete k.constructor}catch(t){}f&&f(k,L)}a({global:!0,constructor:!0,wrap:!0,forced:M},{Promise:_}),p(_,"Promise",!1,!0),g("Promise")},lkBH:function(t,e,r){"use strict";var i=r("mqu/").IteratorPrototype,n=r("IGON"),s=r("XhzM"),a=r("bprL"),o=r("vuRZ"),u=function(){return this};t.exports=function(t,e,r,c){var h=e+" Iterator";return t.prototype=n(i,{next:s(+!c,r)}),a(t,h,!1,!0),o[h]=u,t}},mLvx:function(t,e,r){"use strict";var i=r("XLq/"),n=r("RyuZ"),s=Object.getOwnPropertyDescriptor;t.exports=function(t){if(!n)return i[t];var e=s(i,t);return e&&e.value}},mn8y:function(t,e,r){"use strict";var i=r("XLq/"),n=r("q64P"),s=i.WeakMap;t.exports=n(s)&&/native code/.test(String(s))},"mqu/":function(t,e,r){"use strict";var i,n,s,a=r("PVqM"),o=r("q64P"),u=r("l8ma"),c=r("IGON"),h=r("/5tp"),l=r("LlCP"),f=r("wwku"),p=r("0nPW"),g=f("iterator"),d=!1;[].keys&&("next"in(s=[].keys())?(n=h(h(s)))!==Object.prototype&&(i=n):d=!0),!u(i)||a(function(){var t={};return i[g].call(t)!==t})?i={}:p&&(i=c(i)),o(i[g])||l(i,g,function(){return this}),t.exports={IteratorPrototype:i,BUGGY_SAFARI_ITERATORS:d}},"n+zG":function(t,e,r){"use strict";var i=r("0nPW"),n=r("XLq/"),s=r("I8+V"),a=t.exports=n["__core-js_shared__"]||s("__core-js_shared__",{});(a.versions||(a.versions=[])).push({version:"3.41.0",mode:i?"pure":"global",copyright:"© 2014-2025 Denis Pushkarev (zloirock.ru)",license:"https://github.com/zloirock/core-js/blob/v3.41.0/LICENSE",source:"https://github.com/zloirock/core-js"})},nR2g:function(t,e,r){"use strict";var i=r("PVqM"),n=r("XLq/").RegExp,s=i(function(){var t=n("a","y");return t.lastIndex=2,null!==t.exec("abcd")}),a=s||i(function(){return!n("a","y").sticky}),o=s||i(function(){var t=n("^r","gy");return t.lastIndex=2,null!==t.exec("str")});t.exports={BROKEN_CARET:o,MISSED_STICKY:a,UNSUPPORTED_Y:s}},nkgB:function(t,e,r){"use strict";var i=r("oXN0"),n=r("q64P"),s=r("ZswV"),a=r("vVkC"),o=Object;t.exports=a?function(t){return"symbol"==typeof t}:function(t){var e=i("Symbol");return n(e)&&s(e.prototype,o(t))}},oXN0:function(t,e,r){"use strict";var i=r("XLq/"),n=r("q64P");t.exports=function(t,e){return arguments.length<2?(r=i[t],n(r)?r:void 0):i[t]&&i[t][e];var r}},ojX3:function(t,e,r){"use strict";var i=r("BNX0"),n=r("l8ma"),s=r("6Sc5"),a=r("8irG");t.exports=Object.setPrototypeOf||("__proto__"in{}?function(){var t,e=!1,r={};try{(t=i(Object.prototype,"__proto__","set"))(r,[]),e=r instanceof Array}catch(t){}return function(r,i){return s(r),a(i),n(r)?(e?t(r,i):r.__proto__=i,r):r}}():void 0)},otaM:function(t,e,r){"use strict";t.exports=function(t){return null===t||void 0===t}},pe4O:function(t,e,r){"use strict";var i=r("PIjx"),n=r("cJVG"),s=r("dIgP"),a=r("eSjb").indexOf,o=r("OV1V"),u=i([].push);t.exports=function(t,e){var r,i=s(t),c=0,h=[];for(r in i)!n(o,r)&&n(i,r)&&u(h,r);for(;e.length>c;)n(i,r=e[c++])&&(~a(h,r)||u(h,r));return h}},q64P:function(t,e,r){"use strict";var i="object"==typeof document&&document.all;t.exports=void 0===i&&void 0!==i?function(t){return"function"==typeof t||t===i}:function(t){return"function"==typeof t}},qJSV:function(t,e,r){"use strict";var i,n,s,a,o,u=r("XLq/"),c=r("mLvx"),h=r("qn+b"),l=r("cFQS").set,f=r("IOYf"),p=r("RyoU"),g=r("BvRB"),d=r("BYTi"),v=r("TU0p"),y=u.MutationObserver||u.WebKitMutationObserver,m=u.document,x=u.process,b=u.Promise,w=c("queueMicrotask");if(!w){var S=new f,P=function(){var t,e;for(v&&(t=x.domain)&&t.exit();e=S.get();)try{e()}catch(t){throw S.head&&i(),t}t&&t.enter()};p||v||d||!y||!m?!g&&b&&b.resolve?((a=b.resolve(void 0)).constructor=b,o=h(a.then,a),i=function(){o(P)}):v?i=function(){x.nextTick(P)}:(l=h(l,u),i=function(){l(P)}):(n=!0,s=m.createTextNode(""),new y(P).observe(s,{characterData:!0}),i=function(){s.data=n=!n}),w=function(t){S.head||i(),S.add(t)}}t.exports=w},qQFl:function(t,e,r){"use strict";var i=r("2xqn"),n=r("MxYK");i({target:"Promise",stat:!0,forced:r("IU5P").CONSTRUCTOR},{reject:function(t){var e=n.f(this);return(0,e.reject)(t),e.promise}})},"qn+b":function(t,e,r){"use strict";var i=r("QmdU"),n=r("PyKl"),s=r("1JoV"),a=i(i.bind);t.exports=function(t,e){return n(t),void 0===e?t:s?a(t,e):function(){return t.apply(e,arguments)}}},"r+vj":function(t,e,r){"use strict";var i=r("PIjx"),n=r("PVqM"),s=r("YrDN"),a=Object,o=i("".split);t.exports=n(function(){return!a("z").propertyIsEnumerable(0)})?function(t){return"String"===s(t)?o(t,""):a(t)}:a},r48G:function(t,e,r){"use strict";var i=r("Pm7c"),n=TypeError;t.exports=function(t){if(i(t))throw new n("The method doesn't accept regular expressions");return t}},rHj2:function(t,e,r){"use strict";var i=r("XLq/"),n=r("aNVd"),s=r("BgVa"),a=r("MExs"),o=r("s6mA"),u=r("bprL"),c=r("wwku")("iterator"),h=a.values,l=function(t,e){if(t){if(t[c]!==h)try{o(t,c,h)}catch(e){t[c]=h}if(u(t,e,!0),n[e])for(var r in a)if(t[r]!==a[r])try{o(t,r,a[r])}catch(e){t[r]=a[r]}}};for(var f in n)l(i[f]&&i[f].prototype,f);l(s,"DOMTokenList")},rsXy:function(t,e,r){"use strict";var i={};i[r("wwku")("toStringTag")]="z",t.exports="[object z]"===String(i)},ruWB:function(t,e,r){"use strict";var i=r("Ly3a"),n=r("l8ma"),s=r("nkgB"),a=r("5aM9"),o=r("P09H"),u=r("wwku"),c=TypeError,h=u("toPrimitive");t.exports=function(t,e){if(!n(t)||s(t))return t;var r,u=a(t,h);if(u){if(void 0===e&&(e="default"),r=i(u,t,e),!n(r)||s(r))return r;throw new c("Can't convert object to primitive value")}return void 0===e&&(e="number"),o(t,e)}},s23v:function(t,e,r){"use strict";var i=r("PVqM"),n=r("XLq/").RegExp;t.exports=i(function(){var t=n("(?<a>b)","g");return"b"!==t.exec("b").groups.a||"bc"!=="b".replace(t,"$<a>c")})},s6mA:function(t,e,r){"use strict";var i=r("RyuZ"),n=r("J8qN"),s=r("XhzM");t.exports=i?function(t,e,r){return n.f(t,e,s(1,r))}:function(t,e,r){return t[e]=r,t}},skRZ:function(t,e,r){"use strict";var i=r("RyuZ"),n=r("PVqM");t.exports=i&&n(function(){return 42!==Object.defineProperty(function(){},"prototype",{value:42,writable:!1}).prototype})},tJQ1:function(t,e,r){"use strict";var i=r("2xqn"),n=r("PIjx"),s=r("yv+W"),a=n([].reverse),o=[1,2];i({target:"Array",proto:!0,forced:String(o)===String(o.reverse())},{reverse:function(){return s(this)&&(this.length=this.length),a(this)}})},tLaq:function(t,e,r){"use strict";var i=r("MfJZ"),n=Math.min;t.exports=function(t){var e=i(t);return e>0?n(e,9007199254740991):0}},tlqM:function(t,e,r){"use strict";var i=r("qn+b"),n=r("Ly3a"),s=r("ImkM"),a=r("lV/Q"),o=r("wJpb"),u=r("TvBJ"),c=r("ZswV"),h=r("vt7o"),l=r("InaG"),f=r("/emV"),p=TypeError,g=function(t,e){this.stopped=t,this.result=e},d=g.prototype;t.exports=function(t,e,r){var v,y,m,x,b,w,S,P=r&&r.that,T=!(!r||!r.AS_ENTRIES),O=!(!r||!r.IS_RECORD),A=!(!r||!r.IS_ITERATOR),E=!(!r||!r.INTERRUPTED),C=i(e,P),M=function(t){return v&&f(v,"normal",t),new g(!0,t)},N=function(t){return T?(s(t),E?C(t[0],t[1],M):C(t[0],t[1])):E?C(t,M):C(t)};if(O)v=t.iterator;else if(A)v=t;else{if(!(y=l(t)))throw new p(a(t)+" is not iterable");if(o(y)){for(m=0,x=u(t);x>m;m++)if((b=N(t[m]))&&c(d,b))return b;return new g(!1)}v=h(t,y)}for(w=O?t.next:v.next;!(S=n(w,v)).done;){try{b=N(S.value)}catch(t){f(v,"throw",t)}if("object"==typeof b&&b&&c(d,b))return b}return new g(!1)}},upx8:function(t,e,r){"use strict";var i=r("1QUB"),n=r("fyMe"),s=i("keys");t.exports=function(t){return s[t]||(s[t]=n(t))}},vVkC:function(t,e,r){"use strict";var i=r("4vPW");t.exports=i&&!Symbol.sham&&"symbol"==typeof Symbol.iterator},vsvG:function(t,e,r){"use strict";var i,n,s=r("XLq/"),a=r("QB24"),o=s.process,u=s.Deno,c=o&&o.versions||u&&u.version,h=c&&c.v8;h&&(n=(i=h.split("."))[0]>0&&i[0]<4?1:+(i[0]+i[1])),!n&&a&&(!(i=a.match(/Edge\/(\d+)/))||i[1]>=74)&&(i=a.match(/Chrome\/(\d+)/))&&(n=+i[1]),t.exports=n},vt7o:function(t,e,r){"use strict";var i=r("Ly3a"),n=r("PyKl"),s=r("ImkM"),a=r("lV/Q"),o=r("InaG"),u=TypeError;t.exports=function(t,e){var r=arguments.length<2?o(t):e;if(n(r))return s(i(r,t));throw new u(a(t)+" is not iterable")}},vuRZ:function(t,e,r){"use strict";t.exports={}},wJpb:function(t,e,r){"use strict";var i=r("wwku"),n=r("vuRZ"),s=i("iterator"),a=Array.prototype;t.exports=function(t){return void 0!==t&&(n.Array===t||a[s]===t)}},wwku:function(t,e,r){"use strict";var i=r("XLq/"),n=r("1QUB"),s=r("cJVG"),a=r("fyMe"),o=r("4vPW"),u=r("vVkC"),c=i.Symbol,h=n("wks"),l=u?c.for||c:c&&c.withoutSetter||a;t.exports=function(t){return s(h,t)||(h[t]=o&&s(c,t)?c[t]:l("Symbol."+t)),h[t]}},"x+GU":function(t,e,r){"use strict";r.d(e,"a",function(){return w});
/*! *****************************************************************************
Copyright (c) Microsoft Corporation.

Permission to use, copy, modify, and/or distribute this software for any
purpose with or without fee is hereby granted.

THE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH
REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY
AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,
INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM
LOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR
OTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR
PERFORMANCE OF THIS SOFTWARE.
***************************************************************************** */
var i=function(t,e){return(i=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r])})(t,e)};function n(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");function r(){this.constructor=t}i(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)}function s(t,e){var r=t[0],i=t[1];return[r*Math.cos(e)-i*Math.sin(e),r*Math.sin(e)+i*Math.cos(e)]}function a(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];for(var r=0;r<t.length;r++)if("number"!=typeof t[r])throw new Error("assertNumbers arguments["+r+"] is not a number. "+typeof t[r]+" == typeof "+t[r]);return!0}var o=Math.PI;function u(t,e,r){t.lArcFlag=0===t.lArcFlag?0:1,t.sweepFlag=0===t.sweepFlag?0:1;var i=t.rX,n=t.rY,a=t.x,u=t.y;i=Math.abs(t.rX),n=Math.abs(t.rY);var c=s([(e-a)/2,(r-u)/2],-t.xRot/180*o),h=c[0],l=c[1],f=Math.pow(h,2)/Math.pow(i,2)+Math.pow(l,2)/Math.pow(n,2);1<f&&(i*=Math.sqrt(f),n*=Math.sqrt(f)),t.rX=i,t.rY=n;var p=Math.pow(i,2)*Math.pow(l,2)+Math.pow(n,2)*Math.pow(h,2),g=(t.lArcFlag!==t.sweepFlag?1:-1)*Math.sqrt(Math.max(0,(Math.pow(i,2)*Math.pow(n,2)-p)/p)),d=i*l/n*g,v=-n*h/i*g,y=s([d,v],t.xRot/180*o);t.cX=y[0]+(e+a)/2,t.cY=y[1]+(r+u)/2,t.phi1=Math.atan2((l-v)/n,(h-d)/i),t.phi2=Math.atan2((-l-v)/n,(-h-d)/i),0===t.sweepFlag&&t.phi2>t.phi1&&(t.phi2-=2*o),1===t.sweepFlag&&t.phi2<t.phi1&&(t.phi2+=2*o),t.phi1*=180/o,t.phi2*=180/o}function c(t,e,r){a(t,e,r);var i=t*t+e*e-r*r;if(0>i)return[];if(0===i)return[[t*r/(t*t+e*e),e*r/(t*t+e*e)]];var n=Math.sqrt(i);return[[(t*r+e*n)/(t*t+e*e),(e*r-t*n)/(t*t+e*e)],[(t*r-e*n)/(t*t+e*e),(e*r+t*n)/(t*t+e*e)]]}var h,l=Math.PI/180;function f(t,e,r){return(1-r)*t+r*e}function p(t,e,r,i){return t+Math.cos(i/180*o)*e+Math.sin(i/180*o)*r}function g(t,e,r,i){var n=e-t,s=r-e,a=3*n+3*(i-r)-6*s,o=6*(s-n),u=3*n;return Math.abs(a)<1e-6?[-u/o]:function(t,e,r){void 0===r&&(r=1e-6);var i=t*t/4-u/a;if(i<-r)return[];if(i<=r)return[-t/2];var n=Math.sqrt(i);return[-t/2-n,-t/2+n]}(o/a,0,1e-6)}function d(t,e,r,i,n){var s=1-n;return t*(s*s*s)+e*(3*s*s*n)+r*(3*s*n*n)+i*(n*n*n)}!function(t){function e(){return n(function(t,e,r){return t.relative&&(void 0!==t.x1&&(t.x1+=e),void 0!==t.y1&&(t.y1+=r),void 0!==t.x2&&(t.x2+=e),void 0!==t.y2&&(t.y2+=r),void 0!==t.x&&(t.x+=e),void 0!==t.y&&(t.y+=r),t.relative=!1),t})}function r(){var t=NaN,e=NaN,r=NaN,i=NaN;return n(function(n,s,a){return n.type&w.SMOOTH_CURVE_TO&&(n.type=w.CURVE_TO,t=isNaN(t)?s:t,e=isNaN(e)?a:e,n.x1=n.relative?s-t:2*s-t,n.y1=n.relative?a-e:2*a-e),n.type&w.CURVE_TO?(t=n.relative?s+n.x2:n.x2,e=n.relative?a+n.y2:n.y2):(t=NaN,e=NaN),n.type&w.SMOOTH_QUAD_TO&&(n.type=w.QUAD_TO,r=isNaN(r)?s:r,i=isNaN(i)?a:i,n.x1=n.relative?s-r:2*s-r,n.y1=n.relative?a-i:2*a-i),n.type&w.QUAD_TO?(r=n.relative?s+n.x1:n.x1,i=n.relative?a+n.y1:n.y1):(r=NaN,i=NaN),n})}function i(){var t=NaN,e=NaN;return n(function(r,i,n){if(r.type&w.SMOOTH_QUAD_TO&&(r.type=w.QUAD_TO,t=isNaN(t)?i:t,e=isNaN(e)?n:e,r.x1=r.relative?i-t:2*i-t,r.y1=r.relative?n-e:2*n-e),r.type&w.QUAD_TO){t=r.relative?i+r.x1:r.x1,e=r.relative?n+r.y1:r.y1;var s=r.x1,a=r.y1;r.type=w.CURVE_TO,r.x1=((r.relative?0:i)+2*s)/3,r.y1=((r.relative?0:n)+2*a)/3,r.x2=(r.x+2*s)/3,r.y2=(r.y+2*a)/3}else t=NaN,e=NaN;return r})}function n(t){var e=0,r=0,i=NaN,n=NaN;return function(s){if(isNaN(i)&&!(s.type&w.MOVE_TO))throw new Error("path must start with moveto");var a=t(s,e,r,i,n);return s.type&w.CLOSE_PATH&&(e=i,r=n),void 0!==s.x&&(e=s.relative?e+s.x:s.x),void 0!==s.y&&(r=s.relative?r+s.y:s.y),s.type&w.MOVE_TO&&(i=e,n=r),a}}function o(t,e,r,i,s,o){return a(t,e,r,i,s,o),n(function(n,a,u,c){var h=n.x1,l=n.x2,f=n.relative&&!isNaN(c),p=void 0!==n.x?n.x:f?0:a,g=void 0!==n.y?n.y:f?0:u;function d(t){return t*t}n.type&w.HORIZ_LINE_TO&&0!==e&&(n.type=w.LINE_TO,n.y=n.relative?0:u),n.type&w.VERT_LINE_TO&&0!==r&&(n.type=w.LINE_TO,n.x=n.relative?0:a),void 0!==n.x&&(n.x=n.x*t+g*r+(f?0:s)),void 0!==n.y&&(n.y=p*e+n.y*i+(f?0:o)),void 0!==n.x1&&(n.x1=n.x1*t+n.y1*r+(f?0:s)),void 0!==n.y1&&(n.y1=h*e+n.y1*i+(f?0:o)),void 0!==n.x2&&(n.x2=n.x2*t+n.y2*r+(f?0:s)),void 0!==n.y2&&(n.y2=l*e+n.y2*i+(f?0:o));var v=t*i-e*r;if(void 0!==n.xRot&&(1!==t||0!==e||0!==r||1!==i))if(0===v)delete n.rX,delete n.rY,delete n.xRot,delete n.lArcFlag,delete n.sweepFlag,n.type=w.LINE_TO;else{var y=n.xRot*Math.PI/180,m=Math.sin(y),x=Math.cos(y),b=1/d(n.rX),S=1/d(n.rY),P=d(x)*b+d(m)*S,T=2*m*x*(b-S),O=d(m)*b+d(x)*S,A=P*i*i-T*e*i+O*e*e,E=T*(t*i+e*r)-2*(P*r*i+O*t*e),C=P*r*r-T*t*r+O*t*t,M=(Math.atan2(E,A-C)+Math.PI)%Math.PI/2,N=Math.sin(M),V=Math.cos(M);n.rX=Math.abs(v)/Math.sqrt(A*d(V)+E*N*V+C*d(N)),n.rY=Math.abs(v)/Math.sqrt(A*d(N)-E*N*V+C*d(V)),n.xRot=180*M/Math.PI}return void 0!==n.sweepFlag&&0>v&&(n.sweepFlag=+!n.sweepFlag),n})}t.ROUND=function(t){function e(e){return Math.round(e*t)/t}return void 0===t&&(t=1e13),a(t),function(t){return void 0!==t.x1&&(t.x1=e(t.x1)),void 0!==t.y1&&(t.y1=e(t.y1)),void 0!==t.x2&&(t.x2=e(t.x2)),void 0!==t.y2&&(t.y2=e(t.y2)),void 0!==t.x&&(t.x=e(t.x)),void 0!==t.y&&(t.y=e(t.y)),void 0!==t.rX&&(t.rX=e(t.rX)),void 0!==t.rY&&(t.rY=e(t.rY)),t}},t.TO_ABS=e,t.TO_REL=function(){return n(function(t,e,r){return t.relative||(void 0!==t.x1&&(t.x1-=e),void 0!==t.y1&&(t.y1-=r),void 0!==t.x2&&(t.x2-=e),void 0!==t.y2&&(t.y2-=r),void 0!==t.x&&(t.x-=e),void 0!==t.y&&(t.y-=r),t.relative=!0),t})},t.NORMALIZE_HVZ=function(t,e,r){return void 0===t&&(t=!0),void 0===e&&(e=!0),void 0===r&&(r=!0),n(function(i,n,s,a,o){if(isNaN(a)&&!(i.type&w.MOVE_TO))throw new Error("path must start with moveto");return e&&i.type&w.HORIZ_LINE_TO&&(i.type=w.LINE_TO,i.y=i.relative?0:s),r&&i.type&w.VERT_LINE_TO&&(i.type=w.LINE_TO,i.x=i.relative?0:n),t&&i.type&w.CLOSE_PATH&&(i.type=w.LINE_TO,i.x=i.relative?a-n:a,i.y=i.relative?o-s:o),i.type&w.ARC&&(0===i.rX||0===i.rY)&&(i.type=w.LINE_TO,delete i.rX,delete i.rY,delete i.xRot,delete i.lArcFlag,delete i.sweepFlag),i})},t.NORMALIZE_ST=r,t.QT_TO_C=i,t.INFO=n,t.SANITIZE=function(t){void 0===t&&(t=0),a(t);var e=NaN,r=NaN,i=NaN,s=NaN;return n(function(n,a,o,u,c){var h=Math.abs,l=!1,f=0,p=0;if(n.type&w.SMOOTH_CURVE_TO&&(f=isNaN(e)?0:a-e,p=isNaN(r)?0:o-r),n.type&(w.CURVE_TO|w.SMOOTH_CURVE_TO)?(e=n.relative?a+n.x2:n.x2,r=n.relative?o+n.y2:n.y2):(e=NaN,r=NaN),n.type&w.SMOOTH_QUAD_TO?(i=isNaN(i)?a:2*a-i,s=isNaN(s)?o:2*o-s):n.type&w.QUAD_TO?(i=n.relative?a+n.x1:n.x1,s=n.relative?o+n.y1:n.y2):(i=NaN,s=NaN),n.type&w.LINE_COMMANDS||n.type&w.ARC&&(0===n.rX||0===n.rY||!n.lArcFlag)||n.type&w.CURVE_TO||n.type&w.SMOOTH_CURVE_TO||n.type&w.QUAD_TO||n.type&w.SMOOTH_QUAD_TO){var g=void 0===n.x?0:n.relative?n.x:n.x-a,d=void 0===n.y?0:n.relative?n.y:n.y-o;f=isNaN(i)?void 0===n.x1?f:n.relative?n.x:n.x1-a:i-a,p=isNaN(s)?void 0===n.y1?p:n.relative?n.y:n.y1-o:s-o;var v=void 0===n.x2?0:n.relative?n.x:n.x2-a,y=void 0===n.y2?0:n.relative?n.y:n.y2-o;h(g)<=t&&h(d)<=t&&h(f)<=t&&h(p)<=t&&h(v)<=t&&h(y)<=t&&(l=!0)}return n.type&w.CLOSE_PATH&&h(a-u)<=t&&h(o-c)<=t&&(l=!0),l?[]:n})},t.MATRIX=o,t.ROTATE=function(t,e,r){void 0===e&&(e=0),void 0===r&&(r=0),a(t,e,r);var i=Math.sin(t),n=Math.cos(t);return o(n,i,-i,n,e-e*n+r*i,r-e*i-r*n)},t.TRANSLATE=function(t,e){return void 0===e&&(e=0),a(t,e),o(1,0,0,1,t,e)},t.SCALE=function(t,e){return void 0===e&&(e=t),a(t,e),o(t,0,0,e,0,0)},t.SKEW_X=function(t){return a(t),o(1,0,Math.atan(t),1,0,0)},t.SKEW_Y=function(t){return a(t),o(1,Math.atan(t),0,1,0,0)},t.X_AXIS_SYMMETRY=function(t){return void 0===t&&(t=0),a(t),o(-1,0,0,1,t,0)},t.Y_AXIS_SYMMETRY=function(t){return void 0===t&&(t=0),a(t),o(1,0,0,-1,0,t)},t.A_TO_C=function(){return n(function(t,e,r){return w.ARC===t.type?function(t,e,r){var i,n,a,o;t.cX||u(t,e,r);for(var c=Math.min(t.phi1,t.phi2),h=Math.max(t.phi1,t.phi2)-c,p=Math.ceil(h/90),g=new Array(p),d=e,v=r,y=0;y<p;y++){var m=f(t.phi1,t.phi2,y/p),x=f(t.phi1,t.phi2,(y+1)/p),b=x-m,S=4/3*Math.tan(b*l/4),P=[Math.cos(m*l)-S*Math.sin(m*l),Math.sin(m*l)+S*Math.cos(m*l)],T=P[0],O=P[1],A=[Math.cos(x*l),Math.sin(x*l)],E=A[0],C=A[1],M=[E+S*Math.sin(x*l),C-S*Math.cos(x*l)],N=M[0],V=M[1];g[y]={relative:t.relative,type:w.CURVE_TO};var R=function(e,r){var i=s([e*t.rX,r*t.rY],t.xRot),n=i[0],a=i[1];return[t.cX+n,t.cY+a]};i=R(T,O),g[y].x1=i[0],g[y].y1=i[1],n=R(N,V),g[y].x2=n[0],g[y].y2=n[1],a=R(E,C),g[y].x=a[0],g[y].y=a[1],t.relative&&(g[y].x1-=d,g[y].y1-=v,g[y].x2-=d,g[y].y2-=v,g[y].x-=d,g[y].y-=v),d=(o=[g[y].x,g[y].y])[0],v=o[1]}return g}(t,t.relative?0:e,t.relative?0:r):t})},t.ANNOTATE_ARCS=function(){return n(function(t,e,r){return t.relative&&(e=0,r=0),w.ARC===t.type&&u(t,e,r),t})},t.CLONE=function(){return function(t){var e={};for(var r in t)e[r]=t[r];return e}},t.CALCULATE_BOUNDS=function(){var t=e(),s=i(),a=r(),o=n(function(e,r,i){var n=a(s(t(function(t){var e={};for(var r in t)e[r]=t[r];return e}(e))));function h(t){t>o.maxX&&(o.maxX=t),t<o.minX&&(o.minX=t)}function l(t){t>o.maxY&&(o.maxY=t),t<o.minY&&(o.minY=t)}if(n.type&w.DRAWING_COMMANDS&&(h(r),l(i)),n.type&w.HORIZ_LINE_TO&&h(n.x),n.type&w.VERT_LINE_TO&&l(n.y),n.type&w.LINE_TO&&(h(n.x),l(n.y)),n.type&w.CURVE_TO){h(n.x),l(n.y);for(var f=0,v=g(r,n.x1,n.x2,n.x);f<v.length;f++)0<(I=v[f])&&1>I&&h(d(r,n.x1,n.x2,n.x,I));for(var y=0,m=g(i,n.y1,n.y2,n.y);y<m.length;y++)0<(I=m[y])&&1>I&&l(d(i,n.y1,n.y2,n.y,I))}if(n.type&w.ARC){h(n.x),l(n.y),u(n,r,i);for(var x=n.xRot/180*Math.PI,b=Math.cos(x)*n.rX,S=Math.sin(x)*n.rX,P=-Math.sin(x)*n.rY,T=Math.cos(x)*n.rY,O=n.phi1<n.phi2?[n.phi1,n.phi2]:-180>n.phi2?[n.phi2+360,n.phi1+360]:[n.phi2,n.phi1],A=O[0],E=O[1],C=function(t){var e=t[0],r=t[1],i=180*Math.atan2(r,e)/Math.PI;return i<A?i+360:i},M=0,N=c(P,-b,0).map(C);M<N.length;M++)(I=N[M])>A&&I<E&&h(p(n.cX,b,P,I));for(var V=0,R=c(T,-S,0).map(C);V<R.length;V++){var I;(I=R[V])>A&&I<E&&l(p(n.cY,S,T,I))}}return e});return o.minX=1/0,o.maxX=-1/0,o.minY=1/0,o.maxY=-1/0,o}}(h||(h={}));var v,y=function(){function t(){}return t.prototype.round=function(t){return this.transform(h.ROUND(t))},t.prototype.toAbs=function(){return this.transform(h.TO_ABS())},t.prototype.toRel=function(){return this.transform(h.TO_REL())},t.prototype.normalizeHVZ=function(t,e,r){return this.transform(h.NORMALIZE_HVZ(t,e,r))},t.prototype.normalizeST=function(){return this.transform(h.NORMALIZE_ST())},t.prototype.qtToC=function(){return this.transform(h.QT_TO_C())},t.prototype.aToC=function(){return this.transform(h.A_TO_C())},t.prototype.sanitize=function(t){return this.transform(h.SANITIZE(t))},t.prototype.translate=function(t,e){return this.transform(h.TRANSLATE(t,e))},t.prototype.scale=function(t,e){return this.transform(h.SCALE(t,e))},t.prototype.rotate=function(t,e,r){return this.transform(h.ROTATE(t,e,r))},t.prototype.matrix=function(t,e,r,i,n,s){return this.transform(h.MATRIX(t,e,r,i,n,s))},t.prototype.skewX=function(t){return this.transform(h.SKEW_X(t))},t.prototype.skewY=function(t){return this.transform(h.SKEW_Y(t))},t.prototype.xSymmetry=function(t){return this.transform(h.X_AXIS_SYMMETRY(t))},t.prototype.ySymmetry=function(t){return this.transform(h.Y_AXIS_SYMMETRY(t))},t.prototype.annotateArcs=function(){return this.transform(h.ANNOTATE_ARCS())},t}(),m=function(t){return" "===t||"\t"===t||"\r"===t||"\n"===t},x=function(t){return"0".charCodeAt(0)<=t.charCodeAt(0)&&t.charCodeAt(0)<="9".charCodeAt(0)},b=function(t){function e(){var e=t.call(this)||this;return e.curNumber="",e.curCommandType=-1,e.curCommandRelative=!1,e.canParseCommandOrComma=!0,e.curNumberHasExp=!1,e.curNumberHasExpDigits=!1,e.curNumberHasDecimal=!1,e.curArgs=[],e}return n(e,t),e.prototype.finish=function(t){if(void 0===t&&(t=[]),this.parse(" ",t),0!==this.curArgs.length||!this.canParseCommandOrComma)throw new SyntaxError("Unterminated command at the path end.");return t},e.prototype.parse=function(t,e){var r=this;void 0===e&&(e=[]);for(var i=function(t){e.push(t),r.curArgs.length=0,r.canParseCommandOrComma=!0},n=0;n<t.length;n++){var s=t[n],a=!(this.curCommandType!==w.ARC||3!==this.curArgs.length&&4!==this.curArgs.length||1!==this.curNumber.length||"0"!==this.curNumber&&"1"!==this.curNumber),o=x(s)&&("0"===this.curNumber&&"0"===s||a);if(!x(s)||o)if("e"!==s&&"E"!==s)if("-"!==s&&"+"!==s||!this.curNumberHasExp||this.curNumberHasExpDigits)if("."!==s||this.curNumberHasExp||this.curNumberHasDecimal||a){if(this.curNumber&&-1!==this.curCommandType){var u=Number(this.curNumber);if(isNaN(u))throw new SyntaxError("Invalid number ending at "+n);if(this.curCommandType===w.ARC)if(0===this.curArgs.length||1===this.curArgs.length){if(0>u)throw new SyntaxError('Expected positive number, got "'+u+'" at index "'+n+'"')}else if((3===this.curArgs.length||4===this.curArgs.length)&&"0"!==this.curNumber&&"1"!==this.curNumber)throw new SyntaxError('Expected a flag, got "'+this.curNumber+'" at index "'+n+'"');this.curArgs.push(u),this.curArgs.length===S[this.curCommandType]&&(w.HORIZ_LINE_TO===this.curCommandType?i({type:w.HORIZ_LINE_TO,relative:this.curCommandRelative,x:u}):w.VERT_LINE_TO===this.curCommandType?i({type:w.VERT_LINE_TO,relative:this.curCommandRelative,y:u}):this.curCommandType===w.MOVE_TO||this.curCommandType===w.LINE_TO||this.curCommandType===w.SMOOTH_QUAD_TO?(i({type:this.curCommandType,relative:this.curCommandRelative,x:this.curArgs[0],y:this.curArgs[1]}),w.MOVE_TO===this.curCommandType&&(this.curCommandType=w.LINE_TO)):this.curCommandType===w.CURVE_TO?i({type:w.CURVE_TO,relative:this.curCommandRelative,x1:this.curArgs[0],y1:this.curArgs[1],x2:this.curArgs[2],y2:this.curArgs[3],x:this.curArgs[4],y:this.curArgs[5]}):this.curCommandType===w.SMOOTH_CURVE_TO?i({type:w.SMOOTH_CURVE_TO,relative:this.curCommandRelative,x2:this.curArgs[0],y2:this.curArgs[1],x:this.curArgs[2],y:this.curArgs[3]}):this.curCommandType===w.QUAD_TO?i({type:w.QUAD_TO,relative:this.curCommandRelative,x1:this.curArgs[0],y1:this.curArgs[1],x:this.curArgs[2],y:this.curArgs[3]}):this.curCommandType===w.ARC&&i({type:w.ARC,relative:this.curCommandRelative,rX:this.curArgs[0],rY:this.curArgs[1],xRot:this.curArgs[2],lArcFlag:this.curArgs[3],sweepFlag:this.curArgs[4],x:this.curArgs[5],y:this.curArgs[6]})),this.curNumber="",this.curNumberHasExpDigits=!1,this.curNumberHasExp=!1,this.curNumberHasDecimal=!1,this.canParseCommandOrComma=!0}if(!m(s))if(","===s&&this.canParseCommandOrComma)this.canParseCommandOrComma=!1;else if("+"!==s&&"-"!==s&&"."!==s)if(o)this.curNumber=s,this.curNumberHasDecimal=!1;else{if(0!==this.curArgs.length)throw new SyntaxError("Unterminated command at index "+n+".");if(!this.canParseCommandOrComma)throw new SyntaxError('Unexpected character "'+s+'" at index '+n+". Command cannot follow comma");if(this.canParseCommandOrComma=!1,"z"!==s&&"Z"!==s)if("h"===s||"H"===s)this.curCommandType=w.HORIZ_LINE_TO,this.curCommandRelative="h"===s;else if("v"===s||"V"===s)this.curCommandType=w.VERT_LINE_TO,this.curCommandRelative="v"===s;else if("m"===s||"M"===s)this.curCommandType=w.MOVE_TO,this.curCommandRelative="m"===s;else if("l"===s||"L"===s)this.curCommandType=w.LINE_TO,this.curCommandRelative="l"===s;else if("c"===s||"C"===s)this.curCommandType=w.CURVE_TO,this.curCommandRelative="c"===s;else if("s"===s||"S"===s)this.curCommandType=w.SMOOTH_CURVE_TO,this.curCommandRelative="s"===s;else if("q"===s||"Q"===s)this.curCommandType=w.QUAD_TO,this.curCommandRelative="q"===s;else if("t"===s||"T"===s)this.curCommandType=w.SMOOTH_QUAD_TO,this.curCommandRelative="t"===s;else{if("a"!==s&&"A"!==s)throw new SyntaxError('Unexpected character "'+s+'" at index '+n+".");this.curCommandType=w.ARC,this.curCommandRelative="a"===s}else e.push({type:w.CLOSE_PATH}),this.canParseCommandOrComma=!0,this.curCommandType=-1}else this.curNumber=s,this.curNumberHasDecimal="."===s}else this.curNumber+=s,this.curNumberHasDecimal=!0;else this.curNumber+=s;else this.curNumber+=s,this.curNumberHasExp=!0;else this.curNumber+=s,this.curNumberHasExpDigits=this.curNumberHasExp}return e},e.prototype.transform=function(t){return Object.create(this,{parse:{value:function(e,r){void 0===r&&(r=[]);for(var i=0,n=Object.getPrototypeOf(this).parse.call(this,e);i<n.length;i++){var s=n[i],a=t(s);Array.isArray(a)?r.push.apply(r,a):r.push(a)}return r}}})},e}(y),w=function(t){function e(r){var i=t.call(this)||this;return i.commands="string"==typeof r?e.parse(r):r,i}return n(e,t),e.prototype.encode=function(){return e.encode(this.commands)},e.prototype.getBounds=function(){var t=h.CALCULATE_BOUNDS();return this.transform(t),t},e.prototype.transform=function(t){for(var e=[],r=0,i=this.commands;r<i.length;r++){var n=t(i[r]);Array.isArray(n)?e.push.apply(e,n):e.push(n)}return this.commands=e,this},e.encode=function(t){return function(t){var e="";Array.isArray(t)||(t=[t]);for(var r=0;r<t.length;r++){var i=t[r];if(i.type===w.CLOSE_PATH)e+="z";else if(i.type===w.HORIZ_LINE_TO)e+=(i.relative?"h":"H")+i.x;else if(i.type===w.VERT_LINE_TO)e+=(i.relative?"v":"V")+i.y;else if(i.type===w.MOVE_TO)e+=(i.relative?"m":"M")+i.x+" "+i.y;else if(i.type===w.LINE_TO)e+=(i.relative?"l":"L")+i.x+" "+i.y;else if(i.type===w.CURVE_TO)e+=(i.relative?"c":"C")+i.x1+" "+i.y1+" "+i.x2+" "+i.y2+" "+i.x+" "+i.y;else if(i.type===w.SMOOTH_CURVE_TO)e+=(i.relative?"s":"S")+i.x2+" "+i.y2+" "+i.x+" "+i.y;else if(i.type===w.QUAD_TO)e+=(i.relative?"q":"Q")+i.x1+" "+i.y1+" "+i.x+" "+i.y;else if(i.type===w.SMOOTH_QUAD_TO)e+=(i.relative?"t":"T")+i.x+" "+i.y;else{if(i.type!==w.ARC)throw new Error('Unexpected command type "'+i.type+'" at index '+r+".");e+=(i.relative?"a":"A")+i.rX+" "+i.rY+" "+i.xRot+" "+ +i.lArcFlag+" "+ +i.sweepFlag+" "+i.x+" "+i.y}}return e}(t)},e.parse=function(t){var e=new b,r=[];return e.parse(t,r),e.finish(r),r},e.CLOSE_PATH=1,e.MOVE_TO=2,e.HORIZ_LINE_TO=4,e.VERT_LINE_TO=8,e.LINE_TO=16,e.CURVE_TO=32,e.SMOOTH_CURVE_TO=64,e.QUAD_TO=128,e.SMOOTH_QUAD_TO=256,e.ARC=512,e.LINE_COMMANDS=e.LINE_TO|e.HORIZ_LINE_TO|e.VERT_LINE_TO,e.DRAWING_COMMANDS=e.HORIZ_LINE_TO|e.VERT_LINE_TO|e.LINE_TO|e.CURVE_TO|e.SMOOTH_CURVE_TO|e.QUAD_TO|e.SMOOTH_QUAD_TO|e.ARC,e}(y),S=((v={})[w.MOVE_TO]=2,v[w.LINE_TO]=2,v[w.HORIZ_LINE_TO]=1,v[w.VERT_LINE_TO]=1,v[w.CLOSE_PATH]=0,v[w.QUAD_TO]=4,v[w.SMOOTH_QUAD_TO]=2,v[w.CURVE_TO]=6,v[w.SMOOTH_CURVE_TO]=4,v[w.ARC]=7,v)},yBd6:function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),function(t){r.d(e,"AElement",function(){return te}),r.d(e,"AnimateColorElement",function(){return Wt}),r.d(e,"AnimateElement",function(){return Gt}),r.d(e,"AnimateTransformElement",function(){return Qt}),r.d(e,"BoundingBox",function(){return Tt}),r.d(e,"CB1",function(){return Q}),r.d(e,"CB2",function(){return Z}),r.d(e,"CB3",function(){return J}),r.d(e,"CB4",function(){return $}),r.d(e,"Canvg",function(){return Ve}),r.d(e,"CircleElement",function(){return kt}),r.d(e,"ClipPathElement",function(){return ye}),r.d(e,"DefsElement",function(){return Ut}),r.d(e,"DescElement",function(){return Te}),r.d(e,"Document",function(){return Ce}),r.d(e,"Element",function(){return bt}),r.d(e,"EllipseElement",function(){return _t}),r.d(e,"FeColorMatrixElement",function(){return ge}),r.d(e,"FeCompositeElement",function(){return we}),r.d(e,"FeDropShadowElement",function(){return xe}),r.d(e,"FeGaussianBlurElement",function(){return Se}),r.d(e,"FeMorphologyElement",function(){return be}),r.d(e,"FilterElement",function(){return me}),r.d(e,"Font",function(){return Pt}),r.d(e,"FontElement",function(){return Zt}),r.d(e,"FontFaceElement",function(){return Jt}),r.d(e,"GElement",function(){return zt}),r.d(e,"GlyphElement",function(){return Ct}),r.d(e,"GradientElement",function(){return Xt}),r.d(e,"ImageElement",function(){return se}),r.d(e,"LineElement",function(){return Lt}),r.d(e,"LinearGradientElement",function(){return Ft}),r.d(e,"MarkerElement",function(){return qt}),r.d(e,"MaskElement",function(){return de}),r.d(e,"Matrix",function(){return dt}),r.d(e,"MissingGlyphElement",function(){return $t}),r.d(e,"Mouse",function(){return st}),r.d(e,"PSEUDO_ZERO",function(){return H}),r.d(e,"Parser",function(){return lt}),r.d(e,"PathElement",function(){return Et}),r.d(e,"PathParser",function(){return Ot}),r.d(e,"PatternElement",function(){return Bt}),r.d(e,"Point",function(){return nt}),r.d(e,"PolygonElement",function(){return jt}),r.d(e,"PolylineElement",function(){return Dt}),r.d(e,"Property",function(){return rt}),r.d(e,"QB1",function(){return K}),r.d(e,"QB2",function(){return tt}),r.d(e,"QB3",function(){return et}),r.d(e,"RadialGradientElement",function(){return Ht}),r.d(e,"RectElement",function(){return It}),r.d(e,"RenderedElement",function(){return At}),r.d(e,"Rotate",function(){return pt}),r.d(e,"SVGElement",function(){return Rt}),r.d(e,"SVGFontLoader",function(){return oe}),r.d(e,"Scale",function(){return gt}),r.d(e,"Screen",function(){return ut}),r.d(e,"Skew",function(){return vt}),r.d(e,"SkewX",function(){return yt}),r.d(e,"SkewY",function(){return mt}),r.d(e,"StopElement",function(){return Yt}),r.d(e,"StyleElement",function(){return ue}),r.d(e,"SymbolElement",function(){return ae}),r.d(e,"TRefElement",function(){return Kt}),r.d(e,"TSpanElement",function(){return Nt}),r.d(e,"TextElement",function(){return Mt}),r.d(e,"TextPathElement",function(){return ie}),r.d(e,"TitleElement",function(){return Pe}),r.d(e,"Transform",function(){return xt}),r.d(e,"Translate",function(){return ft}),r.d(e,"UnknownElement",function(){return wt}),r.d(e,"UseElement",function(){return ce}),r.d(e,"ViewPort",function(){return it}),r.d(e,"compressSpaces",function(){return C}),r.d(e,"default",function(){return Ve}),r.d(e,"getSelectorSpecificity",function(){return F}),r.d(e,"normalizeAttributeName",function(){return I}),r.d(e,"normalizeColor",function(){return _}),r.d(e,"parseExternalUrl",function(){return k}),r.d(e,"presets",function(){return E}),r.d(e,"toNumbers",function(){return V}),r.d(e,"trimLeft",function(){return M}),r.d(e,"trimRight",function(){return N}),r.d(e,"vectorMagnitude",function(){return Y}),r.d(e,"vectorsAngle",function(){return W}),r.d(e,"vectorsRatio",function(){return G});var i=r("NtfB"),n=(r.n(i),r("LOdL")),s=r.n(n),a=r("/4Dz"),o=(r.n(a),r("GjT1")),u=(r.n(o),r("Fk9C")),c=(r.n(u),r("MExs")),h=(r.n(c),r("rHj2")),l=(r.n(h),r("GYF4")),f=r.n(l),p=r("d9Mx"),g=(r.n(p),r("ENN0")),d=(r.n(g),r("MgMf")),v=(r.n(d),r("BxnK")),y=r.n(v),m=r("OsTA"),x=(r.n(m),r("yXmV")),b=r.n(x),w=r("G40D"),S=(r.n(w),r("F6+f")),P=(r.n(S),r("tJQ1")),T=(r.n(P),r("x+GU")),O=r("Hq/V"),A=(r.n(O),r("VWo+"));var E=Object.freeze({__proto__:null,offscreen:function(){var{DOMParser:t}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},e={window:null,ignoreAnimation:!0,ignoreMouse:!0,DOMParser:t,createCanvas:(t,e)=>new OffscreenCanvas(t,e),createImage:t=>s()(function*(){var e=yield(yield fetch(t)).blob();return yield createImageBitmap(e)})()};return"undefined"==typeof DOMParser&&void 0!==t||Reflect.deleteProperty(e,"DOMParser"),e},node:function(t){var{DOMParser:e,canvas:r,fetch:i}=t;return{window:null,ignoreAnimation:!0,ignoreMouse:!0,DOMParser:e,fetch:i,createCanvas:r.createCanvas,createImage:r.loadImage}}});function C(t){return t.replace(/(?!\u3000)\s+/gm," ")}function M(t){return t.replace(/^[\n \t]+/,"")}function N(t){return t.replace(/[\n \t]+$/,"")}function V(t){return((t||"").match(/-?(\d+(?:\.\d*(?:[eE][+-]?\d+)?)?|\.\d+)(?=\D|$)/gm)||[]).map(parseFloat)}var R=/^[A-Z-]+$/;function I(t){return R.test(t)?t.toLowerCase():t}function k(t){var e=/url\(('([^']+)'|"([^"]+)"|([^'")]+))\)/.exec(t)||[];return e[2]||e[3]||e[4]}function _(t){if(!t.startsWith("rgb"))return t;var e=3;return t.replace(/\d+(\.\d+)?/g,(t,r)=>e--&&r?String(Math.round(parseFloat(t))):t)}var L=/(\[[^\]]+\])/g,D=/(#[^\s+>~.[:]+)/g,j=/(\.[^\s+>~.[:]+)/g,B=/(::[^\s+>~.[:]+|:first-line|:first-letter|:before|:after)/gi,q=/(:[\w-]+\([^)]*\))/gi,U=/(:[^\s+>~.[:]+)/g,z=/([^\s+>~.[:]+)/g;function X(t,e){var r=e.exec(t);return r?[t.replace(e," "),r.length]:[t,0]}function F(t){var e=[0,0,0],r=t.replace(/:not\(([^)]*)\)/g,"     $1 ").replace(/{[\s\S]*/gm," "),i=0;return[r,i]=X(r,L),e[1]+=i,[r,i]=X(r,D),e[0]+=i,[r,i]=X(r,j),e[1]+=i,[r,i]=X(r,B),e[2]+=i,[r,i]=X(r,q),e[1]+=i,[r,i]=X(r,U),e[1]+=i,r=r.replace(/[*\s+>~]/g," ").replace(/[#.]/g," "),[r,i]=X(r,z),e[2]+=i,e.join("")}var H=1e-8;function Y(t){return Math.sqrt(Math.pow(t[0],2)+Math.pow(t[1],2))}function G(t,e){return(t[0]*e[0]+t[1]*e[1])/(Y(t)*Y(e))}function W(t,e){return(t[0]*e[1]<t[1]*e[0]?-1:1)*Math.acos(G(t,e))}function Q(t){return t*t*t}function Z(t){return 3*t*t*(1-t)}function J(t){return 3*t*(1-t)*(1-t)}function $(t){return(1-t)*(1-t)*(1-t)}function K(t){return t*t}function tt(t){return 2*t*(1-t)}function et(t){return(1-t)*(1-t)}class rt{constructor(t,e,r){this.document=t,this.name=e,this.value=r,this.isNormalizedColor=!1}static empty(t){return new rt(t,"EMPTY","")}split(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:" ",{document:e,name:r}=this;return C(this.getString()).trim().split(t).map(t=>new rt(e,r,t))}hasValue(t){var{value:e}=this;return null!==e&&""!==e&&(t||0!==e)&&void 0!==e}isString(t){var{value:e}=this,r="string"==typeof e;return r&&t?t.test(e):r}isUrlDefinition(){return this.isString(/^url\(/)}isPixels(){if(!this.hasValue())return!1;var t=this.getString();switch(!0){case t.endsWith("px"):case/^[0-9]+$/.test(t):return!0;default:return!1}}setValue(t){return this.value=t,this}getValue(t){return void 0===t||this.hasValue()?this.value:t}getNumber(t){if(!this.hasValue())return void 0===t?0:parseFloat(t);var{value:e}=this,r=parseFloat(e);return this.isString(/%$/)&&(r/=100),r}getString(t){return void 0===t||this.hasValue()?void 0===this.value?"":String(this.value):String(t)}getColor(t){var e=this.getString(t);return this.isNormalizedColor?e:(this.isNormalizedColor=!0,e=_(e),this.value=e,e)}getDpi(){return 96}getRem(){return this.document.rootEmSize}getEm(){return this.document.emSize}getUnits(){return this.getString().replace(/[0-9.-]/g,"")}getPixels(t){var e=arguments.length>1&&void 0!==arguments[1]&&arguments[1];if(!this.hasValue())return 0;var[r,i]="boolean"==typeof t?[void 0,t]:[t],{viewPort:n}=this.document.screen;switch(!0){case this.isString(/vmin$/):return this.getNumber()/100*Math.min(n.computeSize("x"),n.computeSize("y"));case this.isString(/vmax$/):return this.getNumber()/100*Math.max(n.computeSize("x"),n.computeSize("y"));case this.isString(/vw$/):return this.getNumber()/100*n.computeSize("x");case this.isString(/vh$/):return this.getNumber()/100*n.computeSize("y");case this.isString(/rem$/):return this.getNumber()*this.getRem();case this.isString(/em$/):return this.getNumber()*this.getEm();case this.isString(/ex$/):return this.getNumber()*this.getEm()/2;case this.isString(/px$/):return this.getNumber();case this.isString(/pt$/):return this.getNumber()*this.getDpi()*(1/72);case this.isString(/pc$/):return 15*this.getNumber();case this.isString(/cm$/):return this.getNumber()*this.getDpi()/2.54;case this.isString(/mm$/):return this.getNumber()*this.getDpi()/25.4;case this.isString(/in$/):return this.getNumber()*this.getDpi();case this.isString(/%$/)&&i:return this.getNumber()*this.getEm();case this.isString(/%$/):return this.getNumber()*n.computeSize(r);default:var s=this.getNumber();return e&&s<1?s*n.computeSize(r):s}}getMilliseconds(){return this.hasValue()?this.isString(/ms$/)?this.getNumber():1e3*this.getNumber():0}getRadians(){if(!this.hasValue())return 0;switch(!0){case this.isString(/deg$/):return this.getNumber()*(Math.PI/180);case this.isString(/grad$/):return this.getNumber()*(Math.PI/200);case this.isString(/rad$/):return this.getNumber();default:return this.getNumber()*(Math.PI/180)}}getDefinition(){var t=this.getString(),e=/#([^)'"]+)/.exec(t);return e&&(e=e[1]),e||(e=t),this.document.definitions[e]}getFillStyleDefinition(t,e){var r=this.getDefinition();if(!r)return null;if("function"==typeof r.createGradient)return r.createGradient(this.document.ctx,t,e);if("function"==typeof r.createPattern){if(r.getHrefAttribute().hasValue()){var i=r.getAttribute("patternTransform");r=r.getHrefAttribute().getDefinition(),i.hasValue()&&r.getAttribute("patternTransform",!0).setValue(i.value)}return r.createPattern(this.document.ctx,t,e)}return null}getTextBaseline(){return this.hasValue()?rt.textBaselineMapping[this.getString()]:null}addOpacity(t){for(var e=this.getColor(),r=e.length,i=0,n=0;n<r&&(","===e[n]&&i++,3!==i);n++);if(t.hasValue()&&this.isString()&&3!==i){var s=new b.a(e);s.ok&&(s.alpha=t.getNumber(),e=s.toRGBA())}return new rt(this.document,this.name,e)}}rt.textBaselineMapping={baseline:"alphabetic","before-edge":"top","text-before-edge":"top",middle:"middle",central:"middle","after-edge":"bottom","text-after-edge":"bottom",ideographic:"ideographic",alphabetic:"alphabetic",hanging:"hanging",mathematical:"alphabetic"};class it{constructor(){this.viewPorts=[]}clear(){this.viewPorts=[]}setCurrent(t,e){this.viewPorts.push({width:t,height:e})}removeCurrent(){this.viewPorts.pop()}getCurrent(){var{viewPorts:t}=this;return t[t.length-1]}get width(){return this.getCurrent().width}get height(){return this.getCurrent().height}computeSize(t){return"number"==typeof t?t:"x"===t?this.width:"y"===t?this.height:Math.sqrt(Math.pow(this.width,2)+Math.pow(this.height,2))/Math.sqrt(2)}}class nt{constructor(t,e){this.x=t,this.y=e}static parse(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,[r=e,i=e]=V(t);return new nt(r,i)}static parseScale(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:1,[r=e,i=r]=V(t);return new nt(r,i)}static parsePath(t){for(var e=V(t),r=e.length,i=[],n=0;n<r;n+=2)i.push(new nt(e[n],e[n+1]));return i}angleTo(t){return Math.atan2(t.y-this.y,t.x-this.x)}applyTransform(t){var{x:e,y:r}=this,i=e*t[0]+r*t[2]+t[4],n=e*t[1]+r*t[3]+t[5];this.x=i,this.y=n}}class st{constructor(t){this.screen=t,this.working=!1,this.events=[],this.eventElements=[],this.onClick=this.onClick.bind(this),this.onMouseMove=this.onMouseMove.bind(this)}isWorking(){return this.working}start(){if(!this.working){var{screen:t,onClick:e,onMouseMove:r}=this,i=t.ctx.canvas;i.onclick=e,i.onmousemove=r,this.working=!0}}stop(){if(this.working){var t=this.screen.ctx.canvas;this.working=!1,t.onclick=null,t.onmousemove=null}}hasEvents(){return this.working&&this.events.length>0}runEvents(){if(this.working){var{screen:t,events:e,eventElements:r}=this,{style:i}=t.ctx.canvas;i&&(i.cursor=""),e.forEach((t,e)=>{for(var{run:i}=t,n=r[e];n;)i(n),n=n.parent}),this.events=[],this.eventElements=[]}}checkPath(t,e){if(this.working&&e){var{events:r,eventElements:i}=this;r.forEach((r,n)=>{var{x:s,y:a}=r;!i[n]&&e.isPointInPath&&e.isPointInPath(s,a)&&(i[n]=t)})}}checkBoundingBox(t,e){if(this.working&&e){var{events:r,eventElements:i}=this;r.forEach((r,n)=>{var{x:s,y:a}=r;!i[n]&&e.isPointInBox(s,a)&&(i[n]=t)})}}mapXY(t,e){for(var{window:r,ctx:i}=this.screen,n=new nt(t,e),s=i.canvas;s;)n.x-=s.offsetLeft,n.y-=s.offsetTop,s=s.offsetParent;return r.scrollX&&(n.x+=r.scrollX),r.scrollY&&(n.y+=r.scrollY),n}onClick(t){var{x:e,y:r}=this.mapXY(t.clientX,t.clientY);this.events.push({type:"onclick",x:e,y:r,run(t){t.onClick&&t.onClick()}})}onMouseMove(t){var{x:e,y:r}=this.mapXY(t.clientX,t.clientY);this.events.push({type:"onmousemove",x:e,y:r,run(t){t.onMouseMove&&t.onMouseMove()}})}}var at="undefined"!=typeof window?window:null,ot="undefined"!=typeof fetch?fetch.bind(void 0):null;class ut{constructor(t){var{fetch:e=ot,window:r=at}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};this.ctx=t,this.FRAMERATE=30,this.MAX_VIRTUAL_PIXELS=3e4,this.CLIENT_WIDTH=800,this.CLIENT_HEIGHT=600,this.viewPort=new it,this.mouse=new st(this),this.animations=[],this.waits=[],this.frameDuration=0,this.isReadyLock=!1,this.isFirstRender=!0,this.intervalId=null,this.window=r,this.fetch=e}wait(t){this.waits.push(t)}ready(){return this.readyPromise?this.readyPromise:Promise.resolve()}isReady(){if(this.isReadyLock)return!0;var t=this.waits.every(t=>t());return t&&(this.waits=[],this.resolveReady&&this.resolveReady()),this.isReadyLock=t,t}setDefaults(t){t.strokeStyle="rgba(0,0,0,0)",t.lineCap="butt",t.lineJoin="miter",t.miterLimit=4}setViewBox(t){var{document:e,ctx:r,aspectRatio:i,width:n,desiredWidth:s,height:a,desiredHeight:o,minX:u=0,minY:c=0,refX:h,refY:l,clip:f=!1,clipX:p=0,clipY:g=0}=t,d=C(i).replace(/^defer\s/,""),[v,y]=d.split(" "),m=v||"xMidYMid",x=y||"meet",b=n/s,w=a/o,S=Math.min(b,w),P=Math.max(b,w),T=s,O=o;"meet"===x&&(T*=S,O*=S),"slice"===x&&(T*=P,O*=P);var A=new rt(e,"refX",h),E=new rt(e,"refY",l),M=A.hasValue()&&E.hasValue();if(M&&r.translate(-S*A.getPixels("x"),-S*E.getPixels("y")),f){var N=S*p,V=S*g;r.beginPath(),r.moveTo(N,V),r.lineTo(n,V),r.lineTo(n,a),r.lineTo(N,a),r.closePath(),r.clip()}if(!M){var R="meet"===x&&S===w,I="slice"===x&&P===w,k="meet"===x&&S===b,_="slice"===x&&P===b;m.startsWith("xMid")&&(R||I)&&r.translate(n/2-T/2,0),m.endsWith("YMid")&&(k||_)&&r.translate(0,a/2-O/2),m.startsWith("xMax")&&(R||I)&&r.translate(n-T,0),m.endsWith("YMax")&&(k||_)&&r.translate(0,a-O)}switch(!0){case"none"===m:r.scale(b,w);break;case"meet"===x:r.scale(S,S);break;case"slice"===x:r.scale(P,P)}r.translate(-u,-c)}start(t){var{enableRedraw:e=!1,ignoreMouse:r=!1,ignoreAnimation:i=!1,ignoreDimensions:n=!1,ignoreClear:s=!1,forceRedraw:a,scaleWidth:o,scaleHeight:u,offsetX:c,offsetY:h}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},{FRAMERATE:l,mouse:f}=this,p=1e3/l;if(this.frameDuration=p,this.readyPromise=new Promise(t=>{this.resolveReady=t}),this.isReady()&&this.render(t,n,s,o,u,c,h),e){var g=Date.now(),d=g,v=0,m=()=>{g=Date.now(),(v=g-d)>=p&&(d=g-v%p,this.shouldUpdate(i,a)&&(this.render(t,n,s,o,u,c,h),f.runEvents())),this.intervalId=y()(m)};r||f.start(),this.intervalId=y()(m)}}stop(){this.intervalId&&(y.a.cancel(this.intervalId),this.intervalId=null),this.mouse.stop()}shouldUpdate(t,e){if(!t){var{frameDuration:r}=this;if(this.animations.reduce((t,e)=>e.update(r)||t,!1))return!0}return!("function"!=typeof e||!e())||(!(this.isReadyLock||!this.isReady())||!!this.mouse.hasEvents())}render(t,e,r,i,n,s,a){var{CLIENT_WIDTH:o,CLIENT_HEIGHT:u,viewPort:c,ctx:h,isFirstRender:l}=this,f=h.canvas;c.clear(),f.width&&f.height?c.setCurrent(f.width,f.height):c.setCurrent(o,u);var p=t.getStyle("width"),g=t.getStyle("height");!e&&(l||"number"!=typeof i&&"number"!=typeof n)&&(p.hasValue()&&(f.width=p.getPixels("x"),f.style&&(f.style.width="".concat(f.width,"px"))),g.hasValue()&&(f.height=g.getPixels("y"),f.style&&(f.style.height="".concat(f.height,"px"))));var d=f.clientWidth||f.width,v=f.clientHeight||f.height;if(e&&p.hasValue()&&g.hasValue()&&(d=p.getPixels("x"),v=g.getPixels("y")),c.setCurrent(d,v),"number"==typeof s&&t.getAttribute("x",!0).setValue(s),"number"==typeof a&&t.getAttribute("y",!0).setValue(a),"number"==typeof i||"number"==typeof n){var y=V(t.getAttribute("viewBox").getString()),m=0,x=0;if("number"==typeof i){var b=t.getStyle("width");b.hasValue()?m=b.getPixels("x")/i:isNaN(y[2])||(m=y[2]/i)}if("number"==typeof n){var w=t.getStyle("height");w.hasValue()?x=w.getPixels("y")/n:isNaN(y[3])||(x=y[3]/n)}m||(m=x),x||(x=m),t.getAttribute("width",!0).setValue(i),t.getAttribute("height",!0).setValue(n);var S=t.getStyle("transform",!0,!0);S.setValue("".concat(S.getString()," scale(").concat(1/m,", ").concat(1/x,")"))}r||h.clearRect(0,0,d,v),t.render(h),l&&(this.isFirstRender=!1)}}ut.defaultWindow=at,ut.defaultFetch=ot;var{defaultFetch:ct}=ut,ht="undefined"!=typeof DOMParser?DOMParser:null;class lt{constructor(){var{fetch:t=ct,DOMParser:e=ht}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};this.fetch=t,this.DOMParser=e}parse(t){var e=this;return s()(function*(){return t.startsWith("<")?e.parseFromString(t):e.load(t)})()}parseFromString(t){var e=new this.DOMParser;try{return this.checkDocument(e.parseFromString(t,"image/svg+xml"))}catch(r){return this.checkDocument(e.parseFromString(t,"text/xml"))}}checkDocument(t){var e=t.getElementsByTagName("parsererror")[0];if(e)throw new Error(e.textContent);return t}load(t){var e=this;return s()(function*(){var r=yield(yield e.fetch(t)).text();return e.parseFromString(r)})()}}class ft{constructor(t,e){this.type="translate",this.point=null,this.point=nt.parse(e)}apply(t){var{x:e,y:r}=this.point;t.translate(e||0,r||0)}unapply(t){var{x:e,y:r}=this.point;t.translate(-1*e||0,-1*r||0)}applyToPoint(t){var{x:e,y:r}=this.point;t.applyTransform([1,0,0,1,e||0,r||0])}}class pt{constructor(t,e,r){this.type="rotate",this.angle=null,this.originX=null,this.originY=null,this.cx=0,this.cy=0;var i=V(e);this.angle=new rt(t,"angle",i[0]),this.originX=r[0],this.originY=r[1],this.cx=i[1]||0,this.cy=i[2]||0}apply(t){var{cx:e,cy:r,originX:i,originY:n,angle:s}=this,a=e+i.getPixels("x"),o=r+n.getPixels("y");t.translate(a,o),t.rotate(s.getRadians()),t.translate(-a,-o)}unapply(t){var{cx:e,cy:r,originX:i,originY:n,angle:s}=this,a=e+i.getPixels("x"),o=r+n.getPixels("y");t.translate(a,o),t.rotate(-1*s.getRadians()),t.translate(-a,-o)}applyToPoint(t){var{cx:e,cy:r,angle:i}=this,n=i.getRadians();t.applyTransform([1,0,0,1,e||0,r||0]),t.applyTransform([Math.cos(n),Math.sin(n),-Math.sin(n),Math.cos(n),0,0]),t.applyTransform([1,0,0,1,-e||0,-r||0])}}class gt{constructor(t,e,r){this.type="scale",this.scale=null,this.originX=null,this.originY=null;var i=nt.parseScale(e);0!==i.x&&0!==i.y||(i.x=H,i.y=H),this.scale=i,this.originX=r[0],this.originY=r[1]}apply(t){var{scale:{x:e,y:r},originX:i,originY:n}=this,s=i.getPixels("x"),a=n.getPixels("y");t.translate(s,a),t.scale(e,r||e),t.translate(-s,-a)}unapply(t){var{scale:{x:e,y:r},originX:i,originY:n}=this,s=i.getPixels("x"),a=n.getPixels("y");t.translate(s,a),t.scale(1/e,1/r||e),t.translate(-s,-a)}applyToPoint(t){var{x:e,y:r}=this.scale;t.applyTransform([e||0,0,0,r||0,0,0])}}class dt{constructor(t,e,r){this.type="matrix",this.matrix=[],this.originX=null,this.originY=null,this.matrix=V(e),this.originX=r[0],this.originY=r[1]}apply(t){var{originX:e,originY:r,matrix:i}=this,n=e.getPixels("x"),s=r.getPixels("y");t.translate(n,s),t.transform(i[0],i[1],i[2],i[3],i[4],i[5]),t.translate(-n,-s)}unapply(t){var{originX:e,originY:r,matrix:i}=this,n=i[0],s=i[2],a=i[4],o=i[1],u=i[3],c=i[5],h=1/(n*(1*u-0*c)-s*(1*o-0*c)+a*(0*o-0*u)),l=e.getPixels("x"),f=r.getPixels("y");t.translate(l,f),t.transform(h*(1*u-0*c),h*(0*c-1*o),h*(0*a-1*s),h*(1*n-0*a),h*(s*c-a*u),h*(a*o-n*c)),t.translate(-l,-f)}applyToPoint(t){t.applyTransform(this.matrix)}}class vt extends dt{constructor(t,e,r){super(t,e,r),this.type="skew",this.angle=null,this.angle=new rt(t,"angle",e)}}class yt extends vt{constructor(t,e,r){super(t,e,r),this.type="skewX",this.matrix=[1,0,Math.tan(this.angle.getRadians()),1,0,0]}}class mt extends vt{constructor(t,e,r){super(t,e,r),this.type="skewY",this.matrix=[1,Math.tan(this.angle.getRadians()),0,1,0,0]}}class xt{constructor(t,e,r){this.document=t,this.transforms=[],function(t){return C(t).trim().replace(/\)([a-zA-Z])/g,") $1").replace(/\)(\s?,\s?)/g,") ").split(/\s(?=[a-z])/)}(e).forEach(t=>{if("none"!==t){var[e,i]=function(t){var[e,r]=t.split("(");return[e.trim(),r.trim().replace(")","")]}(t),n=xt.transformTypes[e];void 0!==n&&this.transforms.push(new n(this.document,i,r))}})}static fromElement(t,e){var r=e.getStyle("transform",!1,!0),[i,n=i]=e.getStyle("transform-origin",!1,!0).split(),s=[i,n];return r.hasValue()?new xt(t,r.getString(),s):null}apply(t){for(var{transforms:e}=this,r=e.length,i=0;i<r;i++)e[i].apply(t)}unapply(t){for(var{transforms:e}=this,r=e.length-1;r>=0;r--)e[r].unapply(t)}applyToPoint(t){for(var{transforms:e}=this,r=e.length,i=0;i<r;i++)e[i].applyToPoint(t)}}xt.transformTypes={translate:ft,rotate:pt,scale:gt,matrix:dt,skewX:yt,skewY:mt};class bt{constructor(t,e){var r=arguments.length>2&&void 0!==arguments[2]&&arguments[2];if(this.document=t,this.node=e,this.captureTextNodes=r,this.attributes=Object.create(null),this.styles=Object.create(null),this.stylesSpecificity=Object.create(null),this.animationFrozen=!1,this.animationFrozenValue="",this.parent=null,this.children=[],e&&1===e.nodeType){if(Array.from(e.attributes).forEach(e=>{var r=I(e.nodeName);this.attributes[r]=new rt(t,r,e.value)}),this.addStylesFromStyleDefinition(),this.getAttribute("style").hasValue())this.getAttribute("style").getString().split(";").map(t=>t.trim()).forEach(e=>{if(e){var[r,i]=e.split(":").map(t=>t.trim());this.styles[r]=new rt(t,r,i)}});var{definitions:i}=t,n=this.getAttribute("id");n.hasValue()&&(i[n.getString()]||(i[n.getString()]=this)),Array.from(e.childNodes).forEach(e=>{if(1===e.nodeType)this.addChild(e);else if(r&&(3===e.nodeType||4===e.nodeType)){var i=t.createTextNode(e);i.getText().length>0&&this.addChild(i)}})}}getAttribute(t){var e=arguments.length>1&&void 0!==arguments[1]&&arguments[1],r=this.attributes[t];if(!r&&e){var i=new rt(this.document,t,"");return this.attributes[t]=i,i}return r||rt.empty(this.document)}getHrefAttribute(){for(var t in this.attributes)if("href"===t||t.endsWith(":href"))return this.attributes[t];return rt.empty(this.document)}getStyle(t){var e=arguments.length>1&&void 0!==arguments[1]&&arguments[1],r=arguments.length>2&&void 0!==arguments[2]&&arguments[2],i=this.styles[t];if(i)return i;var n=this.getAttribute(t);if(null!==n&&void 0!==n&&n.hasValue())return this.styles[t]=n,n;if(!r){var{parent:s}=this;if(s){var a=s.getStyle(t);if(null!==a&&void 0!==a&&a.hasValue())return a}}if(e){var o=new rt(this.document,t,"");return this.styles[t]=o,o}return i||rt.empty(this.document)}render(t){if("none"!==this.getStyle("display").getString()&&"hidden"!==this.getStyle("visibility").getString()){if(t.save(),this.getStyle("mask").hasValue()){var e=this.getStyle("mask").getDefinition();e&&(this.applyEffects(t),e.apply(t,this))}else if("none"!==this.getStyle("filter").getValue("none")){var r=this.getStyle("filter").getDefinition();r&&(this.applyEffects(t),r.apply(t,this))}else this.setContext(t),this.renderChildren(t),this.clearContext(t);t.restore()}}setContext(t){}applyEffects(t){var e=xt.fromElement(this.document,this);e&&e.apply(t);var r=this.getStyle("clip-path",!1,!0);if(r.hasValue()){var i=r.getDefinition();i&&i.apply(t)}}clearContext(t){}renderChildren(t){this.children.forEach(e=>{e.render(t)})}addChild(t){var e=t instanceof bt?t:this.document.createElement(t);e.parent=this,bt.ignoreChildTypes.includes(e.type)||this.children.push(e)}matchesSelector(t){var e,{node:r}=this;if("function"==typeof r.matches)return r.matches(t);var i=null===(e=r.getAttribute)||void 0===e?void 0:e.call(r,"class");return!(!i||""===i)&&i.split(" ").some(e=>".".concat(e)===t)}addStylesFromStyleDefinition(){var{styles:t,stylesSpecificity:e}=this.document;for(var r in t)if(!r.startsWith("@")&&this.matchesSelector(r)){var i=t[r],n=e[r];if(i)for(var s in i){var a=this.stylesSpecificity[s];void 0===a&&(a="000"),n>=a&&(this.styles[s]=i[s],this.stylesSpecificity[s]=n)}}}removeStyles(t,e){return e.reduce((e,r)=>{var i=t.getStyle(r);if(!i.hasValue())return e;var n=i.getString();return i.setValue(""),[...e,[r,n]]},[])}restoreStyles(t,e){e.forEach(e=>{var[r,i]=e;t.getStyle(r,!0).setValue(i)})}isFirstChild(){var t;return 0===(null===(t=this.parent)||void 0===t?void 0:t.children.indexOf(this))}}bt.ignoreChildTypes=["title"];class wt extends bt{constructor(t,e,r){super(t,e,r)}}function St(t){var e=t.trim();return/^('|")/.test(e)?e:'"'.concat(e,'"')}class Pt{constructor(t,e,r,i,n,s){var a=s?"string"==typeof s?Pt.parse(s):s:{};this.fontFamily=n||a.fontFamily,this.fontSize=i||a.fontSize,this.fontStyle=t||a.fontStyle,this.fontWeight=r||a.fontWeight,this.fontVariant=e||a.fontVariant}static parse(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"",e=arguments.length>1?arguments[1]:void 0,r="",i="",n="",s="",a="",o={fontSize:!1,fontStyle:!1,fontWeight:!1,fontVariant:!1};return C(t).trim().split(" ").forEach(t=>{switch(!0){case!o.fontStyle&&Pt.styles.includes(t):"inherit"!==t&&(r=t),o.fontStyle=!0;break;case!o.fontVariant&&Pt.variants.includes(t):"inherit"!==t&&(i=t),o.fontStyle=!0,o.fontVariant=!0;break;case!o.fontWeight&&Pt.weights.includes(t):"inherit"!==t&&(n=t),o.fontStyle=!0,o.fontVariant=!0,o.fontWeight=!0;break;case!o.fontSize:"inherit"!==t&&([s]=t.split("/")),o.fontStyle=!0,o.fontVariant=!0,o.fontWeight=!0,o.fontSize=!0;break;default:"inherit"!==t&&(a+=t)}}),new Pt(r,i,n,s,a,e)}toString(){return[function(t){if(!t)return"";var e=t.trim().toLowerCase();switch(e){case"normal":case"italic":case"oblique":case"inherit":case"initial":case"unset":return e;default:return/^oblique\s+(-|)\d+deg$/.test(e)?e:""}}(this.fontStyle),this.fontVariant,function(t){if(!t)return"";var e=t.trim().toLowerCase();switch(e){case"normal":case"bold":case"lighter":case"bolder":case"inherit":case"initial":case"unset":return e;default:return/^[\d.]+$/.test(e)?e:""}}(this.fontWeight),this.fontSize,(e=this.fontFamily,void 0===t?e:e.trim().split(",").map(St).join(","))].join(" ").trim();var e}}Pt.styles="normal|italic|oblique|inherit",Pt.variants="normal|small-caps|inherit",Pt.weights="normal|bold|bolder|lighter|100|200|300|400|500|600|700|800|900|inherit";class Tt{constructor(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:Number.NaN,e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:Number.NaN,r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:Number.NaN,i=arguments.length>3&&void 0!==arguments[3]?arguments[3]:Number.NaN;this.x1=t,this.y1=e,this.x2=r,this.y2=i,this.addPoint(t,e),this.addPoint(r,i)}get x(){return this.x1}get y(){return this.y1}get width(){return this.x2-this.x1}get height(){return this.y2-this.y1}addPoint(t,e){void 0!==t&&((isNaN(this.x1)||isNaN(this.x2))&&(this.x1=t,this.x2=t),t<this.x1&&(this.x1=t),t>this.x2&&(this.x2=t)),void 0!==e&&((isNaN(this.y1)||isNaN(this.y2))&&(this.y1=e,this.y2=e),e<this.y1&&(this.y1=e),e>this.y2&&(this.y2=e))}addX(t){this.addPoint(t,null)}addY(t){this.addPoint(null,t)}addBoundingBox(t){if(t){var{x1:e,y1:r,x2:i,y2:n}=t;this.addPoint(e,r),this.addPoint(i,n)}}sumCubic(t,e,r,i,n){return Math.pow(1-t,3)*e+3*Math.pow(1-t,2)*t*r+3*(1-t)*Math.pow(t,2)*i+Math.pow(t,3)*n}bezierCurveAdd(t,e,r,i,n){var s=6*e-12*r+6*i,a=-3*e+9*r-9*i+3*n,o=3*r-3*e;if(0!==a){var u=Math.pow(s,2)-4*o*a;if(!(u<0)){var c=(-s+Math.sqrt(u))/(2*a);0<c&&c<1&&(t?this.addX(this.sumCubic(c,e,r,i,n)):this.addY(this.sumCubic(c,e,r,i,n)));var h=(-s-Math.sqrt(u))/(2*a);0<h&&h<1&&(t?this.addX(this.sumCubic(h,e,r,i,n)):this.addY(this.sumCubic(h,e,r,i,n)))}}else{if(0===s)return;var l=-o/s;0<l&&l<1&&(t?this.addX(this.sumCubic(l,e,r,i,n)):this.addY(this.sumCubic(l,e,r,i,n)))}}addBezierCurve(t,e,r,i,n,s,a,o){this.addPoint(t,e),this.addPoint(a,o),this.bezierCurveAdd(!0,t,r,n,a),this.bezierCurveAdd(!1,e,i,s,o)}addQuadraticCurve(t,e,r,i,n,s){var a=t+2/3*(r-t),o=e+2/3*(i-e),u=a+1/3*(n-t),c=o+1/3*(s-e);this.addBezierCurve(t,e,a,u,o,c,n,s)}isPointInBox(t,e){var{x1:r,y1:i,x2:n,y2:s}=this;return r<=t&&t<=n&&i<=e&&e<=s}}class Ot extends T.a{constructor(t){super(t.replace(/([+\-.])\s+/gm,"$1").replace(/[^MmZzLlHhVvCcSsQqTtAae\d\s.,+-].*/g,"")),this.control=null,this.start=null,this.current=null,this.command=null,this.commands=this.commands,this.i=-1,this.previousCommand=null,this.points=[],this.angles=[]}reset(){this.i=-1,this.command=null,this.previousCommand=null,this.start=new nt(0,0),this.control=new nt(0,0),this.current=new nt(0,0),this.points=[],this.angles=[]}isEnd(){var{i:t,commands:e}=this;return t>=e.length-1}next(){var t=this.commands[++this.i];return this.previousCommand=this.command,this.command=t,t}getPoint(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"x",e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"y",r=new nt(this.command[t],this.command[e]);return this.makeAbsolute(r)}getAsControlPoint(t,e){var r=this.getPoint(t,e);return this.control=r,r}getAsCurrentPoint(t,e){var r=this.getPoint(t,e);return this.current=r,r}getReflectedControlPoint(){var t=this.previousCommand.type;if(t!==T.a.CURVE_TO&&t!==T.a.SMOOTH_CURVE_TO&&t!==T.a.QUAD_TO&&t!==T.a.SMOOTH_QUAD_TO)return this.current;var{current:{x:e,y:r},control:{x:i,y:n}}=this;return new nt(2*e-i,2*r-n)}makeAbsolute(t){if(this.command.relative){var{x:e,y:r}=this.current;t.x+=e,t.y+=r}return t}addMarker(t,e,r){var{points:i,angles:n}=this;r&&n.length>0&&!n[n.length-1]&&(n[n.length-1]=i[i.length-1].angleTo(r)),this.addMarkerAngle(t,e?e.angleTo(t):null)}addMarkerAngle(t,e){this.points.push(t),this.angles.push(e)}getMarkerPoints(){return this.points}getMarkerAngles(){for(var{angles:t}=this,e=t.length,r=0;r<e;r++)if(!t[r])for(var i=r+1;i<e;i++)if(t[i]){t[r]=t[i];break}return t}}class At extends bt{constructor(){super(...arguments),this.modifiedEmSizeStack=!1}calculateOpacity(){for(var t=1,e=this;e;){var r=e.getStyle("opacity",!1,!0);r.hasValue(!0)&&(t*=r.getNumber()),e=e.parent}return t}setContext(t){var e=arguments.length>1&&void 0!==arguments[1]&&arguments[1];if(!e){var r=this.getStyle("fill"),i=this.getStyle("fill-opacity"),n=this.getStyle("stroke"),s=this.getStyle("stroke-opacity");if(r.isUrlDefinition()){var a=r.getFillStyleDefinition(this,i);a&&(t.fillStyle=a)}else if(r.hasValue()){"currentColor"===r.getString()&&r.setValue(this.getStyle("color").getColor());var o=r.getColor();"inherit"!==o&&(t.fillStyle="none"===o?"rgba(0,0,0,0)":o)}if(i.hasValue()){var u=new rt(this.document,"fill",t.fillStyle).addOpacity(i).getColor();t.fillStyle=u}if(n.isUrlDefinition()){var c=n.getFillStyleDefinition(this,s);c&&(t.strokeStyle=c)}else if(n.hasValue()){"currentColor"===n.getString()&&n.setValue(this.getStyle("color").getColor());var h=n.getString();"inherit"!==h&&(t.strokeStyle="none"===h?"rgba(0,0,0,0)":h)}if(s.hasValue()){var l=new rt(this.document,"stroke",t.strokeStyle).addOpacity(s).getString();t.strokeStyle=l}var f=this.getStyle("stroke-width");if(f.hasValue()){var p=f.getPixels();t.lineWidth=p||H}var g=this.getStyle("stroke-linecap"),d=this.getStyle("stroke-linejoin"),v=this.getStyle("stroke-miterlimit"),y=this.getStyle("stroke-dasharray"),m=this.getStyle("stroke-dashoffset");if(g.hasValue()&&(t.lineCap=g.getString()),d.hasValue()&&(t.lineJoin=d.getString()),v.hasValue()&&(t.miterLimit=v.getNumber()),y.hasValue()&&"none"!==y.getString()){var x=V(y.getString());void 0!==t.setLineDash?t.setLineDash(x):void 0!==t.webkitLineDash?t.webkitLineDash=x:void 0===t.mozDash||1===x.length&&0===x[0]||(t.mozDash=x);var b=m.getPixels();void 0!==t.lineDashOffset?t.lineDashOffset=b:void 0!==t.webkitLineDashOffset?t.webkitLineDashOffset=b:void 0!==t.mozDashOffset&&(t.mozDashOffset=b)}}if(this.modifiedEmSizeStack=!1,void 0!==t.font){var w=this.getStyle("font"),S=this.getStyle("font-style"),P=this.getStyle("font-variant"),T=this.getStyle("font-weight"),O=this.getStyle("font-size"),A=this.getStyle("font-family"),E=new Pt(S.getString(),P.getString(),T.getString(),O.hasValue()?"".concat(O.getPixels(!0),"px"):"",A.getString(),Pt.parse(w.getString(),t.font));S.setValue(E.fontStyle),P.setValue(E.fontVariant),T.setValue(E.fontWeight),O.setValue(E.fontSize),A.setValue(E.fontFamily),t.font=E.toString(),O.isPixels()&&(this.document.emSize=O.getPixels(),this.modifiedEmSizeStack=!0)}e||(this.applyEffects(t),t.globalAlpha=this.calculateOpacity())}clearContext(t){super.clearContext(t),this.modifiedEmSizeStack&&this.document.popEmSize()}}class Et extends At{constructor(t,e,r){super(t,e,r),this.type="path",this.pathParser=null,this.pathParser=new Ot(this.getAttribute("d").getString())}path(t){var{pathParser:e}=this,r=new Tt;for(e.reset(),t&&t.beginPath();!e.isEnd();)switch(e.next().type){case Ot.MOVE_TO:this.pathM(t,r);break;case Ot.LINE_TO:this.pathL(t,r);break;case Ot.HORIZ_LINE_TO:this.pathH(t,r);break;case Ot.VERT_LINE_TO:this.pathV(t,r);break;case Ot.CURVE_TO:this.pathC(t,r);break;case Ot.SMOOTH_CURVE_TO:this.pathS(t,r);break;case Ot.QUAD_TO:this.pathQ(t,r);break;case Ot.SMOOTH_QUAD_TO:this.pathT(t,r);break;case Ot.ARC:this.pathA(t,r);break;case Ot.CLOSE_PATH:this.pathZ(t,r)}return r}getBoundingBox(t){return this.path()}getMarkers(){var{pathParser:t}=this,e=t.getMarkerPoints(),r=t.getMarkerAngles();return e.map((t,e)=>[t,r[e]])}renderChildren(t){this.path(t),this.document.screen.mouse.checkPath(this,t);var e=this.getStyle("fill-rule");""!==t.fillStyle&&("inherit"!==e.getString("inherit")?t.fill(e.getString()):t.fill()),""!==t.strokeStyle&&("non-scaling-stroke"===this.getAttribute("vector-effect").getString()?(t.save(),t.setTransform(1,0,0,1,0,0),t.stroke(),t.restore()):t.stroke());var r=this.getMarkers();if(r){var i=r.length-1,n=this.getStyle("marker-start"),s=this.getStyle("marker-mid"),a=this.getStyle("marker-end");if(n.isUrlDefinition()){var o=n.getDefinition(),[u,c]=r[0];o.render(t,u,c)}if(s.isUrlDefinition())for(var h=s.getDefinition(),l=1;l<i;l++){var[f,p]=r[l];h.render(t,f,p)}if(a.isUrlDefinition()){var g=a.getDefinition(),[d,v]=r[i];g.render(t,d,v)}}}static pathM(t){var e=t.getAsCurrentPoint();return t.start=t.current,{point:e}}pathM(t,e){var{pathParser:r}=this,{point:i}=Et.pathM(r),{x:n,y:s}=i;r.addMarker(i),e.addPoint(n,s),t&&t.moveTo(n,s)}static pathL(t){var{current:e}=t;return{current:e,point:t.getAsCurrentPoint()}}pathL(t,e){var{pathParser:r}=this,{current:i,point:n}=Et.pathL(r),{x:s,y:a}=n;r.addMarker(n,i),e.addPoint(s,a),t&&t.lineTo(s,a)}static pathH(t){var{current:e,command:r}=t,i=new nt((r.relative?e.x:0)+r.x,e.y);return t.current=i,{current:e,point:i}}pathH(t,e){var{pathParser:r}=this,{current:i,point:n}=Et.pathH(r),{x:s,y:a}=n;r.addMarker(n,i),e.addPoint(s,a),t&&t.lineTo(s,a)}static pathV(t){var{current:e,command:r}=t,i=new nt(e.x,(r.relative?e.y:0)+r.y);return t.current=i,{current:e,point:i}}pathV(t,e){var{pathParser:r}=this,{current:i,point:n}=Et.pathV(r),{x:s,y:a}=n;r.addMarker(n,i),e.addPoint(s,a),t&&t.lineTo(s,a)}static pathC(t){var{current:e}=t;return{current:e,point:t.getPoint("x1","y1"),controlPoint:t.getAsControlPoint("x2","y2"),currentPoint:t.getAsCurrentPoint()}}pathC(t,e){var{pathParser:r}=this,{current:i,point:n,controlPoint:s,currentPoint:a}=Et.pathC(r);r.addMarker(a,s,n),e.addBezierCurve(i.x,i.y,n.x,n.y,s.x,s.y,a.x,a.y),t&&t.bezierCurveTo(n.x,n.y,s.x,s.y,a.x,a.y)}static pathS(t){var{current:e}=t;return{current:e,point:t.getReflectedControlPoint(),controlPoint:t.getAsControlPoint("x2","y2"),currentPoint:t.getAsCurrentPoint()}}pathS(t,e){var{pathParser:r}=this,{current:i,point:n,controlPoint:s,currentPoint:a}=Et.pathS(r);r.addMarker(a,s,n),e.addBezierCurve(i.x,i.y,n.x,n.y,s.x,s.y,a.x,a.y),t&&t.bezierCurveTo(n.x,n.y,s.x,s.y,a.x,a.y)}static pathQ(t){var{current:e}=t;return{current:e,controlPoint:t.getAsControlPoint("x1","y1"),currentPoint:t.getAsCurrentPoint()}}pathQ(t,e){var{pathParser:r}=this,{current:i,controlPoint:n,currentPoint:s}=Et.pathQ(r);r.addMarker(s,n,n),e.addQuadraticCurve(i.x,i.y,n.x,n.y,s.x,s.y),t&&t.quadraticCurveTo(n.x,n.y,s.x,s.y)}static pathT(t){var{current:e}=t,r=t.getReflectedControlPoint();return t.control=r,{current:e,controlPoint:r,currentPoint:t.getAsCurrentPoint()}}pathT(t,e){var{pathParser:r}=this,{current:i,controlPoint:n,currentPoint:s}=Et.pathT(r);r.addMarker(s,n,n),e.addQuadraticCurve(i.x,i.y,n.x,n.y,s.x,s.y),t&&t.quadraticCurveTo(n.x,n.y,s.x,s.y)}static pathA(t){var{current:e,command:r}=t,{rX:i,rY:n,xRot:s,lArcFlag:a,sweepFlag:o}=r,u=s*(Math.PI/180),c=t.getAsCurrentPoint(),h=new nt(Math.cos(u)*(e.x-c.x)/2+Math.sin(u)*(e.y-c.y)/2,-Math.sin(u)*(e.x-c.x)/2+Math.cos(u)*(e.y-c.y)/2),l=Math.pow(h.x,2)/Math.pow(i,2)+Math.pow(h.y,2)/Math.pow(n,2);l>1&&(i*=Math.sqrt(l),n*=Math.sqrt(l));var f=(a===o?-1:1)*Math.sqrt((Math.pow(i,2)*Math.pow(n,2)-Math.pow(i,2)*Math.pow(h.y,2)-Math.pow(n,2)*Math.pow(h.x,2))/(Math.pow(i,2)*Math.pow(h.y,2)+Math.pow(n,2)*Math.pow(h.x,2)));isNaN(f)&&(f=0);var p=new nt(f*i*h.y/n,f*-n*h.x/i),g=new nt((e.x+c.x)/2+Math.cos(u)*p.x-Math.sin(u)*p.y,(e.y+c.y)/2+Math.sin(u)*p.x+Math.cos(u)*p.y),d=W([1,0],[(h.x-p.x)/i,(h.y-p.y)/n]),v=[(h.x-p.x)/i,(h.y-p.y)/n],y=[(-h.x-p.x)/i,(-h.y-p.y)/n],m=W(v,y);return G(v,y)<=-1&&(m=Math.PI),G(v,y)>=1&&(m=0),{currentPoint:c,rX:i,rY:n,sweepFlag:o,xAxisRotation:u,centp:g,a1:d,ad:m}}pathA(t,e){var{pathParser:r}=this,{currentPoint:i,rX:n,rY:s,sweepFlag:a,xAxisRotation:o,centp:u,a1:c,ad:h}=Et.pathA(r),l=1-a?1:-1,f=c+l*(h/2),p=new nt(u.x+n*Math.cos(f),u.y+s*Math.sin(f));if(r.addMarkerAngle(p,f-l*Math.PI/2),r.addMarkerAngle(i,f-l*Math.PI),e.addPoint(i.x,i.y),t&&!isNaN(c)&&!isNaN(h)){var g=n>s?n:s,d=n>s?1:n/s,v=n>s?s/n:1;t.translate(u.x,u.y),t.rotate(o),t.scale(d,v),t.arc(0,0,g,c,c+h,Boolean(1-a)),t.scale(1/d,1/v),t.rotate(-o),t.translate(-u.x,-u.y)}}static pathZ(t){t.current=t.start}pathZ(t,e){Et.pathZ(this.pathParser),t&&e.x1!==e.x2&&e.y1!==e.y2&&t.closePath()}}class Ct extends Et{constructor(t,e,r){super(t,e,r),this.type="glyph",this.horizAdvX=this.getAttribute("horiz-adv-x").getNumber(),this.unicode=this.getAttribute("unicode").getString(),this.arabicForm=this.getAttribute("arabic-form").getString()}}class Mt extends At{constructor(t,e,r){super(t,e,new.target===Mt||r),this.type="text",this.x=0,this.y=0,this.measureCache=-1}setContext(t){var e=arguments.length>1&&void 0!==arguments[1]&&arguments[1];super.setContext(t,e);var r=this.getStyle("dominant-baseline").getTextBaseline()||this.getStyle("alignment-baseline").getTextBaseline();r&&(t.textBaseline=r)}initializeCoordinates(){this.x=0,this.y=0,this.leafTexts=[],this.textChunkStart=0,this.minX=Number.POSITIVE_INFINITY,this.maxX=Number.NEGATIVE_INFINITY}getBoundingBox(t){if("text"!==this.type)return this.getTElementBoundingBox(t);this.initializeCoordinates(),this.adjustChildCoordinatesRecursive(t);var e=null;return this.children.forEach((r,i)=>{var n=this.getChildBoundingBox(t,this,this,i);e?e.addBoundingBox(n):e=n}),e}getFontSize(){var{document:t,parent:e}=this,r=Pt.parse(t.ctx.font).fontSize;return e.getStyle("font-size").getNumber(r)}getTElementBoundingBox(t){var e=this.getFontSize();return new Tt(this.x,this.y-e,this.x+this.measureText(t),this.y)}getGlyph(t,e,r){var i=e[r],n=null;if(t.isArabic){var s=e.length,a=e[r-1],o=e[r+1],u="isolated";if((0===r||" "===a)&&r<s-1&&" "!==o&&(u="terminal"),r>0&&" "!==a&&r<s-1&&" "!==o&&(u="medial"),r>0&&" "!==a&&(r===s-1||" "===o)&&(u="initial"),void 0!==t.glyphs[i]){var c=t.glyphs[i];n=c instanceof Ct?c:c[u]}}else n=t.glyphs[i];return n||(n=t.missingGlyph),n}getText(){return""}getTextFromNode(t){var e=t||this.node,r=Array.from(e.parentNode.childNodes),i=r.indexOf(e),n=r.length-1,s=C(e.textContent||"");return 0===i&&(s=M(s)),i===n&&(s=N(s)),s}renderChildren(t){if("text"===this.type){this.initializeCoordinates(),this.adjustChildCoordinatesRecursive(t),this.children.forEach((e,r)=>{this.renderChild(t,this,this,r)});var{mouse:e}=this.document.screen;e.isWorking()&&e.checkBoundingBox(this,this.getBoundingBox(t))}else this.renderTElementChildren(t)}renderTElementChildren(t){var{document:e,parent:r}=this,i=this.getText(),n=r.getStyle("font-family").getDefinition();if(n)for(var{unitsPerEm:s}=n.fontFace,a=Pt.parse(e.ctx.font),o=r.getStyle("font-size").getNumber(a.fontSize),u=r.getStyle("font-style").getString(a.fontStyle),c=o/s,h=n.isRTL?i.split("").reverse().join(""):i,l=V(r.getAttribute("dx").getString()),f=h.length,p=0;p<f;p++){var g=this.getGlyph(n,h,p);t.translate(this.x,this.y),t.scale(c,-c);var d=t.lineWidth;t.lineWidth=t.lineWidth*s/o,"italic"===u&&t.transform(1,0,.4,1,0,0),g.render(t),"italic"===u&&t.transform(1,0,-.4,1,0,0),t.lineWidth=d,t.scale(1/c,-1/c),t.translate(-this.x,-this.y),this.x+=o*(g.horizAdvX||n.horizAdvX)/s,void 0===l[p]||isNaN(l[p])||(this.x+=l[p])}else{var{x:v,y:y}=this;t.fillStyle&&t.fillText(i,v,y),t.strokeStyle&&t.strokeText(i,v,y)}}applyAnchoring(){if(!(this.textChunkStart>=this.leafTexts.length)){var t=this.leafTexts[this.textChunkStart],e=t.getStyle("text-anchor").getString("start"),r=0;r="start"===e?t.x-this.minX:"end"===e?t.x-this.maxX:t.x-(this.minX+this.maxX)/2;for(var i=this.textChunkStart;i<this.leafTexts.length;i++)this.leafTexts[i].x+=r;this.minX=Number.POSITIVE_INFINITY,this.maxX=Number.NEGATIVE_INFINITY,this.textChunkStart=this.leafTexts.length}}adjustChildCoordinatesRecursive(t){this.children.forEach((e,r)=>{this.adjustChildCoordinatesRecursiveCore(t,this,this,r)}),this.applyAnchoring()}adjustChildCoordinatesRecursiveCore(t,e,r,i){var n=r.children[i];n.children.length>0?n.children.forEach((r,i)=>{e.adjustChildCoordinatesRecursiveCore(t,e,n,i)}):this.adjustChildCoordinates(t,e,r,i)}adjustChildCoordinates(t,e,r,i){var n=r.children[i];if("function"!=typeof n.measureText)return n;t.save(),n.setContext(t,!0);var s=n.getAttribute("x"),a=n.getAttribute("y"),o=n.getAttribute("dx"),u=n.getAttribute("dy"),c=n.getStyle("font-family").getDefinition(),h=Boolean(c)&&c.isRTL;0===i&&(s.hasValue()||s.setValue(n.getInheritedAttribute("x")),a.hasValue()||a.setValue(n.getInheritedAttribute("y")),o.hasValue()||o.setValue(n.getInheritedAttribute("dx")),u.hasValue()||u.setValue(n.getInheritedAttribute("dy")));var l=n.measureText(t);return h&&(e.x-=l),s.hasValue()?(e.applyAnchoring(),n.x=s.getPixels("x"),o.hasValue()&&(n.x+=o.getPixels("x"))):(o.hasValue()&&(e.x+=o.getPixels("x")),n.x=e.x),e.x=n.x,h||(e.x+=l),a.hasValue()?(n.y=a.getPixels("y"),u.hasValue()&&(n.y+=u.getPixels("y"))):(u.hasValue()&&(e.y+=u.getPixels("y")),n.y=e.y),e.y=n.y,e.leafTexts.push(n),e.minX=Math.min(e.minX,n.x,n.x+l),e.maxX=Math.max(e.maxX,n.x,n.x+l),n.clearContext(t),t.restore(),n}getChildBoundingBox(t,e,r,i){var n=r.children[i];if("function"!=typeof n.getBoundingBox)return null;var s=n.getBoundingBox(t);return s?(n.children.forEach((r,i)=>{var a=e.getChildBoundingBox(t,e,n,i);s.addBoundingBox(a)}),s):null}renderChild(t,e,r,i){var n=r.children[i];n.render(t),n.children.forEach((r,i)=>{e.renderChild(t,e,n,i)})}measureText(t){var{measureCache:e}=this;if(~e)return e;var r=this.getText(),i=this.measureTargetText(t,r);return this.measureCache=i,i}measureTargetText(t,e){if(!e.length)return 0;var{parent:r}=this,i=r.getStyle("font-family").getDefinition();if(i){for(var n=this.getFontSize(),s=i.isRTL?e.split("").reverse().join(""):e,a=V(r.getAttribute("dx").getString()),o=s.length,u=0,c=0;c<o;c++){u+=(this.getGlyph(i,s,c).horizAdvX||i.horizAdvX)*n/i.fontFace.unitsPerEm,void 0===a[c]||isNaN(a[c])||(u+=a[c])}return u}if(!t.measureText)return 10*e.length;t.save(),this.setContext(t,!0);var{width:h}=t.measureText(e);return this.clearContext(t),t.restore(),h}getInheritedAttribute(t){for(var e=this;e instanceof Mt&&e.isFirstChild();){var r=e.parent.getAttribute(t);if(r.hasValue(!0))return r.getValue("0");e=e.parent}return null}}class Nt extends Mt{constructor(t,e,r){super(t,e,new.target===Nt||r),this.type="tspan",this.text=this.children.length>0?"":this.getTextFromNode()}getText(){return this.text}}class Vt extends Nt{constructor(){super(...arguments),this.type="textNode"}}class Rt extends At{constructor(){super(...arguments),this.type="svg",this.root=!1}setContext(t){var e,{document:r}=this,{screen:i,window:n}=r,s=t.canvas;if(i.setDefaults(t),s.style&&void 0!==t.font&&n&&void 0!==n.getComputedStyle){t.font=n.getComputedStyle(s).getPropertyValue("font");var a=new rt(r,"fontSize",Pt.parse(t.font).fontSize);a.hasValue()&&(r.rootEmSize=a.getPixels("y"),r.emSize=r.rootEmSize)}this.getAttribute("x").hasValue()||this.getAttribute("x",!0).setValue(0),this.getAttribute("y").hasValue()||this.getAttribute("y",!0).setValue(0);var{width:o,height:u}=i.viewPort;this.getStyle("width").hasValue()||this.getStyle("width",!0).setValue("100%"),this.getStyle("height").hasValue()||this.getStyle("height",!0).setValue("100%"),this.getStyle("color").hasValue()||this.getStyle("color",!0).setValue("black");var c=this.getAttribute("refX"),h=this.getAttribute("refY"),l=this.getAttribute("viewBox"),f=l.hasValue()?V(l.getString()):null,p=!this.root&&"visible"!==this.getStyle("overflow").getValue("hidden"),g=0,d=0,v=0,y=0;f&&(g=f[0],d=f[1]),this.root||(o=this.getStyle("width").getPixels("x"),u=this.getStyle("height").getPixels("y"),"marker"===this.type&&(v=g,y=d,g=0,d=0)),i.viewPort.setCurrent(o,u),!this.node||this.parent&&"foreignObject"!==(null===(e=this.node.parentNode)||void 0===e?void 0:e.nodeName)||!this.getStyle("transform",!1,!0).hasValue()||this.getStyle("transform-origin",!1,!0).hasValue()||this.getStyle("transform-origin",!0,!0).setValue("50% 50%"),super.setContext(t),t.translate(this.getAttribute("x").getPixels("x"),this.getAttribute("y").getPixels("y")),f&&(o=f[2],u=f[3]),r.setViewBox({ctx:t,aspectRatio:this.getAttribute("preserveAspectRatio").getString(),width:i.viewPort.width,desiredWidth:o,height:i.viewPort.height,desiredHeight:u,minX:g,minY:d,refX:c.getValue(),refY:h.getValue(),clip:p,clipX:v,clipY:y}),f&&(i.viewPort.removeCurrent(),i.viewPort.setCurrent(o,u))}clearContext(t){super.clearContext(t),this.document.screen.viewPort.removeCurrent()}resize(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:t,r=arguments.length>2&&void 0!==arguments[2]&&arguments[2],i=this.getAttribute("width",!0),n=this.getAttribute("height",!0),s=this.getAttribute("viewBox"),a=this.getAttribute("style"),o=i.getNumber(0),u=n.getNumber(0);if(r)if("string"==typeof r)this.getAttribute("preserveAspectRatio",!0).setValue(r);else{var c=this.getAttribute("preserveAspectRatio");c.hasValue()&&c.setValue(c.getString().replace(/^\s*(\S.*\S)\s*$/,"$1"))}if(i.setValue(t),n.setValue(e),s.hasValue()||s.setValue("0 0 ".concat(o||t," ").concat(u||e)),a.hasValue()){var h=this.getStyle("width"),l=this.getStyle("height");h.hasValue()&&h.setValue("".concat(t,"px")),l.hasValue()&&l.setValue("".concat(e,"px"))}}}class It extends Et{constructor(){super(...arguments),this.type="rect"}path(t){var e=this.getAttribute("x").getPixels("x"),r=this.getAttribute("y").getPixels("y"),i=this.getStyle("width",!1,!0).getPixels("x"),n=this.getStyle("height",!1,!0).getPixels("y"),s=this.getAttribute("rx"),a=this.getAttribute("ry"),o=s.getPixels("x"),u=a.getPixels("y");if(s.hasValue()&&!a.hasValue()&&(u=o),a.hasValue()&&!s.hasValue()&&(o=u),o=Math.min(o,i/2),u=Math.min(u,n/2),t){var c=(Math.sqrt(2)-1)/3*4;t.beginPath(),n>0&&i>0&&(t.moveTo(e+o,r),t.lineTo(e+i-o,r),t.bezierCurveTo(e+i-o+c*o,r,e+i,r+u-c*u,e+i,r+u),t.lineTo(e+i,r+n-u),t.bezierCurveTo(e+i,r+n-u+c*u,e+i-o+c*o,r+n,e+i-o,r+n),t.lineTo(e+o,r+n),t.bezierCurveTo(e+o-c*o,r+n,e,r+n-u+c*u,e,r+n-u),t.lineTo(e,r+u),t.bezierCurveTo(e,r+u-c*u,e+o-c*o,r,e+o,r),t.closePath())}return new Tt(e,r,e+i,r+n)}getMarkers(){return null}}class kt extends Et{constructor(){super(...arguments),this.type="circle"}path(t){var e=this.getAttribute("cx").getPixels("x"),r=this.getAttribute("cy").getPixels("y"),i=this.getAttribute("r").getPixels();return t&&i>0&&(t.beginPath(),t.arc(e,r,i,0,2*Math.PI,!1),t.closePath()),new Tt(e-i,r-i,e+i,r+i)}getMarkers(){return null}}class _t extends Et{constructor(){super(...arguments),this.type="ellipse"}path(t){var e=(Math.sqrt(2)-1)/3*4,r=this.getAttribute("rx").getPixels("x"),i=this.getAttribute("ry").getPixels("y"),n=this.getAttribute("cx").getPixels("x"),s=this.getAttribute("cy").getPixels("y");return t&&r>0&&i>0&&(t.beginPath(),t.moveTo(n+r,s),t.bezierCurveTo(n+r,s+e*i,n+e*r,s+i,n,s+i),t.bezierCurveTo(n-e*r,s+i,n-r,s+e*i,n-r,s),t.bezierCurveTo(n-r,s-e*i,n-e*r,s-i,n,s-i),t.bezierCurveTo(n+e*r,s-i,n+r,s-e*i,n+r,s),t.closePath()),new Tt(n-r,s-i,n+r,s+i)}getMarkers(){return null}}class Lt extends Et{constructor(){super(...arguments),this.type="line"}getPoints(){return[new nt(this.getAttribute("x1").getPixels("x"),this.getAttribute("y1").getPixels("y")),new nt(this.getAttribute("x2").getPixels("x"),this.getAttribute("y2").getPixels("y"))]}path(t){var[{x:e,y:r},{x:i,y:n}]=this.getPoints();return t&&(t.beginPath(),t.moveTo(e,r),t.lineTo(i,n)),new Tt(e,r,i,n)}getMarkers(){var[t,e]=this.getPoints(),r=t.angleTo(e);return[[t,r],[e,r]]}}class Dt extends Et{constructor(t,e,r){super(t,e,r),this.type="polyline",this.points=[],this.points=nt.parsePath(this.getAttribute("points").getString())}path(t){var{points:e}=this,[{x:r,y:i}]=e,n=new Tt(r,i);return t&&(t.beginPath(),t.moveTo(r,i)),e.forEach(e=>{var{x:r,y:i}=e;n.addPoint(r,i),t&&t.lineTo(r,i)}),n}getMarkers(){var{points:t}=this,e=t.length-1,r=[];return t.forEach((i,n)=>{n!==e&&r.push([i,i.angleTo(t[n+1])])}),r.length>0&&r.push([t[t.length-1],r[r.length-1][1]]),r}}class jt extends Dt{constructor(){super(...arguments),this.type="polygon"}path(t){var e=super.path(t),[{x:r,y:i}]=this.points;return t&&(t.lineTo(r,i),t.closePath()),e}}class Bt extends bt{constructor(){super(...arguments),this.type="pattern"}createPattern(t,e,r){var i=this.getStyle("width").getPixels("x",!0),n=this.getStyle("height").getPixels("y",!0),s=new Rt(this.document,null);s.attributes.viewBox=new rt(this.document,"viewBox",this.getAttribute("viewBox").getValue()),s.attributes.width=new rt(this.document,"width","".concat(i,"px")),s.attributes.height=new rt(this.document,"height","".concat(n,"px")),s.attributes.transform=new rt(this.document,"transform",this.getAttribute("patternTransform").getValue()),s.children=this.children;var a=this.document.createCanvas(i,n),o=a.getContext("2d"),u=this.getAttribute("x"),c=this.getAttribute("y");u.hasValue()&&c.hasValue()&&o.translate(u.getPixels("x",!0),c.getPixels("y",!0)),r.hasValue()?this.styles["fill-opacity"]=r:Reflect.deleteProperty(this.styles,"fill-opacity");for(var h=-1;h<=1;h++)for(var l=-1;l<=1;l++)o.save(),s.attributes.x=new rt(this.document,"x",h*a.width),s.attributes.y=new rt(this.document,"y",l*a.height),s.render(o),o.restore();return t.createPattern(a,"repeat")}}class qt extends bt{constructor(){super(...arguments),this.type="marker"}render(t,e,r){if(e){var{x:i,y:n}=e,s=this.getAttribute("orient").getString("auto"),a=this.getAttribute("markerUnits").getString("strokeWidth");t.translate(i,n),"auto"===s&&t.rotate(r),"strokeWidth"===a&&t.scale(t.lineWidth,t.lineWidth),t.save();var o=new Rt(this.document,null);o.type=this.type,o.attributes.viewBox=new rt(this.document,"viewBox",this.getAttribute("viewBox").getValue()),o.attributes.refX=new rt(this.document,"refX",this.getAttribute("refX").getValue()),o.attributes.refY=new rt(this.document,"refY",this.getAttribute("refY").getValue()),o.attributes.width=new rt(this.document,"width",this.getAttribute("markerWidth").getValue()),o.attributes.height=new rt(this.document,"height",this.getAttribute("markerHeight").getValue()),o.attributes.overflow=new rt(this.document,"overflow",this.getAttribute("overflow").getValue()),o.attributes.fill=new rt(this.document,"fill",this.getAttribute("fill").getColor("black")),o.attributes.stroke=new rt(this.document,"stroke",this.getAttribute("stroke").getValue("none")),o.children=this.children,o.render(t),t.restore(),"strokeWidth"===a&&t.scale(1/t.lineWidth,1/t.lineWidth),"auto"===s&&t.rotate(-r),t.translate(-i,-n)}}}class Ut extends bt{constructor(){super(...arguments),this.type="defs"}render(){}}class zt extends At{constructor(){super(...arguments),this.type="g"}getBoundingBox(t){var e=new Tt;return this.children.forEach(r=>{e.addBoundingBox(r.getBoundingBox(t))}),e}}class Xt extends bt{constructor(t,e,r){super(t,e,r),this.attributesToInherit=["gradientUnits"],this.stops=[];var{stops:i,children:n}=this;n.forEach(t=>{"stop"===t.type&&i.push(t)})}getGradientUnits(){return this.getAttribute("gradientUnits").getString("objectBoundingBox")}createGradient(t,e,r){var i=this;this.getHrefAttribute().hasValue()&&(i=this.getHrefAttribute().getDefinition(),this.inheritStopContainer(i));var{stops:n}=i,s=this.getGradient(t,e);if(!s)return this.addParentOpacity(r,n[n.length-1].color);if(n.forEach(t=>{s.addColorStop(t.offset,this.addParentOpacity(r,t.color))}),this.getAttribute("gradientTransform").hasValue()){var{document:a}=this,{MAX_VIRTUAL_PIXELS:o,viewPort:u}=a.screen,[c]=u.viewPorts,h=new It(a,null);h.attributes.x=new rt(a,"x",-o/3),h.attributes.y=new rt(a,"y",-o/3),h.attributes.width=new rt(a,"width",o),h.attributes.height=new rt(a,"height",o);var l=new zt(a,null);l.attributes.transform=new rt(a,"transform",this.getAttribute("gradientTransform").getValue()),l.children=[h];var f=new Rt(a,null);f.attributes.x=new rt(a,"x",0),f.attributes.y=new rt(a,"y",0),f.attributes.width=new rt(a,"width",c.width),f.attributes.height=new rt(a,"height",c.height),f.children=[l];var p=a.createCanvas(c.width,c.height),g=p.getContext("2d");return g.fillStyle=s,f.render(g),g.createPattern(p,"no-repeat")}return s}inheritStopContainer(t){this.attributesToInherit.forEach(e=>{!this.getAttribute(e).hasValue()&&t.getAttribute(e).hasValue()&&this.getAttribute(e,!0).setValue(t.getAttribute(e).getValue())})}addParentOpacity(t,e){return t.hasValue()?new rt(this.document,"color",e).addOpacity(t).getColor():e}}class Ft extends Xt{constructor(t,e,r){super(t,e,r),this.type="linearGradient",this.attributesToInherit.push("x1","y1","x2","y2")}getGradient(t,e){var r="objectBoundingBox"===this.getGradientUnits(),i=r?e.getBoundingBox(t):null;if(r&&!i)return null;this.getAttribute("x1").hasValue()||this.getAttribute("y1").hasValue()||this.getAttribute("x2").hasValue()||this.getAttribute("y2").hasValue()||(this.getAttribute("x1",!0).setValue(0),this.getAttribute("y1",!0).setValue(0),this.getAttribute("x2",!0).setValue(1),this.getAttribute("y2",!0).setValue(0));var n=r?i.x+i.width*this.getAttribute("x1").getNumber():this.getAttribute("x1").getPixels("x"),s=r?i.y+i.height*this.getAttribute("y1").getNumber():this.getAttribute("y1").getPixels("y"),a=r?i.x+i.width*this.getAttribute("x2").getNumber():this.getAttribute("x2").getPixels("x"),o=r?i.y+i.height*this.getAttribute("y2").getNumber():this.getAttribute("y2").getPixels("y");return n===a&&s===o?null:t.createLinearGradient(n,s,a,o)}}class Ht extends Xt{constructor(t,e,r){super(t,e,r),this.type="radialGradient",this.attributesToInherit.push("cx","cy","r","fx","fy","fr")}getGradient(t,e){var r="objectBoundingBox"===this.getGradientUnits(),i=e.getBoundingBox(t);if(r&&!i)return null;this.getAttribute("cx").hasValue()||this.getAttribute("cx",!0).setValue("50%"),this.getAttribute("cy").hasValue()||this.getAttribute("cy",!0).setValue("50%"),this.getAttribute("r").hasValue()||this.getAttribute("r",!0).setValue("50%");var n=r?i.x+i.width*this.getAttribute("cx").getNumber():this.getAttribute("cx").getPixels("x"),s=r?i.y+i.height*this.getAttribute("cy").getNumber():this.getAttribute("cy").getPixels("y"),a=n,o=s;this.getAttribute("fx").hasValue()&&(a=r?i.x+i.width*this.getAttribute("fx").getNumber():this.getAttribute("fx").getPixels("x")),this.getAttribute("fy").hasValue()&&(o=r?i.y+i.height*this.getAttribute("fy").getNumber():this.getAttribute("fy").getPixels("y"));var u=r?(i.width+i.height)/2*this.getAttribute("r").getNumber():this.getAttribute("r").getPixels(),c=this.getAttribute("fr").getPixels();return t.createRadialGradient(a,o,c,n,s,u)}}class Yt extends bt{constructor(t,e,r){super(t,e,r),this.type="stop";var i=Math.max(0,Math.min(1,this.getAttribute("offset").getNumber())),n=this.getStyle("stop-opacity"),s=this.getStyle("stop-color",!0);""===s.getString()&&s.setValue("#000"),n.hasValue()&&(s=s.addOpacity(n)),this.offset=i,this.color=s.getColor()}}class Gt extends bt{constructor(t,e,r){super(t,e,r),this.type="animate",this.duration=0,this.initialValue=null,this.initialUnits="",this.removed=!1,this.frozen=!1,t.screen.animations.push(this),this.begin=this.getAttribute("begin").getMilliseconds(),this.maxDuration=this.begin+this.getAttribute("dur").getMilliseconds(),this.from=this.getAttribute("from"),this.to=this.getAttribute("to"),this.values=new rt(t,"values",null);var i=this.getAttribute("values");i.hasValue()&&this.values.setValue(i.getString().split(";"))}getProperty(){var t=this.getAttribute("attributeType").getString(),e=this.getAttribute("attributeName").getString();return"CSS"===t?this.parent.getStyle(e,!0):this.parent.getAttribute(e,!0)}calcValue(){var{initialUnits:t}=this,{progress:e,from:r,to:i}=this.getProgress(),n=r.getNumber()+(i.getNumber()-r.getNumber())*e;return"%"===t&&(n*=100),"".concat(n).concat(t)}update(t){var{parent:e}=this,r=this.getProperty();if(this.initialValue||(this.initialValue=r.getString(),this.initialUnits=r.getUnits()),this.duration>this.maxDuration){var i=this.getAttribute("fill").getString("remove");if("indefinite"===this.getAttribute("repeatCount").getString()||"indefinite"===this.getAttribute("repeatDur").getString())this.duration=0;else if("freeze"!==i||this.frozen){if("remove"===i&&!this.removed)return this.removed=!0,r.setValue(e.animationFrozen?e.animationFrozenValue:this.initialValue),!0}else this.frozen=!0,e.animationFrozen=!0,e.animationFrozenValue=r.getString();return!1}this.duration+=t;var n=!1;if(this.begin<this.duration){var s=this.calcValue(),a=this.getAttribute("type");if(a.hasValue()){var o=a.getString();s="".concat(o,"(").concat(s,")")}r.setValue(s),n=!0}return n}getProgress(){var{document:t,values:e}=this,r={progress:(this.duration-this.begin)/(this.maxDuration-this.begin)};if(e.hasValue()){var i=r.progress*(e.getValue().length-1),n=Math.floor(i),s=Math.ceil(i);r.from=new rt(t,"from",parseFloat(e.getValue()[n])),r.to=new rt(t,"to",parseFloat(e.getValue()[s])),r.progress=(i-n)/(s-n)}else r.from=this.from,r.to=this.to;return r}}class Wt extends Gt{constructor(){super(...arguments),this.type="animateColor"}calcValue(){var{progress:t,from:e,to:r}=this.getProgress(),i=new b.a(e.getColor()),n=new b.a(r.getColor());if(i.ok&&n.ok){var s=i.r+(n.r-i.r)*t,a=i.g+(n.g-i.g)*t,o=i.b+(n.b-i.b)*t;return"rgb(".concat(Math.floor(s),", ").concat(Math.floor(a),", ").concat(Math.floor(o),")")}return this.getAttribute("from").getColor()}}class Qt extends Gt{constructor(){super(...arguments),this.type="animateTransform"}calcValue(){var{progress:t,from:e,to:r}=this.getProgress(),i=V(e.getString()),n=V(r.getString());return i.map((e,r)=>{return e+(n[r]-e)*t}).join(" ")}}class Zt extends bt{constructor(t,e,r){super(t,e,r),this.type="font",this.glyphs=Object.create(null),this.horizAdvX=this.getAttribute("horiz-adv-x").getNumber();var{definitions:i}=t,{children:n}=this;for(var s of n)switch(s.type){case"font-face":this.fontFace=s;var a=s.getStyle("font-family");a.hasValue()&&(i[a.getString()]=this);break;case"missing-glyph":this.missingGlyph=s;break;case"glyph":var o=s;o.arabicForm?(this.isRTL=!0,this.isArabic=!0,void 0===this.glyphs[o.unicode]&&(this.glyphs[o.unicode]=Object.create(null)),this.glyphs[o.unicode][o.arabicForm]=o):this.glyphs[o.unicode]=o}}render(){}}class Jt extends bt{constructor(t,e,r){super(t,e,r),this.type="font-face",this.ascent=this.getAttribute("ascent").getNumber(),this.descent=this.getAttribute("descent").getNumber(),this.unitsPerEm=this.getAttribute("units-per-em").getNumber()}}class $t extends Et{constructor(){super(...arguments),this.type="missing-glyph",this.horizAdvX=0}}class Kt extends Mt{constructor(){super(...arguments),this.type="tref"}getText(){var t=this.getHrefAttribute().getDefinition();if(t){var e=t.children[0];if(e)return e.getText()}return""}}class te extends Mt{constructor(t,e,r){super(t,e,r),this.type="a";var{childNodes:i}=e,n=i[0],s=i.length>0&&Array.from(i).every(t=>3===t.nodeType);this.hasText=s,this.text=s?this.getTextFromNode(n):""}getText(){return this.text}renderChildren(t){if(this.hasText){super.renderChildren(t);var{document:e,x:r,y:i}=this,{mouse:n}=e.screen,s=new rt(e,"fontSize",Pt.parse(e.ctx.font).fontSize);n.isWorking()&&n.checkBoundingBox(this,new Tt(r,i-s.getPixels("y"),r+this.measureText(t),i))}else if(this.children.length>0){var a=new zt(this.document,null);a.children=this.children,a.parent=this,a.render(t)}}onClick(){var{window:t}=this.document;t&&t.open(this.getHrefAttribute().getString())}onMouseMove(){this.document.ctx.canvas.style.cursor="pointer"}}function ee(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);e&&(i=i.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,i)}return r}function re(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?ee(Object(r),!0).forEach(function(e){f()(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):ee(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}class ie extends Mt{constructor(t,e,r){super(t,e,r),this.type="textPath",this.textWidth=0,this.textHeight=0,this.pathLength=-1,this.glyphInfo=null,this.letterSpacingCache=[],this.measuresCache=new Map([["",0]]);var i=this.getHrefAttribute().getDefinition();this.text=this.getTextFromNode(),this.dataArray=this.parsePathData(i)}getText(){return this.text}path(t){var{dataArray:e}=this;t&&t.beginPath(),e.forEach(e=>{var{type:r,points:i}=e;switch(r){case Ot.LINE_TO:t&&t.lineTo(i[0],i[1]);break;case Ot.MOVE_TO:t&&t.moveTo(i[0],i[1]);break;case Ot.CURVE_TO:t&&t.bezierCurveTo(i[0],i[1],i[2],i[3],i[4],i[5]);break;case Ot.QUAD_TO:t&&t.quadraticCurveTo(i[0],i[1],i[2],i[3]);break;case Ot.ARC:var[n,s,a,o,u,c,h,l]=i,f=a>o?a:o,p=a>o?1:a/o,g=a>o?o/a:1;t&&(t.translate(n,s),t.rotate(h),t.scale(p,g),t.arc(0,0,f,u,u+c,Boolean(1-l)),t.scale(1/p,1/g),t.rotate(-h),t.translate(-n,-s));break;case Ot.CLOSE_PATH:t&&t.closePath()}})}renderChildren(t){this.setTextData(t),t.save();var e=this.parent.getStyle("text-decoration").getString(),r=this.getFontSize(),{glyphInfo:i}=this,n=t.fillStyle;"underline"===e&&t.beginPath(),i.forEach((i,n)=>{var{p0:s,p1:a,rotation:o,text:u}=i;t.save(),t.translate(s.x,s.y),t.rotate(o),t.fillStyle&&t.fillText(u,0,0),t.strokeStyle&&t.strokeText(u,0,0),t.restore(),"underline"===e&&(0===n&&t.moveTo(s.x,s.y+r/8),t.lineTo(a.x,a.y+r/5))}),"underline"===e&&(t.lineWidth=r/20,t.strokeStyle=n,t.stroke(),t.closePath()),t.restore()}getLetterSpacingAt(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0;return this.letterSpacingCache[t]||0}findSegmentToFitChar(t,e,r,i,n,s,a,o,u){var c=s,h=this.measureText(t,o);" "===o&&"justify"===e&&r<i&&(h+=(i-r)/n),u>-1&&(c+=this.getLetterSpacingAt(u));var l=this.textHeight/20,f=this.getEquidistantPointOnPath(c,l,0),p=this.getEquidistantPointOnPath(c+h,l,0),g={p0:f,p1:p},d=f&&p?Math.atan2(p.y-f.y,p.x-f.x):0;if(a){var v=Math.cos(Math.PI/2+d)*a,y=Math.cos(-d)*a;g.p0=re(re({},f),{},{x:f.x+v,y:f.y+y}),g.p1=re(re({},p),{},{x:p.x+v,y:p.y+y})}return{offset:c+=h,segment:g,rotation:d}}measureText(t,e){var{measuresCache:r}=this,i=e||this.getText();if(r.has(i))return r.get(i);var n=this.measureTargetText(t,i);return r.set(i,n),n}setTextData(t){if(!this.glyphInfo){var e=this.getText(),r=e.split(""),i=e.split(" ").length-1,n=this.parent.getAttribute("dx").split().map(t=>t.getPixels("x")),s=this.parent.getAttribute("dy").getPixels("y"),a=this.parent.getStyle("text-anchor").getString("start"),o=this.getStyle("letter-spacing"),u=this.parent.getStyle("letter-spacing"),c=0;o.hasValue()&&"inherit"!==o.getValue()?o.hasValue()&&"initial"!==o.getValue()&&"unset"!==o.getValue()&&(c=o.getPixels()):c=u.getPixels();var h=[],l=e.length;this.letterSpacingCache=h;for(var f=0;f<l;f++)h.push(void 0!==n[f]?n[f]:c);var p=h.reduce((t,e,r)=>0===r?0:t+e||0,0),g=this.measureText(t),d=Math.max(g+p,0);this.textWidth=g,this.textHeight=this.getFontSize(),this.glyphInfo=[];var v=this.getPathLength(),y=this.getStyle("startOffset").getNumber(0)*v,m=0;"middle"!==a&&"center"!==a||(m=-d/2),"end"!==a&&"right"!==a||(m=-d),m+=y,r.forEach((e,n)=>{var{offset:o,segment:u,rotation:c}=this.findSegmentToFitChar(t,a,d,v,i,m,s,e,n);m=o,u.p0&&u.p1&&this.glyphInfo.push({text:r[n],p0:u.p0,p1:u.p1,rotation:c})})}}parsePathData(t){if(this.pathLength=-1,!t)return[];var e=[],{pathParser:r}=t;for(r.reset();!r.isEnd();){var{current:i}=r,n=i?i.x:0,s=i?i.y:0,a=r.next(),o=a.type,u=[];switch(a.type){case Ot.MOVE_TO:this.pathM(r,u);break;case Ot.LINE_TO:o=this.pathL(r,u);break;case Ot.HORIZ_LINE_TO:o=this.pathH(r,u);break;case Ot.VERT_LINE_TO:o=this.pathV(r,u);break;case Ot.CURVE_TO:this.pathC(r,u);break;case Ot.SMOOTH_CURVE_TO:o=this.pathS(r,u);break;case Ot.QUAD_TO:this.pathQ(r,u);break;case Ot.SMOOTH_QUAD_TO:o=this.pathT(r,u);break;case Ot.ARC:u=this.pathA(r);break;case Ot.CLOSE_PATH:Et.pathZ(r)}a.type!==Ot.CLOSE_PATH?e.push({type:o,points:u,start:{x:n,y:s},pathLength:this.calcLength(n,s,o,u)}):e.push({type:Ot.CLOSE_PATH,points:[],pathLength:0})}return e}pathM(t,e){var{x:r,y:i}=Et.pathM(t).point;e.push(r,i)}pathL(t,e){var{x:r,y:i}=Et.pathL(t).point;return e.push(r,i),Ot.LINE_TO}pathH(t,e){var{x:r,y:i}=Et.pathH(t).point;return e.push(r,i),Ot.LINE_TO}pathV(t,e){var{x:r,y:i}=Et.pathV(t).point;return e.push(r,i),Ot.LINE_TO}pathC(t,e){var{point:r,controlPoint:i,currentPoint:n}=Et.pathC(t);e.push(r.x,r.y,i.x,i.y,n.x,n.y)}pathS(t,e){var{point:r,controlPoint:i,currentPoint:n}=Et.pathS(t);return e.push(r.x,r.y,i.x,i.y,n.x,n.y),Ot.CURVE_TO}pathQ(t,e){var{controlPoint:r,currentPoint:i}=Et.pathQ(t);e.push(r.x,r.y,i.x,i.y)}pathT(t,e){var{controlPoint:r,currentPoint:i}=Et.pathT(t);return e.push(r.x,r.y,i.x,i.y),Ot.QUAD_TO}pathA(t){var{rX:e,rY:r,sweepFlag:i,xAxisRotation:n,centp:s,a1:a,ad:o}=Et.pathA(t);return 0===i&&o>0&&(o-=2*Math.PI),1===i&&o<0&&(o+=2*Math.PI),[s.x,s.y,e,r,a,o,n,i]}calcLength(t,e,r,i){var n=0,s=null,a=null,o=0;switch(r){case Ot.LINE_TO:return this.getLineLength(t,e,i[0],i[1]);case Ot.CURVE_TO:for(n=0,s=this.getPointOnCubicBezier(0,t,e,i[0],i[1],i[2],i[3],i[4],i[5]),o=.01;o<=1;o+=.01)a=this.getPointOnCubicBezier(o,t,e,i[0],i[1],i[2],i[3],i[4],i[5]),n+=this.getLineLength(s.x,s.y,a.x,a.y),s=a;return n;case Ot.QUAD_TO:for(n=0,s=this.getPointOnQuadraticBezier(0,t,e,i[0],i[1],i[2],i[3]),o=.01;o<=1;o+=.01)a=this.getPointOnQuadraticBezier(o,t,e,i[0],i[1],i[2],i[3]),n+=this.getLineLength(s.x,s.y,a.x,a.y),s=a;return n;case Ot.ARC:n=0;var u=i[4],c=i[5],h=i[4]+c,l=Math.PI/180;if(Math.abs(u-h)<l&&(l=Math.abs(u-h)),s=this.getPointOnEllipticalArc(i[0],i[1],i[2],i[3],u,0),c<0)for(o=u-l;o>h;o-=l)a=this.getPointOnEllipticalArc(i[0],i[1],i[2],i[3],o,0),n+=this.getLineLength(s.x,s.y,a.x,a.y),s=a;else for(o=u+l;o<h;o+=l)a=this.getPointOnEllipticalArc(i[0],i[1],i[2],i[3],o,0),n+=this.getLineLength(s.x,s.y,a.x,a.y),s=a;return a=this.getPointOnEllipticalArc(i[0],i[1],i[2],i[3],h,0),n+=this.getLineLength(s.x,s.y,a.x,a.y)}return 0}getPointOnLine(t,e,r,i,n){var s=arguments.length>5&&void 0!==arguments[5]?arguments[5]:e,a=arguments.length>6&&void 0!==arguments[6]?arguments[6]:r,o=(n-r)/(i-e+H),u=Math.sqrt(t*t/(1+o*o));i<e&&(u*=-1);var c=o*u,h=null;if(i===e)h={x:s,y:a+c};else if((a-r)/(s-e+H)===o)h={x:s+u,y:a+c};else{var l,f,p=this.getLineLength(e,r,i,n);if(p<H)return null;var g=(s-e)*(i-e)+(a-r)*(n-r);l=e+(g/=p*p)*(i-e),f=r+g*(n-r);var d=this.getLineLength(s,a,l,f),v=Math.sqrt(t*t-d*d);u=Math.sqrt(v*v/(1+o*o)),i<e&&(u*=-1),h={x:l+u,y:f+(c=o*u)}}return h}getPointOnPath(t){var e=this.getPathLength(),r=0,i=null;if(t<-5e-5||t-5e-5>e)return null;var{dataArray:n}=this;for(var s of n){if(!s||!(s.pathLength<5e-5||r+s.pathLength+5e-5<t)){var a=t-r,o=0;switch(s.type){case Ot.LINE_TO:i=this.getPointOnLine(a,s.start.x,s.start.y,s.points[0],s.points[1],s.start.x,s.start.y);break;case Ot.ARC:var u=s.points[4],c=s.points[5],h=s.points[4]+c;if(o=u+a/s.pathLength*c,c<0&&o<h||c>=0&&o>h)break;i=this.getPointOnEllipticalArc(s.points[0],s.points[1],s.points[2],s.points[3],o,s.points[6]);break;case Ot.CURVE_TO:(o=a/s.pathLength)>1&&(o=1),i=this.getPointOnCubicBezier(o,s.start.x,s.start.y,s.points[0],s.points[1],s.points[2],s.points[3],s.points[4],s.points[5]);break;case Ot.QUAD_TO:(o=a/s.pathLength)>1&&(o=1),i=this.getPointOnQuadraticBezier(o,s.start.x,s.start.y,s.points[0],s.points[1],s.points[2],s.points[3])}if(i)return i;break}r+=s.pathLength}return null}getLineLength(t,e,r,i){return Math.sqrt((r-t)*(r-t)+(i-e)*(i-e))}getPathLength(){return-1===this.pathLength&&(this.pathLength=this.dataArray.reduce((t,e)=>e.pathLength>0?t+e.pathLength:t,0)),this.pathLength}getPointOnCubicBezier(t,e,r,i,n,s,a,o,u){return{x:o*Q(t)+s*Z(t)+i*J(t)+e*$(t),y:u*Q(t)+a*Z(t)+n*J(t)+r*$(t)}}getPointOnQuadraticBezier(t,e,r,i,n,s,a){return{x:s*K(t)+i*tt(t)+e*et(t),y:a*K(t)+n*tt(t)+r*et(t)}}getPointOnEllipticalArc(t,e,r,i,n,s){var a=Math.cos(s),o=Math.sin(s),u=r*Math.cos(n),c=i*Math.sin(n);return{x:t+(u*a-c*o),y:e+(u*o+c*a)}}buildEquidistantCache(t,e){var r=this.getPathLength(),i=e||.25,n=t||r/100;if(!this.equidistantCache||this.equidistantCache.step!==n||this.equidistantCache.precision!==i){this.equidistantCache={step:n,precision:i,points:[]};for(var s=0,a=0;a<=r;a+=i){var o=this.getPointOnPath(a),u=this.getPointOnPath(a+i);o&&u&&((s+=this.getLineLength(o.x,o.y,u.x,u.y))>=n&&(this.equidistantCache.points.push({x:o.x,y:o.y,distance:a}),s-=n))}}}getEquidistantPointOnPath(t,e,r){if(this.buildEquidistantCache(e,r),t<0||t-this.getPathLength()>5e-5)return null;var i=Math.round(t/this.getPathLength()*(this.equidistantCache.points.length-1));return this.equidistantCache.points[i]||null}}var ne=/^\s*data:(([^/,;]+\/[^/,;]+)(?:;([^,;=]+=[^,;=]+))?)?(?:;(base64))?,(.*)$/i;class se extends At{constructor(t,e,r){super(t,e,r),this.type="image",this.loaded=!1;var i=this.getHrefAttribute().getString();if(i){var n=i.endsWith(".svg")||/^\s*data:image\/svg\+xml/i.test(i);t.images.push(this),n?this.loadSvg(i):this.loadImage(i),this.isSvg=n}}loadImage(t){var e=this;return s()(function*(){try{var r=yield e.document.createImage(t);e.image=r}catch(t){}e.loaded=!0})()}loadSvg(t){var e=this;return s()(function*(){var r=ne.exec(t);if(r){var i=r[5];"base64"===r[4]?e.image=atob(i):e.image=decodeURIComponent(i)}else try{var n=yield(yield e.document.fetch(t)).text();e.image=n}catch(t){}e.loaded=!0})()}renderChildren(t){var{document:e,image:r,loaded:i}=this,n=this.getAttribute("x").getPixels("x"),s=this.getAttribute("y").getPixels("y"),a=this.getStyle("width").getPixels("x"),o=this.getStyle("height").getPixels("y");if(i&&r&&a&&o){if(t.save(),t.translate(n,s),this.isSvg){var u=e.canvg.forkString(t,this.image,{ignoreMouse:!0,ignoreAnimation:!0,ignoreDimensions:!0,ignoreClear:!0,offsetX:0,offsetY:0,scaleWidth:a,scaleHeight:o});u.document.documentElement.parent=this,u.render()}else{var c=this.image;e.setViewBox({ctx:t,aspectRatio:this.getAttribute("preserveAspectRatio").getString(),width:a,desiredWidth:c.width,height:o,desiredHeight:c.height}),this.loaded&&(void 0===c.complete||c.complete)&&t.drawImage(c,0,0)}t.restore()}}getBoundingBox(){var t=this.getAttribute("x").getPixels("x"),e=this.getAttribute("y").getPixels("y"),r=this.getStyle("width").getPixels("x"),i=this.getStyle("height").getPixels("y");return new Tt(t,e,t+r,e+i)}}class ae extends At{constructor(){super(...arguments),this.type="symbol"}render(t){}}class oe{constructor(t){this.document=t,this.loaded=!1,t.fonts.push(this)}load(t,e){var r=this;return s()(function*(){try{var{document:i}=r,n=(yield i.canvg.parser.load(e)).getElementsByTagName("font");Array.from(n).forEach(e=>{var r=i.createElement(e);i.definitions[t]=r})}catch(t){}r.loaded=!0})()}}class ue extends bt{constructor(t,e,r){super(t,e,r),this.type="style",C(Array.from(e.childNodes).map(t=>t.textContent).join("").replace(/(\/\*([^*]|[\r\n]|(\*+([^*/]|[\r\n])))*\*+\/)|(^[\s]*\/\/.*)/gm,"").replace(/@import.*;/g,"")).split("}").forEach(e=>{var r=e.trim();if(r){var i=r.split("{"),n=i[0].split(","),s=i[1].split(";");n.forEach(e=>{var r=e.trim();if(r){var i=t.styles[r]||{};if(s.forEach(e=>{var r=e.indexOf(":"),n=e.substr(0,r).trim(),s=e.substr(r+1,e.length-r).trim();n&&s&&(i[n]=new rt(t,n,s))}),t.styles[r]=i,t.stylesSpecificity[r]=F(r),"@font-face"===r){var n=i["font-family"].getString().replace(/"|'/g,"");i.src.getString().split(",").forEach(e=>{if(e.indexOf('format("svg")')>0){var r=k(e);r&&new oe(t).load(n,r)}})}}})}})}}ue.parseExternalUrl=k;class ce extends At{constructor(){super(...arguments),this.type="use"}setContext(t){super.setContext(t);var e=this.getAttribute("x"),r=this.getAttribute("y");e.hasValue()&&t.translate(e.getPixels("x"),0),r.hasValue()&&t.translate(0,r.getPixels("y"))}path(t){var{element:e}=this;e&&e.path(t)}renderChildren(t){var{document:e,element:r}=this;if(r){var i=r;if("symbol"===r.type&&((i=new Rt(e,null)).attributes.viewBox=new rt(e,"viewBox",r.getAttribute("viewBox").getString()),i.attributes.preserveAspectRatio=new rt(e,"preserveAspectRatio",r.getAttribute("preserveAspectRatio").getString()),i.attributes.overflow=new rt(e,"overflow",r.getAttribute("overflow").getString()),i.children=r.children,r.styles.opacity=new rt(e,"opacity",this.calculateOpacity())),"svg"===i.type){var n=this.getStyle("width",!1,!0),s=this.getStyle("height",!1,!0);n.hasValue()&&(i.attributes.width=new rt(e,"width",n.getString())),s.hasValue()&&(i.attributes.height=new rt(e,"height",s.getString()))}var a=i.parent;i.parent=this,i.render(t),i.parent=a}}getBoundingBox(t){var{element:e}=this;return e?e.getBoundingBox(t):null}elementTransform(){var{document:t,element:e}=this;return xt.fromElement(t,e)}get element(){return this.cachedElement||(this.cachedElement=this.getHrefAttribute().getDefinition()),this.cachedElement}}function he(t,e,r,i,n,s){return t[r*i*4+4*e+s]}function le(t,e,r,i,n,s,a){t[r*i*4+4*e+s]=a}function fe(t,e,r){return t[e]*r}function pe(t,e,r,i){return e+Math.cos(t)*r+Math.sin(t)*i}class ge extends bt{constructor(t,e,r){super(t,e,r),this.type="feColorMatrix";var i=V(this.getAttribute("values").getString());switch(this.getAttribute("type").getString("matrix")){case"saturate":var n=i[0];i=[.213+.787*n,.715-.715*n,.072-.072*n,0,0,.213-.213*n,.715+.285*n,.072-.072*n,0,0,.213-.213*n,.715-.715*n,.072+.928*n,0,0,0,0,0,1,0,0,0,0,0,1];break;case"hueRotate":var s=i[0]*Math.PI/180;i=[pe(s,.213,.787,-.213),pe(s,.715,-.715,-.715),pe(s,.072,-.072,.928),0,0,pe(s,.213,-.213,.143),pe(s,.715,.285,.14),pe(s,.072,-.072,-.283),0,0,pe(s,.213,-.213,-.787),pe(s,.715,-.715,.715),pe(s,.072,.928,.072),0,0,0,0,0,1,0,0,0,0,0,1];break;case"luminanceToAlpha":i=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,.2125,.7154,.0721,0,0,0,0,0,0,1]}this.matrix=i,this.includeOpacity=this.getAttribute("includeOpacity").hasValue()}apply(t,e,r,i,n){for(var{includeOpacity:s,matrix:a}=this,o=t.getImageData(0,0,i,n),u=0;u<n;u++)for(var c=0;c<i;c++){var h=he(o.data,c,u,i,0,0),l=he(o.data,c,u,i,0,1),f=he(o.data,c,u,i,0,2),p=he(o.data,c,u,i,0,3),g=fe(a,0,h)+fe(a,1,l)+fe(a,2,f)+fe(a,3,p)+fe(a,4,1),d=fe(a,5,h)+fe(a,6,l)+fe(a,7,f)+fe(a,8,p)+fe(a,9,1),v=fe(a,10,h)+fe(a,11,l)+fe(a,12,f)+fe(a,13,p)+fe(a,14,1),y=fe(a,15,h)+fe(a,16,l)+fe(a,17,f)+fe(a,18,p)+fe(a,19,1);s&&(g=0,d=0,v=0,y*=p/255),le(o.data,c,u,i,0,0,g),le(o.data,c,u,i,0,1,d),le(o.data,c,u,i,0,2,v),le(o.data,c,u,i,0,3,y)}t.clearRect(0,0,i,n),t.putImageData(o,0,0)}}class de extends bt{constructor(){super(...arguments),this.type="mask"}apply(t,e){var{document:r}=this,i=this.getAttribute("x").getPixels("x"),n=this.getAttribute("y").getPixels("y"),s=this.getStyle("width").getPixels("x"),a=this.getStyle("height").getPixels("y");if(!s&&!a){var o=new Tt;this.children.forEach(e=>{o.addBoundingBox(e.getBoundingBox(t))}),i=Math.floor(o.x1),n=Math.floor(o.y1),s=Math.floor(o.width),a=Math.floor(o.height)}var u=this.removeStyles(e,de.ignoreStyles),c=r.createCanvas(i+s,n+a),h=c.getContext("2d");r.screen.setDefaults(h),this.renderChildren(h),new ge(r,{nodeType:1,childNodes:[],attributes:[{nodeName:"type",value:"luminanceToAlpha"},{nodeName:"includeOpacity",value:"true"}]}).apply(h,0,0,i+s,n+a);var l=r.createCanvas(i+s,n+a),f=l.getContext("2d");r.screen.setDefaults(f),e.render(f),f.globalCompositeOperation="destination-in",f.fillStyle=h.createPattern(c,"no-repeat"),f.fillRect(0,0,i+s,n+a),t.fillStyle=f.createPattern(l,"no-repeat"),t.fillRect(0,0,i+s,n+a),this.restoreStyles(e,u)}render(t){}}de.ignoreStyles=["mask","transform","clip-path"];var ve=()=>{};class ye extends bt{constructor(){super(...arguments),this.type="clipPath"}apply(t){var{document:e}=this,r=Reflect.getPrototypeOf(t),{beginPath:i,closePath:n}=t;r&&(r.beginPath=ve,r.closePath=ve),Reflect.apply(i,t,[]),this.children.forEach(i=>{if(void 0!==i.path){var s=void 0!==i.elementTransform?i.elementTransform():null;s||(s=xt.fromElement(e,i)),s&&s.apply(t),i.path(t),r&&(r.closePath=n),s&&s.unapply(t)}}),Reflect.apply(n,t,[]),t.clip(),r&&(r.beginPath=i,r.closePath=n)}render(t){}}class me extends bt{constructor(){super(...arguments),this.type="filter"}apply(t,e){var{document:r,children:i}=this,n=e.getBoundingBox(t);if(n){var s=0,a=0;i.forEach(t=>{var e=t.extraFilterDistance||0;s=Math.max(s,e),a=Math.max(a,e)});var o=Math.floor(n.width),u=Math.floor(n.height),c=o+2*s,h=u+2*a;if(!(c<1||h<1)){var l=Math.floor(n.x),f=Math.floor(n.y),p=this.removeStyles(e,me.ignoreStyles),g=r.createCanvas(c,h),d=g.getContext("2d");r.screen.setDefaults(d),d.translate(-l+s,-f+a),e.render(d),i.forEach(t=>{"function"==typeof t.apply&&t.apply(d,0,0,c,h)}),t.drawImage(g,0,0,c,h,l-s,f-a,c,h),this.restoreStyles(e,p)}}}render(t){}}me.ignoreStyles=["filter","transform","clip-path"];class xe extends bt{constructor(t,e,r){super(t,e,r),this.type="feDropShadow",this.addStylesFromStyleDefinition()}apply(t,e,r,i,n){}}class be extends bt{constructor(){super(...arguments),this.type="feMorphology"}apply(t,e,r,i,n){}}class we extends bt{constructor(){super(...arguments),this.type="feComposite"}apply(t,e,r,i,n){}}class Se extends bt{constructor(t,e,r){super(t,e,r),this.type="feGaussianBlur",this.blurRadius=Math.floor(this.getAttribute("stdDeviation").getNumber()),this.extraFilterDistance=this.blurRadius}apply(t,e,r,i,n){var{document:s,blurRadius:a}=this,o=s.window?s.window.document.body:null,u=t.canvas;u.id=s.getUniqueId(),o&&(u.style.display="none",o.appendChild(u)),Object(A.a)(u,e,r,i,n,a),o&&o.removeChild(u)}}class Pe extends bt{constructor(){super(...arguments),this.type="title"}}class Te extends bt{constructor(){super(...arguments),this.type="desc"}}var Oe={svg:Rt,rect:It,circle:kt,ellipse:_t,line:Lt,polyline:Dt,polygon:jt,path:Et,pattern:Bt,marker:qt,defs:Ut,linearGradient:Ft,radialGradient:Ht,stop:Yt,animate:Gt,animateColor:Wt,animateTransform:Qt,font:Zt,"font-face":Jt,"missing-glyph":$t,glyph:Ct,text:Mt,tspan:Nt,tref:Kt,a:te,textPath:ie,image:se,g:zt,symbol:ae,style:ue,use:ce,mask:de,clipPath:ye,filter:me,feDropShadow:xe,feMorphology:be,feComposite:we,feColorMatrix:ge,feGaussianBlur:Se,title:Pe,desc:Te};function Ae(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);e&&(i=i.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,i)}return r}function Ee(){return(Ee=s()(function*(t){var e=arguments.length>1&&void 0!==arguments[1]&&arguments[1],r=document.createElement("img");return e&&(r.crossOrigin="Anonymous"),new Promise((e,i)=>{r.onload=(()=>{e(r)}),r.onerror=((t,e,r,n,s)=>{i(s)}),r.src=t})})).apply(this,arguments)}class Ce{constructor(t){var{rootEmSize:e=12,emSize:r=12,createCanvas:i=Ce.createCanvas,createImage:n=Ce.createImage,anonymousCrossOrigin:s}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};this.canvg=t,this.definitions=Object.create(null),this.styles=Object.create(null),this.stylesSpecificity=Object.create(null),this.images=[],this.fonts=[],this.emSizeStack=[],this.uniqueId=0,this.screen=t.screen,this.rootEmSize=e,this.emSize=r,this.createCanvas=i,this.createImage=this.bindCreateImage(n,s),this.screen.wait(this.isImagesLoaded.bind(this)),this.screen.wait(this.isFontsLoaded.bind(this))}bindCreateImage(t,e){return"boolean"==typeof e?(r,i)=>t(r,"boolean"==typeof i?i:e):t}get window(){return this.screen.window}get fetch(){return this.screen.fetch}get ctx(){return this.screen.ctx}get emSize(){var{emSizeStack:t}=this;return t[t.length-1]}set emSize(t){var{emSizeStack:e}=this;e.push(t)}popEmSize(){var{emSizeStack:t}=this;t.pop()}getUniqueId(){return"canvg".concat(++this.uniqueId)}isImagesLoaded(){return this.images.every(t=>t.loaded)}isFontsLoaded(){return this.fonts.every(t=>t.loaded)}createDocumentElement(t){var e=this.createElement(t.documentElement);return e.root=!0,e.addStylesFromStyleDefinition(),this.documentElement=e,e}createElement(t){var e=t.nodeName.replace(/^[^:]+:/,""),r=Ce.elementTypes[e];return void 0!==r?new r(this,t):new wt(this,t)}createTextNode(t){return new Vt(this,t)}setViewBox(t){this.screen.setViewBox(function(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?Ae(Object(r),!0).forEach(function(e){f()(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):Ae(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}({document:this},t))}}function Me(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);e&&(i=i.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,i)}return r}function Ne(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?Me(Object(r),!0).forEach(function(e){f()(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):Me(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}Ce.createCanvas=function(t,e){var r=document.createElement("canvas");return r.width=t,r.height=e,r},Ce.createImage=function(t){return Ee.apply(this,arguments)},Ce.elementTypes=Oe;class Ve{constructor(t,e){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};this.parser=new lt(r),this.screen=new ut(t,r),this.options=r;var i=new Ce(this,r),n=i.createDocumentElement(e);this.document=i,this.documentElement=n}static from(t,e){var r=arguments;return s()(function*(){var i=r.length>2&&void 0!==r[2]?r[2]:{},n=yield new lt(i).parse(e);return new Ve(t,n,i)})()}static fromString(t,e){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},i=new lt(r).parseFromString(e);return new Ve(t,i,r)}fork(t,e){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};return Ve.from(t,e,Ne(Ne({},this.options),r))}forkString(t,e){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};return Ve.fromString(t,e,Ne(Ne({},this.options),r))}ready(){return this.screen.ready()}isReady(){return this.screen.isReady()}render(){var t=arguments,e=this;return s()(function*(){var r=t.length>0&&void 0!==t[0]?t[0]:{};e.start(Ne({enableRedraw:!0,ignoreAnimation:!0,ignoreMouse:!0},r)),yield e.ready(),e.stop()})()}start(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},{documentElement:e,screen:r,options:i}=this;r.start(e,Ne(Ne({enableRedraw:!0},i),t))}stop(){this.screen.stop()}resize(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:t,r=arguments.length>2&&void 0!==arguments[2]&&arguments[2];this.documentElement.resize(t,e,r)}}}.call(e,r("RvN7"))},yN9v:function(t,e,r){"use strict";var i=r("cJVG"),n=r("G8u6"),s=r("TkC3"),a=r("J8qN");t.exports=function(t,e,r){for(var o=n(e),u=a.f,c=s.f,h=0;h<o.length;h++){var l=o[h];i(t,l)||r&&i(r,l)||u(t,l,c(e,l))}}},yXmV:function(t,e){t.exports=function(t){this.ok=!1,this.alpha=1,"#"==t.charAt(0)&&(t=t.substr(1,6)),t=(t=t.replace(/ /g,"")).toLowerCase();var e={aliceblue:"f0f8ff",antiquewhite:"faebd7",aqua:"00ffff",aquamarine:"7fffd4",azure:"f0ffff",beige:"f5f5dc",bisque:"ffe4c4",black:"000000",blanchedalmond:"ffebcd",blue:"0000ff",blueviolet:"8a2be2",brown:"a52a2a",burlywood:"deb887",cadetblue:"5f9ea0",chartreuse:"7fff00",chocolate:"d2691e",coral:"ff7f50",cornflowerblue:"6495ed",cornsilk:"fff8dc",crimson:"dc143c",cyan:"00ffff",darkblue:"00008b",darkcyan:"008b8b",darkgoldenrod:"b8860b",darkgray:"a9a9a9",darkgreen:"006400",darkkhaki:"bdb76b",darkmagenta:"8b008b",darkolivegreen:"556b2f",darkorange:"ff8c00",darkorchid:"9932cc",darkred:"8b0000",darksalmon:"e9967a",darkseagreen:"8fbc8f",darkslateblue:"483d8b",darkslategray:"2f4f4f",darkturquoise:"00ced1",darkviolet:"9400d3",deeppink:"ff1493",deepskyblue:"00bfff",dimgray:"696969",dodgerblue:"1e90ff",feldspar:"d19275",firebrick:"b22222",floralwhite:"fffaf0",forestgreen:"228b22",fuchsia:"ff00ff",gainsboro:"dcdcdc",ghostwhite:"f8f8ff",gold:"ffd700",goldenrod:"daa520",gray:"808080",green:"008000",greenyellow:"adff2f",honeydew:"f0fff0",hotpink:"ff69b4",indianred:"cd5c5c",indigo:"4b0082",ivory:"fffff0",khaki:"f0e68c",lavender:"e6e6fa",lavenderblush:"fff0f5",lawngreen:"7cfc00",lemonchiffon:"fffacd",lightblue:"add8e6",lightcoral:"f08080",lightcyan:"e0ffff",lightgoldenrodyellow:"fafad2",lightgrey:"d3d3d3",lightgreen:"90ee90",lightpink:"ffb6c1",lightsalmon:"ffa07a",lightseagreen:"20b2aa",lightskyblue:"87cefa",lightslateblue:"8470ff",lightslategray:"778899",lightsteelblue:"b0c4de",lightyellow:"ffffe0",lime:"00ff00",limegreen:"32cd32",linen:"faf0e6",magenta:"ff00ff",maroon:"800000",mediumaquamarine:"66cdaa",mediumblue:"0000cd",mediumorchid:"ba55d3",mediumpurple:"9370d8",mediumseagreen:"3cb371",mediumslateblue:"7b68ee",mediumspringgreen:"00fa9a",mediumturquoise:"48d1cc",mediumvioletred:"c71585",midnightblue:"191970",mintcream:"f5fffa",mistyrose:"ffe4e1",moccasin:"ffe4b5",navajowhite:"ffdead",navy:"000080",oldlace:"fdf5e6",olive:"808000",olivedrab:"6b8e23",orange:"ffa500",orangered:"ff4500",orchid:"da70d6",palegoldenrod:"eee8aa",palegreen:"98fb98",paleturquoise:"afeeee",palevioletred:"d87093",papayawhip:"ffefd5",peachpuff:"ffdab9",peru:"cd853f",pink:"ffc0cb",plum:"dda0dd",powderblue:"b0e0e6",purple:"800080",rebeccapurple:"663399",red:"ff0000",rosybrown:"bc8f8f",royalblue:"4169e1",saddlebrown:"8b4513",salmon:"fa8072",sandybrown:"f4a460",seagreen:"2e8b57",seashell:"fff5ee",sienna:"a0522d",silver:"c0c0c0",skyblue:"87ceeb",slateblue:"6a5acd",slategray:"708090",snow:"fffafa",springgreen:"00ff7f",steelblue:"4682b4",tan:"d2b48c",teal:"008080",thistle:"d8bfd8",tomato:"ff6347",turquoise:"40e0d0",violet:"ee82ee",violetred:"d02090",wheat:"f5deb3",white:"ffffff",whitesmoke:"f5f5f5",yellow:"ffff00",yellowgreen:"9acd32"};t=e[t]||t;for(var r=[{re:/^rgba\((\d{1,3}),\s*(\d{1,3}),\s*(\d{1,3}),\s*((?:\d?\.)?\d)\)$/,example:["rgba(123, 234, 45, 0.8)","rgba(255,234,245,1.0)"],process:function(t){return[parseInt(t[1]),parseInt(t[2]),parseInt(t[3]),parseFloat(t[4])]}},{re:/^rgb\((\d{1,3}),\s*(\d{1,3}),\s*(\d{1,3})\)$/,example:["rgb(123, 234, 45)","rgb(255,234,245)"],process:function(t){return[parseInt(t[1]),parseInt(t[2]),parseInt(t[3])]}},{re:/^([0-9a-fA-F]{2})([0-9a-fA-F]{2})([0-9a-fA-F]{2})$/,example:["#00ff00","336699"],process:function(t){return[parseInt(t[1],16),parseInt(t[2],16),parseInt(t[3],16)]}},{re:/^([0-9a-fA-F]{1})([0-9a-fA-F]{1})([0-9a-fA-F]{1})$/,example:["#fb0","f0f"],process:function(t){return[parseInt(t[1]+t[1],16),parseInt(t[2]+t[2],16),parseInt(t[3]+t[3],16)]}}],i=0;i<r.length;i++){var n=r[i].re,s=r[i].process,a=n.exec(t);if(a){var o=s(a);this.r=o[0],this.g=o[1],this.b=o[2],o.length>3&&(this.alpha=o[3]),this.ok=!0}}this.r=this.r<0||isNaN(this.r)?0:this.r>255?255:this.r,this.g=this.g<0||isNaN(this.g)?0:this.g>255?255:this.g,this.b=this.b<0||isNaN(this.b)?0:this.b>255?255:this.b,this.alpha=this.alpha<0?0:this.alpha>1||isNaN(this.alpha)?1:this.alpha,this.toRGB=function(){return"rgb("+this.r+", "+this.g+", "+this.b+")"},this.toRGBA=function(){return"rgba("+this.r+", "+this.g+", "+this.b+", "+this.alpha+")"},this.toHex=function(){var t=this.r.toString(16),e=this.g.toString(16),r=this.b.toString(16);return 1==t.length&&(t="0"+t),1==e.length&&(e="0"+e),1==r.length&&(r="0"+r),"#"+t+e+r},this.getHelpXML=function(){for(var t=new Array,i=0;i<r.length;i++)for(var n=r[i].example,s=0;s<n.length;s++)t[t.length]=n[s];for(var a in e)t[t.length]=a;var o=document.createElement("ul");o.setAttribute("id","rgbcolor-examples");for(i=0;i<t.length;i++)try{var u=document.createElement("li"),c=new RGBColor(t[i]),h=document.createElement("div");h.style.cssText="margin: 3px; border: 1px solid black; background:"+c.toHex()+"; color:"+c.toHex(),h.appendChild(document.createTextNode("test"));var l=document.createTextNode(" "+t[i]+" -> "+c.toRGB()+" -> "+c.toHex());u.appendChild(h),u.appendChild(l),o.appendChild(u)}catch(t){}return o}}},yc0e:function(t,e,r){"use strict";var i=r("PIjx"),n=r("q64P"),s=r("n+zG"),a=i(Function.toString);n(s.inspectSource)||(s.inspectSource=function(t){return a(t)}),t.exports=s.inspectSource},"yv+W":function(t,e,r){"use strict";var i=r("YrDN");t.exports=Array.isArray||function(t){return"Array"===i(t)}}});