webpackJsonp([5],{FP3a:function(t,i,a){"use strict";Object.defineProperty(i,"__esModule",{value:!0});var s={name:"ChinaMap",data:function(){return{attackLines:[{x1:50,y1:50,x2:70,y2:30,color:"#ff6347"},{x1:60,y1:80,x2:40,y2:60,color:"#4db1ff"},{x1:30,y1:40,x2:50,y2:70,color:"#ffcc00"},{x1:70,y1:60,x2:30,y2:30,color:"#00ff7f"}],cities:[{x:50,y:30,value:90},{x:70,y:40,value:60},{x:30,y:50,value:30},{x:60,y:70,value:50},{x:40,y:60,value:80}]}},methods:{getLineStyle:function(t){return{left:"".concat(t.x1,"%"),top:"".concat(t.y1,"%"),width:"".concat(Math.sqrt(Math.pow(t.x2-t.x1,2)+Math.pow(t.y2-t.y1,2)),"%"),transform:"rotate(".concat(180*Math.atan2(t.y2-t.y1,t.x2-t.x1)/Math.PI,"deg)"),transformOrigin:"left center",backgroundColor:t.color}},getPointStyle:function(t){return{backgroundColor:t}},getCityStyle:function(t){return{left:"".concat(t.x,"%"),top:"".concat(t.y,"%")}}}},e={render:function(){var t=this,i=t.$createElement,a=t._self._c||i;return a("div",{staticClass:"map-wrapper"},[a("div",{staticClass:"map-outline"},[a("svg",{attrs:{viewBox:"0 0 1000 800",xmlns:"http://www.w3.org/2000/svg"}},[a("path",{attrs:{d:"M650,200 Q800,250 750,400 Q720,500 600,550 Q450,600 300,450 Q250,350 350,250 Q450,180 650,200 Z",fill:"none",stroke:"#1e3c6e","stroke-width":"2"}})]),t._v(" "),t._l(t.attackLines,function(i,s){return a("div",{key:s,staticClass:"attack-line",style:t.getLineStyle(i)},[a("div",{staticClass:"attack-point",style:t.getPointStyle(i.color)})])}),t._v(" "),t._l(t.cities,function(i,s){return a("div",{key:s,staticClass:"city-point",style:t.getCityStyle(i)},[a("div",{staticClass:"city-label"},[t._v(t._s(i.value))])])})],2)])},staticRenderFns:[]};var n={name:"HomePage",components:{ChinaMap:a("owSs")(s,e,!1,function(t){a("UDsa")},"data-v-064fb191",null).exports},data:function(){return{currentTime:"",timer:null}},created:function(){document.body.classList.add("home-page"),this.updateTime(),this.timer=setInterval(this.updateTime,1e3)},beforeDestroy:function(){document.body.classList.remove("home-page"),this.timer&&clearInterval(this.timer)},methods:{isLoggedIn:function(){return this.$cookies&&this.$cookies.isKey("token")},checkLogin:function(t){this.isLoggedIn()?this.$router.push(t):this.goToLogin()},goToSoar:function(){this.checkLogin("/dashboard")},goToLogin:function(){this.$router.push("/login")},updateTime:function(){var t=new Date,i=t.getFullYear(),a=String(t.getMonth()+1).padStart(2,"0"),s=String(t.getDate()).padStart(2,"0"),e=String(t.getHours()).padStart(2,"0"),n=String(t.getMinutes()).padStart(2,"0"),c=String(t.getSeconds()).padStart(2,"0");this.currentTime="".concat(i,"-").concat(a,"-").concat(s," ").concat(e,":").concat(n,":").concat(c)}}},c={render:function(){var t=this,i=t.$createElement,a=t._self._c||i;return a("div",{staticClass:"dashboard full-page"},[a("header",{staticClass:"header"},[a("div",{staticClass:"nav-left"},[a("div",{staticClass:"nav-item"},[t._v("首页")]),t._v(" "),a("div",{staticClass:"nav-item dropdown"},[t._v("\n        网络攻击预警 "),a("span",{staticClass:"arrow"},[t._v("▼")]),t._v(" "),a("div",{staticClass:"dropdown-content"},[a("div",{on:{click:function(i){return t.checkLogin("/network-alerts")}}},[t._v("网络攻击预警")]),t._v(" "),a("div",{on:{click:function(i){return t.checkLogin("/vulnerability")}}},[t._v("脆弱性态势")]),t._v(" "),a("div",{on:{click:function(i){return t.checkLogin("/threats")}}},[t._v("威胁态势")]),t._v(" "),a("div",{on:{click:function(i){return t.checkLogin("/security-operations")}}},[t._v("安全运营态势")])])])]),t._v(" "),t._m(0),t._v(" "),a("div",{staticClass:"nav-right"},[a("div",{staticClass:"nav-item",on:{click:function(i){return t.checkLogin("/dashboard")}}},[t._v("安全编排")]),t._v(" "),a("div",{staticClass:"nav-item",on:{click:function(i){return t.checkLogin("/intrusion-detection")}}},[t._v("入侵检测")]),t._v(" "),a("div",{staticClass:"nav-item",on:{click:function(i){return t.checkLogin("/intelligent-operations")}}},[t._v("智能运营")]),t._v(" "),a("div",{staticClass:"nav-item",on:{click:function(i){return t.checkLogin("/system")}}},[t._v("系统管理")]),t._v(" "),a("div",{staticClass:"user-icon",on:{click:t.goToLogin}},[t._v("👤")])])]),t._v(" "),a("main",{staticClass:"content"},[t._m(1),t._v(" "),a("div",{staticClass:"center-panel"},[a("div",{staticClass:"time-bar"},[a("span",[t._v("时间")]),t._v(" "),a("div",{staticClass:"current-time"},[t._v(t._s(t.currentTime))]),t._v(" "),a("div",{staticClass:"time-selector"})]),t._v(" "),a("div",{staticClass:"map-area"},[a("h3",{staticClass:"section-label"},[t._v("攻击态势")]),t._v(" "),a("div",{staticClass:"china-map"},[a("china-map")],1)]),t._v(" "),t._m(2),t._v(" "),t._m(3)]),t._v(" "),t._m(4)])])},staticRenderFns:[function(){var t=this.$createElement,i=this._self._c||t;return i("div",{staticClass:"platform-title"},[i("div",{staticClass:"title-graphic"},[i("div",{staticClass:"trapezoid"}),this._v(" "),i("div",{staticClass:"title-text"},[this._v("智能安全运营平台")]),this._v(" "),i("div",{staticClass:"trapezoid right"})]),this._v(" "),i("div",{staticClass:"title-underline"})])},function(){var t=this,i=t.$createElement,a=t._self._c||i;return a("div",{staticClass:"left-panel"},[a("div",{staticClass:"data-box threat-box"},[a("h3",[t._v("威胁数据")]),t._v(" "),a("div",{staticClass:"data-content"})]),t._v(" "),a("div",{staticClass:"data-box asset-box"},[a("h3",[t._v("资产数据")]),t._v(" "),a("div",{staticClass:"data-content"})]),t._v(" "),a("div",{staticClass:"data-box operation-box"},[a("h3",[t._v("安全运营数据")]),t._v(" "),a("div",{staticClass:"data-content"},[a("div",{staticClass:"metric-grid"},[a("div",{staticClass:"metric"},[t._v("告警量")]),t._v(" "),a("div",{staticClass:"metric"},[t._v("事件量")]),t._v(" "),a("div",{staticClass:"metric"},[t._v("自动处量")]),t._v(" "),a("div",{staticClass:"metric"},[t._v("手动处量")])])])])])},function(){var t=this.$createElement,i=this._self._c||t;return i("div",{staticClass:"risk-bar"},[i("h3",{staticClass:"section-label"},[this._v("高危风险")])])},function(){var t=this.$createElement,i=this._self._c||t;return i("div",{staticClass:"attack-alerts"},[i("h3",{staticClass:"section-label"},[this._v("网络攻击预警")])])},function(){var t=this.$createElement,i=this._self._c||t;return i("div",{staticClass:"right-panel"},[i("div",{staticClass:"data-box vulnerability-box"},[i("h3",[this._v("漏洞数据")]),this._v(" "),i("div",{staticClass:"data-content"})]),this._v(" "),i("div",{staticClass:"data-box device-box"},[i("h3",[this._v("网络设备状态")]),this._v(" "),i("div",{staticClass:"data-content"})])])}]};var o=a("owSs")(n,c,!1,function(t){a("G7Jf"),a("fVX8")},"data-v-64728c0b",null);i.default=o.exports},G7Jf:function(t,i){},UDsa:function(t,i){},fVX8:function(t,i){}});