webpackJsonp([8],{"A+FA":function(t,e){},OlBJ:function(t,e,a){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var o=a("WW91"),n=[{title:"缩略图",key:"thumbnail",scopedSlots:{customRender:"thumbnail"},width:200},{title:"类型",key:"type",scopedSlots:{customRender:"type"},width:60},{title:"分类",dataIndex:"type_name",key:"type_name",scopedSlots:{customRender:"type_name"},width:116},{title:"剧本名称",dataIndex:"name",key:"name",scopedSlots:{customRender:"name"},width:260},{title:"备注",dataIndex:"remarks",key:"remarks",scopedSlots:{customRender:"remarks"}},{title:"创建人",dataIndex:"nick_name",key:"nick_name",scopedSlots:{customRender:"nick_name"},width:90},{title:"更新时间",key:"update_time",dataIndex:"update_time",scopedSlots:{customRender:"update_time"},width:190},{title:"状态",key:"status",dataIndex:"status",scopedSlots:{customRender:"status"},width:80},{title:"操作",key:"action",scopedSlots:{customRender:"action"},width:116}],r={name:"workflowHome",components:{AlarmClock:o.a,Textx:o.l,Ring:o.j,ConnectionPoint:o.c},data:function(){return{columns:n,loading:!1,visible_input_url:!1,data:[],so_text:"",select_type:"0",type_data:[],input_url:"",pagination:{total:0,defaultPageSize:10,showTotal:function(t){return"共 ".concat(t," 条数据")},showSizeChanger:!0,pageSizeOptions:["10","15","20","50","100"],onShowSizeChange:this.onPageShowSizeChange,onChange:this.onPageChange},curr_page:1,curr_page_size:10}},mounted:function(){this.onLoad()},methods:{onPageShowSizeChange:function(t,e){this.curr_page=t,this.curr_page_size=e,this.onLoad(this.so_text,this.select_type,t,e)},onPageChange:function(t,e){this.curr_page=t,this.curr_page_size=e,this.onLoad(this.so_text,this.select_type,t,e)},onLoad:function(){var t=this,e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"",a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"0",o=arguments.length>2&&void 0!==arguments[2]?arguments[2]:1,n=arguments.length>3&&void 0!==arguments[3]?arguments[3]:10;this.loading=!0,this.onSelectType(),this.$http.post("/api/v1/soar/get/workflow/list",{keywords:e,type:a,page:o,page_count:n}).then(function(e){0==e.code?(t.data=e.data.list,t.pagination.total=e.data.total_count,t.loading=!1):(t.$message.error(e.msg),t.loading=!1)})},onSaveUrlInput:function(){var t=this;this.$http.post("/api/v1/soar/get/workflow/import_url",{url:this.input_url}).then(function(e){if(0==e.code){var a=e.data.data;t.addPlayBook(a),t.onCloseUrlInput()}else t.$message.error(e.msg)})},onCloseUrlInput:function(){this.visible_input_url=!1},onDown:function(t){var e=this;this.$http.post("/api/v1/soar/post/workflow/detail",{uuid:t}).then(function(t){if(0==t.code){var a,o,n,r,s=t.data.name,i=t.data.remarks;a=""===t.data.flow_json.trim()?{nodes:[],edges:[]}:JSON.parse(t.data.flow_json),o="none"===t.data.flow_data||""===t.data.flow_data.trim()||"{}"===t.data.flow_data.trim()?{}:JSON.parse(t.data.flow_data);var l,p=t.data.start_app,d=t.data.end_app,c=t.data.input_app,u=t.data.webhook_app,_=t.data.timer_app,h=t.data.for_list,m=t.data.if_list,f=t.data.audit_list;n="none"===t.data.local_var_data||""===t.data.local_var_data.trim()||"[]"===t.data.local_var_data.trim()?[]:JSON.parse(t.data.local_var_data),r="none"===t.data.controller_data||""===t.data.controller_data.trim()||"{}"===t.data.controller_data.trim()?{}:JSON.parse(t.data.controller_data),l=""==i.trim()?s:s+"-"+i;var g={name:s,remarks:i,start_app:p,end_app:d,input_app:c,webhook_app:u,timer_app:_,for_list:h,if_list:m,audit_list:f,flow_json:a,flow_data:o,local_var_data:n,controller_data:r,grid_type:t.data.grid_type,edge_marker:t.data.edge_marker,edge_color:t.data.edge_color,edge_connector:t.data.edge_connector,edge_router:t.data.edge_router,thumbnail:t.data.thumbnail},y=document.createElement("a");y.download=l+".json",y.style.display="none";var v=new Blob([JSON.stringify(g)],{type:"application/json"});y.href=URL.createObjectURL(v),document.body.appendChild(y),y.click(),document.body.removeChild(y)}else e.$message.error(t.msg)})},handleMenuClick:function(t){1==t.key?this.$refs.inputer.click():2==t.key&&(this.visible_input_url=!0)},upload_json:function(t){var e=this,a=t.target,o=new FileReader;o.readAsText(a.files[0],"utf8"),o.onload=function(){try{var t=JSON.parse(o.result);e.addPlayBook(t)}catch(t){return e.$message.error("导入失败，请检测 W5 JSON 文件是否正确"),!1}}},addPlayBook:function(t){var e=this;if(!(t.hasOwnProperty("name")&&t.hasOwnProperty("remarks")&&t.hasOwnProperty("start_app")&&t.hasOwnProperty("end_app")&&t.hasOwnProperty("input_app")&&t.hasOwnProperty("webhook_app")&&t.hasOwnProperty("timer_app")&&t.hasOwnProperty("flow_json")&&t.hasOwnProperty("flow_data")&&t.hasOwnProperty("controller_data")&&t.hasOwnProperty("local_var_data")&&t.hasOwnProperty("grid_type")&&t.hasOwnProperty("edge_marker")&&t.hasOwnProperty("edge_color")&&t.hasOwnProperty("edge_connector")&&t.hasOwnProperty("edge_router")&&t.hasOwnProperty("for_list")&&t.hasOwnProperty("if_list")&&t.hasOwnProperty("audit_list")&&t.hasOwnProperty("thumbnail")))return this.$message.error("非法格式,请检查是否为 W5 SOAR 专用 JSON 文件"),!1;for(var a=t.flow_json.cells,o=0;o<a.length;o++)if("html"===a.shape){var n=a[o].data.icon.replace(/^http:\/\/[^/]+/,"").replace(/^https:\/\/[^/]+/,""),r=this.BaseURL+n;a[o].data.icon=r}this.$http.post("/api/v1/soar/post/workflow/add",{type:1,name:t.name,remarks:t.remarks,start_app:t.start_app,end_app:t.end_app,input_app:t.input_app,webhook_app:t.webhook_app,timer_app:t.timer_app,for_list:t.for_list,if_list:t.if_list,audit_list:t.audit_list,flow_json:JSON.stringify(t.flow_json),flow_data:JSON.stringify(t.flow_data),controller_data:JSON.stringify(t.controller_data),local_var_data:JSON.stringify(t.local_var_data),grid_type:t.grid_type,edge_marker:t.edge_marker,edge_color:t.edge_color,edge_connector:t.edge_connector,edge_router:t.edge_router,thumbnail:t.thumbnail}).then(function(t){if(0==t.code){var a=t.data.uuid;e.$router.push({name:"WorkflowEdit",params:{uuid:a}})}else e.$message.error(t.msg)})},onSelectType:function(){var t=this;this.$http.post("/api/v1/soar/get/type/list",{type:1}).then(function(e){0==e.code?t.type_data=e.data:t.$message.error(e.msg)})},del:function(t){var e=this;this.$http.post("/api/v1/soar/post/workflow/del",{uuid:t}).then(function(t){0==t.code?(e.$message.success("删除成功"),e.onLoad(e.so_text,e.select_type,e.curr_page,e.curr_page_size)):e.$message.error(t.msg)})},onSearch:function(t){this.so_text=t,this.onLoad(this.so_text,this.select_type)},onSelect:function(t){this.select_type=t,this.onLoad(this.so_text,this.select_type)},tzAddPlayBook:function(){var t=this;this.$http.post("/api/v1/soar/post/workflow/add",{type:0}).then(function(e){if(0==e.code){var a=e.data.uuid;t.$router.push({name:"WorkflowEdit",params:{uuid:a}})}else t.$message.error(e.msg)})},tzUpdatePlayBook:function(t){this.$router.push({name:"WorkflowEdit",params:{uuid:t}})},tzStatistics:function(t){this.$router.push({name:"StatisticsHome",params:{uuid:t}})},onSwitch:function(t,e){var a=this,o=1;t&&(o=0),this.$http.post("/api/v1/soar/post/workflow/status",{id:e,status:o}).then(function(t){0==t.code?(a.$message.success("操作成功"),a.onLoad(a.so_text,a.select_type,a.curr_page,a.curr_page_size)):a.$message.error(t.msg)})},onFilterOption:function(t,e){return e.componentOptions.children[0].text.toLowerCase().indexOf(t.toLowerCase())>=0}}},s={render:function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("a-layout-content",[a("div",{staticClass:"header_div"},[a("a-select",{staticClass:"align",staticStyle:{width:"120px"},attrs:{"show-search":"","filter-option":t.onFilterOption,"default-value":"0"},on:{change:t.onSelect}},[a("a-select-option",{attrs:{value:"0"}},[t._v("全部")]),t._v(" "),t._l(t.type_data,function(e,o){return a("a-select-option",{key:o,attrs:{value:e.id}},[t._v(t._s(e.name))])})],2),t._v(" "),a("a-input-search",{staticClass:"align",staticStyle:{width:"200px"},attrs:{placeholder:"请输入剧本名称"},on:{search:t.onSearch}}),t._v(" "),a("a-dropdown",[a("a-menu",{attrs:{slot:"overlay"},on:{click:t.handleMenuClick},slot:"overlay"},[a("a-menu-item",{key:"1"},[a("a-icon",{attrs:{type:"upload"}}),t._v(" 本地导入\n                ")],1),t._v(" "),a("a-menu-item",{key:"2"},[a("a-icon",{attrs:{type:"cloud-upload"}}),t._v(" 云端导入\n                ")],1)],1),t._v(" "),a("a-button",{staticClass:"align btn_add",attrs:{type:"primary",icon:"plus-circle"},on:{click:t.tzAddPlayBook}},[t._v("创建剧本\n                "),a("a-icon",{attrs:{type:"down"}})],1)],1)],1),t._v(" "),a("a-table",{attrs:{rowKey:"id",columns:t.columns,"data-source":t.data,loading:t.loading,pagination:t.pagination},scopedSlots:t._u([{key:"type",fn:function(e){return""!=e.timer_app?a("a-tag",{staticStyle:{"padding-top":"4px","line-height":"17px"},attrs:{color:"#3b9c30"}},[a("a-tooltip",{attrs:{placement:"top"}},[a("template",{slot:"title"},[a("span",[t._v("定时器剧本")])]),t._v(" "),a("alarm-clock",{attrs:{theme:"outline",size:"14",fill:"#fff"}})],2)],1):""!=e.webhook_app?a("a-tag",{staticStyle:{"padding-top":"4px","line-height":"17px"},attrs:{color:"#53b0b5"}},[a("a-tooltip",{attrs:{placement:"top"}},[a("template",{slot:"title"},[a("span",[t._v("WebHook剧本")])]),t._v(" "),a("ring",{attrs:{theme:"outline",size:"14",fill:"#fff"}})],2)],1):""!=e.input_app?a("a-tag",{staticStyle:{"padding-top":"4px","line-height":"17px"},attrs:{color:"#b628da"}},[a("a-tooltip",{attrs:{placement:"top"}},[a("template",{slot:"title"},[a("span",[t._v("用户输入剧本")])]),t._v(" "),a("textx",{attrs:{theme:"outline",size:"14",fill:"#fff"}})],2)],1):a("a-tag",{staticStyle:{"padding-top":"4px","line-height":"17px"},attrs:{slot:"type",color:"#355070"},slot:"type"},[a("a-tooltip",{attrs:{placement:"top"}},[a("template",{slot:"title"},[a("span",[t._v("普通剧本")])]),t._v(" "),a("connection-point",{attrs:{theme:"outline",size:"14",fill:"#fff"}})],2)],1)}},{key:"thumbnail",fn:function(e,o){return a("span",{},[o.thumbnail?a("div",{staticClass:"thumbnail",style:"background-color:#202020;background-image:url("+o.thumbnail+");background-repeat:no-repeat;background-position: inherit;\n    background-size: cover;\n    height: 100px;border-radius: 5px;"}):a("div",{staticClass:"thumbnail"},[t._v("\n                暂无缩略图\n            ")])])}},{key:"type_name",fn:function(e){return a("a-tag",{attrs:{color:"#cf3f3d"}},[t._v("\n            "+t._s(e)+"\n        ")])}},{key:"name",fn:function(e,o){return a("span",{},[a("b",[t._v(" "+t._s(e))])])}},{key:"nick_name",fn:function(e){return a("a-tag",{attrs:{color:"blue"}},[t._v("\n            "+t._s(e)+"\n        ")])}},{key:"update_time",fn:function(e){return a("span",{},[t._v("\n            "+t._s(t.Dayjs(e).subtract(8,"hour").format("YYYY-MM-DD HH:mm:ss"))+"\n        ")])}},{key:"create_time",fn:function(e){return a("span",{},[t._v("\n            "+t._s(t.Dayjs(e).subtract(8,"hour").format("YYYY-MM-DD HH:mm:ss"))+"\n        ")])}},{key:"remarks",fn:function(e){return a("span",{},[t._v("\n            "+t._s(e)+"\n        ")])}},{key:"status",fn:function(e,o){return a("div",{},[a("a-switch",0===e?{attrs:{"default-checked":""},on:{click:function(e){return t.onSwitch(e,o.id)}}}:{on:{click:function(e){return t.onSwitch(e,o.id)}}},[a("a-icon",{attrs:{slot:"checkedChildren",type:"check"},slot:"checkedChildren"}),t._v(" "),a("a-icon",{attrs:{slot:"unCheckedChildren",type:"close"},slot:"unCheckedChildren"})],1)],1)}},{key:"action",fn:function(e,o){return a("span",{},[a("a-space",{attrs:{size:"small"}},[a("a-tooltip",{attrs:{placement:"left"}},[a("template",{slot:"title"},[a("span",[t._v("删除")])]),t._v(" "),a("a-popconfirm",{attrs:{title:"是否要删除该剧本?","ok-text":"是","cancel-text":"否"},on:{confirm:function(e){return t.del(o.uuid)}}},[a("a-icon",{staticClass:"pointer",attrs:{type:"delete"}})],1)],2),t._v(" "),a("span",[t._v("|")]),t._v(" "),a("a-tooltip",{attrs:{placement:"top"}},[a("template",{slot:"title"},[a("span",[t._v("编辑")])]),t._v(" "),a("a-icon",{staticClass:"pointer",attrs:{type:"form"},on:{click:function(e){return t.tzUpdatePlayBook(o.uuid)}}})],2),t._v(" "),a("span",[t._v("|")]),t._v(" "),a("a-tooltip",{attrs:{placement:"top"}},[a("template",{slot:"title"},[a("span",[t._v("导出")])]),t._v(" "),a("a-icon",{staticClass:"pointer",attrs:{type:"cloud-download"},on:{click:function(e){return t.onDown(o.uuid)}}})],2)],1)],1)}}],null,!0)}),t._v(" "),a("a-modal",{attrs:{title:"云端导入",cancelText:"关闭",okText:"导入",width:600,visible:t.visible_input_url},on:{ok:t.onSaveUrlInput,cancel:t.onCloseUrlInput}},[a("a-row",{attrs:{gutter:16}},[a("a-col",{attrs:{span:24}},[a("a-input",{attrs:{placeholder:"请输入 W5 JSON 地址"},model:{value:t.input_url,callback:function(e){t.input_url=e},expression:"input_url"}},[a("a-icon",{attrs:{slot:"prefix",type:"global"},slot:"prefix"})],1)],1)],1)],1),t._v(" "),a("input",{ref:"inputer",staticStyle:{display:"none"},attrs:{type:"file",accept:"application/json"},on:{change:t.upload_json}})],1)},staticRenderFns:[]};var i=a("owSs")(r,s,!1,function(t){a("A+FA")},"data-v-eab16c96",null);e.default=i.exports}});