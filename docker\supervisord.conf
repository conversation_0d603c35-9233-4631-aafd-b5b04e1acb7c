[unix_http_server]
file=/tmp/supervisor.sock

[supervisord]
logfile=/tmp/supervisord.log
logfile_maxbytes=50MB
logfile_backups=10
loglevel=info
pidfile=/tmp/supervisord.pid
nodaemon=true
silent=false
minfds=1024
minprocs=200

[rpcinterface:supervisor]
supervisor.rpcinterface_factory = supervisor.rpcinterface:make_main_rpcinterface

[supervisorctl]
serverurl=unix:///tmp/supervisor.sock

[program:w5]
command = python run.py
directory = /w5
autostart = true
autorestart = true
redirect_stderr = true
stdout_logfile=/opt/w5.log
stdout_logfile_maxbytes = 20MB
stdout_logfile_backups = 20