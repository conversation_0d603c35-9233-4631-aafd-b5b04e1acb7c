#!/usr/bin/env python
# encoding:utf-8
"""
消息通知功能测试脚本
"""
import requests
import json

# 测试配置
BASE_URL = "http://localhost:8888"
TOKEN = "YOUR_TEST_TOKEN"  # 需要替换为实际的TOKEN

def test_notification_process():
    """测试消息处理接口"""
    print("=== 测试消息处理接口 ===")
    
    # 测试数据
    test_cases = [
        {
            "name": "S60000待处理通知",
            "data": {
                "script_type": "s60000_pending",
                "result": {"processed_count": 5}
            }
        },
        {
            "name": "邮件通知",
            "data": {
                "script_type": "email",
                "result": {"mails_count": 3}
            }
        },
        {
            "name": "奇安信告警通知",
            "data": {
                "script_type": "qianxin_ids",
                "result": {"total": 10}
            }
        },
        {
            "name": "绿盟漏扫通知",
            "data": {
                "script_type": "nsfocus_scan",
                "result": {"total_tasks": 2, "total_vulns": 8}
            }
        },
        {
            "name": "S60000反馈截止通知",
            "data": {
                "script_type": "s60000_feedback",
                "result": {"ddl_entry_count": 1}
            }
        },
        {
            "name": "无需通知的情况",
            "data": {
                "script_type": "s60000_pending",
                "result": {"processed_count": 0}
            }
        }
    ]
    
    for test_case in test_cases:
        print(f"\n测试: {test_case['name']}")
        
        response = requests.post(
            f"{BASE_URL}/api/v1/soar/notification/process",
            json=test_case['data'],
            headers={
                "Content-Type": "application/json",
                "Authorization": f"Bearer {TOKEN}"
            }
        )
        
        print(f"状态码: {response.status_code}")
        print(f"响应: {json.dumps(response.json(), ensure_ascii=False, indent=2)}")


def test_notification_list():
    """测试通知列表查询接口"""
    print("\n=== 测试通知列表查询接口 ===")
    
    response = requests.post(
        f"{BASE_URL}/api/v1/soar/notification/list",
        json={
            "page": 1,
            "limit": 10,
            "status": "all"
        },
        headers={
            "Content-Type": "application/json",
            "Authorization": f"Bearer {TOKEN}"
        }
    )
    
    print(f"状态码: {response.status_code}")
    print(f"响应: {json.dumps(response.json(), ensure_ascii=False, indent=2)}")


def test_notification_status():
    """测试通知状态更新接口"""
    print("\n=== 测试通知状态更新接口 ===")
    
    # 这里需要一个实际的notification_id
    notification_id = "test-notification-id"
    
    response = requests.put(
        f"{BASE_URL}/api/v1/soar/notification/status",
        json={
            "notification_id": notification_id,
            "status": "read"
        },
        headers={
            "Content-Type": "application/json",
            "Authorization": f"Bearer {TOKEN}"
        }
    )
    
    print(f"状态码: {response.status_code}")
    print(f"响应: {json.dumps(response.json(), ensure_ascii=False, indent=2)}")


def test_notification_dialog():
    """测试通知对话生成接口"""
    print("\n=== 测试通知对话生成接口 ===")
    
    # 这里需要一个实际的notification_id
    notification_id = "test-notification-id"
    
    response = requests.post(
        f"{BASE_URL}/api/v1/soar/notification/dialog",
        json={
            "notification_id": notification_id,
            "user_question": "这个告警的严重程度如何？"
        },
        headers={
            "Content-Type": "application/json",
            "Authorization": f"Bearer {TOKEN}"
        }
    )
    
    print(f"状态码: {response.status_code}")
    print(f"响应: {json.dumps(response.json(), ensure_ascii=False, indent=2)}")


def test_app_function():
    """测试APP功能"""
    print("\n=== 测试APP功能 ===")
    
    # 模拟APP调用
    import sys
    import os
    sys.path.append(os.path.join(os.getcwd(), 'apps', 'notification', 'main'))
    
    try:
        from run import process_message
        import asyncio
        
        # 测试数据
        test_data = {
            "script_type": "s60000_pending",
            "result_data": '{"processed_count": 5}',
            "execution_id": "test-execution-123"
        }
        
        # 运行异步函数
        result = asyncio.run(process_message(**test_data))
        print(f"APP执行结果: {json.dumps(result, ensure_ascii=False, indent=2)}")
        
    except Exception as e:
        print(f"APP测试失败: {str(e)}")


if __name__ == "__main__":
    print("消息通知功能测试")
    print("注意：请确保服务已启动并替换正确的TOKEN")
    
    # 测试接口功能
    try:
        test_notification_process()
        test_notification_list()
        # test_notification_status()  # 需要实际的notification_id
        # test_notification_dialog()  # 需要实际的notification_id
    except Exception as e:
        print(f"接口测试失败: {str(e)}")
    
    # 测试APP功能
    test_app_function()
