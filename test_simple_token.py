#!/usr/bin/env python
# encoding:utf-8
"""
简化的TOKEN获取系统测试脚本
"""
import sys
import os
import asyncio

# 添加项目路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'apps', 'notification', 'main'))

def test_token_functions():
    """测试TOKEN相关函数"""
    print("=== 测试TOKEN获取系统 ===")
    
    try:
        from run import (
            get_system_token,
            verify_token_in_redis,
            generate_new_token
        )
        
        # 测试TOKEN获取
        print("\n1. 测试TOKEN获取")
        token = get_system_token()
        if token:
            print(f"获取到TOKEN: {token[:20]}...")
            
            # 测试TOKEN验证
            print("\n2. 测试TOKEN验证")
            is_valid = verify_token_in_redis(token)
            print(f"TOKEN验证结果: {'有效' if is_valid else '无效'}")
        else:
            print("TOKEN获取失败")
        
    except ImportError as e:
        print(f"导入模块失败: {e}")
    except Exception as e:
        print(f"测试过程中出现异常: {e}")


def test_app_function():
    """测试APP主函数"""
    print("\n=== 测试APP主函数 ===")
    
    try:
        from run import process_message
        
        # 测试数据
        test_data = {
            "script_type": "s60000_pending",
            "result_data": '{"processed_count": 5}',
            "execution_id": "test-execution-123"
        }
        
        print(f"测试参数: {test_data}")
        
        # 运行异步函数
        result = asyncio.run(process_message(**test_data))
        print(f"APP执行结果: {result}")
        
    except Exception as e:
        print(f"APP测试失败: {e}")


def test_database_user():
    """测试数据库用户"""
    print("\n=== 测试数据库用户 ===")
    
    try:
        # 添加项目根目录到路径
        current_dir = os.path.dirname(os.path.abspath(__file__))
        project_root = current_dir
        sys.path.insert(0, project_root)
        
        from core.model import Users
        
        # 查询ID为1的用户
        user = Users.where('id', 1).first()
        if user:
            print(f"找到系统用户:")
            print(f"  ID: {user.id}")
            print(f"  账号: {user.account}")
            print(f"  昵称: {user.nick_name}")
            print(f"  邮箱: {user.email}")
            print(f"  状态: {user.status}")
            print(f"  TOKEN: {user.token[:20] if user.token else 'None'}...")
        else:
            print("未找到ID为1的用户")
            print("建议创建系统用户:")
            print("INSERT INTO w5_users (id, account, passwd, nick_name, email, status, create_time, update_time)")
            print("VALUES (1, 'system', MD5('system123'), '系统用户', '<EMAIL>', 0, NOW(), NOW());")
        
    except Exception as e:
        print(f"数据库用户测试失败: {e}")


def test_redis_connection():
    """测试Redis连接"""
    print("\n=== 测试Redis连接 ===")
    
    try:
        # 添加项目根目录到路径
        current_dir = os.path.dirname(os.path.abspath(__file__))
        project_root = current_dir
        sys.path.insert(0, project_root)
        
        from core import redis
        
        if redis:
            # 测试连接
            test_key = "test_notification_key"
            test_value = "test_value"
            
            redis.set(test_key, test_value, ex=60)
            retrieved_value = redis.get(test_key)
            
            if retrieved_value == test_value:
                print("Redis连接正常")
                redis.delete(test_key)
            else:
                print("Redis连接异常")
        else:
            print("Redis模块未导入")
        
    except Exception as e:
        print(f"Redis连接测试失败: {e}")


if __name__ == "__main__":
    print("简化的消息通知TOKEN系统测试")
    print("=" * 50)
    
    # 测试数据库用户
    test_database_user()
    
    # 测试Redis连接
    test_redis_connection()
    
    # 测试TOKEN功能
    test_token_functions()
    
    # 测试APP功能
    test_app_function()
    
    print("\n测试完成")
