# 消息通知功能体设计文档

## 1. 概述

### 1.1 功能描述
基于W5 SOAR平台的消息通知功能体，通过剧本执行结果自动触发消息处理，实现前端弹窗通知和智能对话生成。

### 1.2 设计原则
- 模块化设计，符合现有项目架构
- 统一的接口规范和响应格式
- 可扩展的通知类型和处理逻辑
- 复用现有基础设施和工具类

### 1.3 技术架构
```
业务剧本 → 消息处理APP → 消息处理接口 → 前端弹窗通知 → 对话生成接口
```

## 2. 模块结构设计

### 2.1 目录结构
```
core/view_soar/notification/
├── __init__.py
└── view.py                 # 主要接口实现（包含消息处理逻辑和通知处理逻辑）

apps/notification/
├── app.json               # 消息处理APP配置
├── icon.png              # APP图标
├── readme.md             # 说明文档
└── main/
    ├── __init__.py
    └── run.py            # APP核心逻辑
```

### 2.2 路由注册
在`core/__init__.py`中的`soar_route_list`定义时直接加入：
```python
soar_route_list = [r_login, r_user, r_type, r_variablen, r_system, r_apps, r_workflow, r_logs, r_dashboard, r_api,
                  r_report, r_timer, r_audit, r_alert, r_analysis, r_rectification, r_notification]
```

## 3. 接口设计详情

### 3.1 消息处理接口

#### 接口信息
- **路径**: `/api/v1/soar/notification/process`
- **方法**: `POST`
- **认证**: 需要TOKEN认证
- **描述**: 处理剧本执行结果，判断是否需要发送通知

#### 请求参数
```json
{
  "script_type": "string",     // 剧本类型: "s60000_pending", "email", "qianxin_ids", "nsfocus_scan", "s60000_feedback"
  "result": "object",          // 业务APP的JSON返回结果
  "execution_id": "string",    // 剧本执行ID（可选）
  "timestamp": "string"        // 执行时间戳（可选，默认当前时间）
}
```

#### 响应格式
```json
{
  "code": 0,
  "msg": "Success",
  "data": {
    "need_notification": true,        // 是否需要通知
    "notification_id": "string",      // 通知ID
    "notification_type": "string",    // 通知类型
    "count": 10,                     // 数量
    "source": "string",              // 来源系统
    "title": "string",               // 通知标题
    "created_at": "string"           // 创建时间
  }
}
```

#### 业务逻辑
1. 根据`script_type`解析对应的判断字段
2. 检查数量字段是否非零
3. 如果需要通知，生成通知记录并存储到Redis
4. 构造通知标题和相关信息

### 3.2 通知列表查询接口

#### 接口信息
- **路径**: `/api/v1/soar/notification/list`
- **方法**: `POST`
- **认证**: 需要TOKEN认证
- **描述**: 获取用户的通知列表

#### 请求参数
```json
{
  "page": 1,
  "limit": 20,
  "status": "unread"  // "all", "read", "unread"
}
```

#### 响应格式
```json
{
  "code": 0,
  "msg": "Success",
  "data": {
    "total": 50,
    "page": 1,
    "limit": 20,
    "notifications": [
      {
        "notification_id": "string",
        "title": "string",
        "source": "string",
        "notification_type": "string",
        "count": 10,
        "status": "unread",
        "created_at": "string",
        "result_data": "object"
      }
    ]
  }
}
```

### 3.3 通知状态更新接口

#### 接口信息
- **路径**: `/api/v1/soar/notification/status`
- **方法**: `PUT`
- **认证**: 需要TOKEN认证
- **描述**: 更新通知状态（已读/未读）

#### 请求参数
```json
{
  "notification_id": "string",
  "status": "read"  // "read" 或 "unread"
}
```

#### 响应格式
```json
{
  "code": 0,
  "msg": "Success",
  "data": {
    "notification_id": "string",
    "status": "read",
    "updated_at": "string"
  }
}
```

### 3.4 通知对话生成接口

#### 接口信息
- **路径**: `/api/v1/soar/notification/dialog`
- **方法**: `POST`
- **认证**: 需要TOKEN认证
- **描述**: 基于通知内容生成智能对话

#### 请求参数
```json
{
  "notification_id": "string",
  "user_question": "string"  // 用户问题（可选）
}
```

#### 响应格式
```json
{
  "code": 0,
  "msg": "Success",
  "data": {
    "dialog_id": "string",
    "conversation": [
      {
        "role": "system",
        "content": "string",
        "timestamp": "string"
      },
      {
        "role": "user", 
        "content": "string",
        "timestamp": "string"
      },
      {
        "role": "assistant",
        "content": "string",
        "timestamp": "string"
      }
    ],
    "context": {
      "notification_info": "object",
      "result_data": "object"
    }
  }
}
```

## 4. 消息处理APP设计

### 4.1 APP配置文件 (apps/notification/app.json)
```json
{
  "identification": "w5soar",
  "is_public": true,
  "name": "消息通知处理",
  "version": "1.0",
  "description": "处理剧本执行结果并发送通知",
  "type": "消息通知",
  "action": [
    {
      "name": "处理消息",
      "func": "process_message"
    }
  ],
  "args": {
    "process_message": [
      {
        "key": "script_type",
        "type": "select",
        "required": true,
        "options": ["s60000_pending", "email", "qianxin_ids", "nsfocus_scan", "s60000_feedback"]
      },
      {
        "key": "result_data",
        "type": "text",
        "required": true
      }
    ]
  }
}
```

### 4.2 APP核心逻辑 (apps/notification/main/run.py)
```python
async def process_message(script_type, result_data):
    """
    处理消息通知
    """
    try:
        # 调用消息处理接口
        response = requests.post(
            "http://localhost:8888/api/v1/soar/notification/process",
            json={
                "script_type": script_type,
                "result": json.loads(result_data)
            },
            headers={"Authorization": "Bearer " + get_system_token()}
        )
        
        if response.status_code == 200:
            return {"status": 0, "result": response.json()}
        else:
            return {"status": 1, "result": "通知处理失败"}
            
    except Exception as e:
        return {"status": 1, "result": f"处理异常: {str(e)}"}
```

## 5. 数据结构设计

### 5.1 Redis存储结构
```
# 通知队列
notification:queue:{user_id} -> List[notification_id]

# 通知详情
notification:detail:{notification_id} -> Hash{
  "title": "string",
  "source": "string", 
  "notification_type": "string",
  "count": "number",
  "status": "unread|read",
  "created_at": "timestamp",
  "result_data": "json_string",
  "user_id": "string"
}

# 用户未读计数
notification:unread:{user_id} -> Number
```

### 5.2 判断条件映射
```python
SCRIPT_CONDITIONS = {
    "s60000_pending": {
        "field": "processed_count",
        "type": "待处理数",
        "source": "S60000系统"
    },
    "email": {
        "field": "mails_count", 
        "type": "未读邮件",
        "source": "邮件系统"
    },
    "qianxin_ids": {
        "field": "total",
        "type": "危急告警",
        "source": "奇安信天眼"
    },
    "nsfocus_scan": {
        "fields": ["total_tasks", "total_vulns"],
        "types": ["扫描任务", "安全漏洞"],
        "source": "绿盟漏扫"
    },
    "s60000_feedback": {
        "field": "ddl_entry_count",
        "type": "反馈截止",
        "source": "S60000系统"
    }
}
```

## 6. 前端集成方案

### 6.1 WebSocket通知推送
复用现有的WebSocket连接，在通知生成时推送到前端：
```javascript
// 前端监听通知事件
socket.on('notification', function(data) {
    showNotificationPopup(data);
});
```

### 6.2 通知弹窗组件
前端需要实现通知弹窗组件，包含：
- 通知标题显示
- 点击查看详情
- 标记已读功能
- 生成对话入口

## 7. 部署和配置

### 7.1 配置文件更新 (config.ini)
```ini
[notification]
# 通知保留时间（天）
retention_days = 30
# 最大通知数量
max_notifications = 1000
# 是否启用实时推送
enable_realtime = true
```

### 7.2 数据库初始化
如需持久化存储，可创建通知历史表：
```sql
CREATE TABLE notification_history (
    id VARCHAR(36) PRIMARY KEY,
    user_id INT NOT NULL,
    title VARCHAR(255) NOT NULL,
    source VARCHAR(100) NOT NULL,
    notification_type VARCHAR(100) NOT NULL,
    count INT NOT NULL,
    status ENUM('read', 'unread') DEFAULT 'unread',
    result_data JSON,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

## 8. 测试用例

### 8.1 消息处理测试
```bash
# 测试S60000待处理通知
curl -X POST http://localhost:8888/api/v1/soar/notification/process \
-H "Content-Type: application/json" \
-H "Authorization: Bearer YOUR_TOKEN" \
-d '{
  "script_type": "s60000_pending",
  "result": {"processed_count": 5}
}'
```

### 8.2 通知查询测试
```bash
# 获取通知列表
curl -X POST http://localhost:8888/api/v1/soar/notification/list \
-H "Content-Type: application/json" \
-H "Authorization: Bearer YOUR_TOKEN" \
-d '{
  "page": 1,
  "limit": 10,
  "status": "unread"
}'
```

## 9. 扩展性考虑

### 9.1 新增剧本类型
只需在`SCRIPT_CONDITIONS`中添加新的判断条件配置即可。

### 9.2 多种通知渠道
可以扩展支持邮件、短信、企业微信等多种通知方式。

### 9.3 通知规则引擎
未来可以实现更复杂的通知规则配置，支持条件组合和自定义阈值。

这个设计文档提供了完整的接口规范和实现方案，可以直接用于开发实施。
